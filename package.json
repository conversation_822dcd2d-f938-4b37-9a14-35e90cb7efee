{"name": "kidaro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@clerk/nextjs": "^6.31.4", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-tooltip": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "hamburger-react": "^2.5.2", "lucide-react": "^0.542.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "sass": "^1.91.0", "slugify": "^1.6.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}