{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/Users/<USER>/Sites/kidaro/app/globals.scss"], "sourcesContent": ["@use 'sass:color';\n\n// SCSS Variables for custom styles\n$primary-color: #3b82f6;\n$secondary-color: #64748b;\n$success-color: #10b981;\n$error-color: #ef4444;\n$warning-color: #f59e0b;\n\n// Mixins for common patterns\n@mixin flex-center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n@mixin button-base {\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  font-weight: 500;\n  transition: all 0.2s ease-in-out;\n  cursor: pointer;\n  border: none;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n}\n\n// Custom component styles\n.test {\n  background: $error-color;\n  height: 100px;\n  width: 100px;\n  @include flex-center;\n  border-radius: 0.5rem;\n\n  &:hover {\n    background: color.adjust($error-color, $lightness: -10%);\n    transform: scale(1.05);\n  }\n}\n"], "names": [], "mappings": "AA8BA;;;;;;;;;;AAOE"}}]}