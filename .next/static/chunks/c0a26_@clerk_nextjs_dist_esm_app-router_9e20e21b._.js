(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/.pnpm/@clerk+nextjs@6.31.4_next@15.5.0_react-dom@19.1.0_react@19.1.0/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/c0a26_@clerk_nextjs_dist_esm_app-router_b385c2b5._.js",
  "static/chunks/c0a26_@clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_760766d8.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@clerk+nextjs@6.31.4_next@15.5.0_react-dom@19.1.0_react@19.1.0/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/.pnpm/@clerk+nextjs@6.31.4_next@15.5.0_react-dom@19.1.0_react@19.1.0/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/c0a26_@clerk_nextjs_dist_esm_app-router_0d633462._.js",
  "static/chunks/c0a26_@clerk_nextjs_dist_esm_app-router_keyless-actions_2b7077a6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@clerk+nextjs@6.31.4_next@15.5.0_react-dom@19.1.0_react@19.1.0/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-client] (ecmascript)");
    });
});
}),
]);