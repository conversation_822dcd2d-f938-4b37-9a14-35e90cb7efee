{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/error.ts"], "sourcesContent": ["import type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ClerkAPIResponseErro<PERSON> as ClerkAPIResponseErrorInterface,\n} from '@clerk/types';\n\n/**\n * Checks if the provided error object is an unauthorized error.\n *\n * @internal\n *\n * @deprecated This is no longer used, and will be removed in the next major version.\n */\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\n/**\n * Checks if the provided error object is a captcha error.\n *\n * @internal\n */\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\n/**\n * Checks if the provided error is a 4xx error.\n *\n * @internal\n */\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\n/**\n * Checks if the provided error is a network error.\n *\n * @internal\n */\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\n/**\n * Options for creating a ClerkAPIResponseError.\n *\n * @internal\n */\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\n/**\n * Checks if the provided error is either a ClerkAPIResponseError, a ClerkRuntimeError, or a MetamaskError.\n *\n * @internal\n */\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\n/**\n * Checks if the provided error is a ClerkAPIResponseError.\n *\n * @internal\n */\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return err && 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param err - The error object to check.\n * @returns True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\n/**\n * Checks if the provided error is a Clerk runtime error indicating a reverification was cancelled.\n *\n * @internal\n */\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\n/**\n * Checks if the provided error is a Metamask error.\n *\n * @internal\n */\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\n/**\n * Checks if the provided error is clerk api response error indicating a user is locked.\n *\n * @internal\n */\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\n/**\n * Checks if the provided error is a clerk api response error indicating a password was pwned.\n *\n * @internal\n */\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\n/**\n * Parses an array of ClerkAPIErrorJSON objects into an array of ClerkAPIError objects.\n *\n * @internal\n */\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\n/**\n * Parses a ClerkAPIErrorJSON object into a ClerkAPIError object.\n *\n * @internal\n */\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n      isPlanUpgradePossible: error?.meta?.is_plan_upgrade_possible,\n    },\n  };\n}\n\n/**\n * Converts a ClerkAPIError object into a ClerkAPIErrorJSON object.\n *\n * @internal\n */\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n      is_plan_upgrade_possible: error?.meta?.isPlanUpgradePossible,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error implements ClerkAPIResponseErrorInterface {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n *\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\n/**\n * Checks if the provided error is an EmailLinkError.\n *\n * @internal\n */\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\n/**\n * Builds an error thrower.\n *\n * @internal\n */\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  /**\n   * Builds a message from a raw message and replacements.\n   *\n   * @internal\n   */\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n"], "names": ["packageName", "customMessages"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaO,SAAS,oBAAoB,CAAA,EAAiB;IACnD,MAAM,SAAS,GAAG;IAClB,MAAM,OAAO,GAAG,QAAA,CAAS,CAAC,CAAA,EAAG;IAC7B,OAAO,SAAS,4BAA4B,WAAW;AACzD;AAOO,SAAS,eAAe,CAAA,EAAmC;IAChE,OAAO;QAAC;QAAmB;QAAuB,uBAAuB;KAAA,CAAE,QAAA,CAAS,EAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAI;AACtG;AAOO,SAAS,WAAW,CAAA,EAAiB;IAC1C,MAAM,SAAS,GAAG;IAClB,OAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;AAC/C;AAOO,SAAS,eAAe,CAAA,EAAiB;IAE9C,MAAM,UAAA,CAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,EAAA,IAAM,EAAA,EAAI,WAAA,CAAY,EAAE,OAAA,CAAQ,QAAQ,EAAE;IAChF,OAAO,QAAQ,QAAA,CAAS,cAAc;AACxC;AA2BO,SAAS,aAAa,KAAA,EAAgF;IAC3G,OAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAOO,SAAS,wBAAwB,GAAA,EAAwC;IAC9E,OAAO,OAAO,gBAAgB;AAChC;AAkBO,SAAS,oBAAoB,GAAA,EAAoC;IACtE,OAAO,uBAAuB;AAChC;AAOO,SAAS,+BAA+B,GAAA,EAAU;IACvD,OAAO,oBAAoB,GAAG,KAAK,IAAI,IAAA,KAAS;AAClD;AAOO,SAAS,gBAAgB,GAAA,EAAgC;IAC9D,OAAO,UAAU,OAAO;QAAC;QAAM;QAAO,KAAK;KAAA,CAAE,QAAA,CAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAOO,SAAS,kBAAkB,GAAA,EAAU;IAC1C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAOO,SAAS,qBAAqB,GAAA,EAAU;IAC7C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAOO,SAAS,YAAY,OAA4B,CAAC,CAAA,EAAoB;IAC3E,OAAO,KAAK,MAAA,GAAS,IAAI,KAAK,GAAA,CAAI,UAAU,IAAI,CAAC,CAAA;AACnD;AAOO,SAAS,WAAW,KAAA,EAAyC;IAClE,OAAO;QACL,MAAM,MAAM,IAAA;QACZ,SAAS,MAAM,OAAA;QACf,aAAa,MAAM,YAAA;QACnB,MAAM;YACJ,WAAW,OAAO,MAAM;YACxB,WAAW,OAAO,MAAM;YACxB,gBAAgB,OAAO,MAAM;YAC7B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;YACrB,MAAM,OAAO,MAAM;YACnB,uBAAuB,OAAO,MAAM;QACtC;IACF;AACF;AAOO,SAAS,YAAY,KAAA,EAAgD;IAC1E,OAAO;QACL,MAAM,OAAO,QAAQ;QACrB,SAAS,OAAO,WAAW;QAC3B,cAAc,OAAO;QACrB,MAAM;YACJ,YAAY,OAAO,MAAM;YACzB,YAAY,OAAO,MAAM;YACzB,iBAAiB,OAAO,MAAM;YAC9B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;YACrB,MAAM,OAAO,MAAM;YACnB,0BAA0B,OAAO,MAAM;QACzC;IACF;AACF;AAEO,IAAM,wBAAN,MAAM,+BAA8B,MAAgD;IAUzF,YAAY,OAAA,EAAiB,EAAE,IAAA,EAAM,MAAA,EAAQ,YAAA,EAAc,UAAA,CAAW,CAAA,CAA4B;QAChG,KAAA,CAAM,OAAO;QAYf,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,IAAI,UAAU,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,CAAA;OAAA,EAAY,IAAA,CAAK,MAAM,CAAA;mBAAA,EAAwB,IAAA,CAAK,MAAA,CAAO,GAAA,CAC9G,CAAA,IAAK,KAAK,SAAA,CAAU,CAAC,IACtB;YAED,IAAI,IAAA,CAAK,YAAA,EAAc;gBACrB,WAAW,CAAA;gBAAA,EAAqB,IAAA,CAAK,YAAY,EAAA;YACnD;YAEA,OAAO;QACT;QApBE,OAAO,cAAA,CAAe,IAAA,EAAM,uBAAsB,SAAS;QAE3D,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,MAAA,GAAS,YAAY,IAAI;IAChC;AAaF;AAUO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;IAiB3C,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqB;QACvD,MAAM,SAAS;QACf,MAAM,QAAQ,IAAI,OAAO,OAAO,OAAA,CAAQ,KAAK,MAAM,GAAG,GAAG;QACzD,MAAM,YAAY,QAAQ,OAAA,CAAQ,OAAO,EAAE;QAC3C,MAAM,WAAW,GAAG,MAAM,CAAA,CAAA,EAAI,UAAU,IAAA,CAAK,CAAC,CAAA;;OAAA,EAAc,IAAI,CAAA;AAAA,CAAA;QAChE,KAAA,CAAM,QAAQ;QAehB;;;;KAAA,GAAA,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,OAAO,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,EAAA;QAChD;QAfE,OAAO,cAAA,CAAe,IAAA,EAAM,mBAAkB,SAAS;QAEvD,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,IAAA,GAAO;IACd;AAUF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;IAGxC,YAAY,IAAA,CAAc;QACxB,KAAA,CAAM,IAAI;QACV,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,cAAA,CAAe,IAAA,EAAM,gBAAe,SAAS;IACtD;AACF;AAOO,SAAS,iBAAiB,GAAA,EAAmC;IAClE,OAAO,IAAI,IAAA,KAAS;AACtB;AAOO,IAAM,qBAAqB;IAChC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEO,IAAM,2BAA2B;IACtC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEA,IAAM,kBAAkB,OAAO,MAAA,CAAO;IACpC,6BAA6B,CAAA,gJAAA,CAAA;IAC7B,mCAAmC,CAAA,uJAAA,CAAA;IACnC,mCAAmC,CAAA,sGAAA,CAAA;IACnC,8BAA8B,CAAA,iGAAA,CAAA;IAC9B,sBAAsB,CAAA,gIAAA,CAAA;AACxB,CAAC;AAoCM,SAAS,kBAAkB,EAAE,WAAA,EAAa,cAAA,CAAe,CAAA,EAAsC;IACpG,IAAI,MAAM;IAOV,SAAS,aAAa,UAAA,EAAoB,YAAA,EAAgD;QACxF,IAAI,CAAC,cAAc;YACjB,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,UAAU,EAAA;QAC9B;QAEA,IAAI,MAAM;QACV,MAAM,UAAU,WAAW,QAAA,CAAS,uBAAuB;QAE3D,KAAA,MAAW,SAAS,QAAS;YAC3B,MAAM,cAAA,CAAe,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC,CAAA,IAAK,EAAA,EAAI,QAAA,CAAS;YAC5D,MAAM,IAAI,OAAA,CAAQ,CAAA,EAAA,EAAK,KAAA,CAAM,CAAC,CAAC,CAAA,EAAA,CAAA,EAAM,WAAW;QAClD;QAEA,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,GAAG,EAAA;IACvB;IAEA,MAAM,WAAW;QACf,GAAG,eAAA;QACH,GAAG,cAAA;IACL;IAEA,OAAO;QACL,gBAAe,EAAE,aAAAA,YAAAA,CAAY,CAAA,EAAsC;YACjE,IAAI,OAAOA,iBAAgB,UAAU;gBACnC,MAAMA;YACR;YACA,OAAO,IAAA;QACT;QAEA,aAAY,EAAE,gBAAAC,eAAAA,CAAe,CAAA,EAAsC;YACjE,OAAO,MAAA,CAAO,UAAUA,mBAAkB,CAAC,CAAC;YAC5C,OAAO,IAAA;QACT;QAEA,iCAAgC,MAAA,EAAiC;YAC/D,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAA,EAAmC,MAAM,CAAC;QAClF;QAEA,sBAAqB,MAAA,EAAiC;YACpD,MAAM,IAAI,MAAM,aAAa,SAAS,2BAAA,EAA6B,MAAM,CAAC;QAC5E;QAEA,kCAAyC;YACvC,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;QAC1E;QAEA,6BAAoC;YAClC,MAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;QACrE;QAEA,gCAA+B,MAAA,EAAoC;YACjE,MAAM,IAAI,MAAM,aAAa,SAAS,oBAAA,EAAsB,MAAM,CAAC;QACrE;QAEA,OAAM,OAAA,EAAwB;YAC5B,MAAM,IAAI,MAAM,aAAa,OAAO,CAAC;QACvC;IACF;AACF;AAgBO,IAAM,qBAAN,cAAiC,kBAAkB;IAMxD,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqC;QACvE,KAAA,CAAM,SAAS;YAAE;QAAK,CAAC;QACvB,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/authorization.ts"], "sourcesContent": ["import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => value.replace(/^(org:)*/, 'org:');\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return prefixWithOrg(orgRole) === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages.\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n"], "names": ["config"], "mappings": ";;;;;;;;;;;AA4CA,IAAM,mBAAkC;IACtC,YAAY;QACV,cAAc;QACd,OAAO;IACT;IACA,QAAQ;QACN,cAAc;QACd,OAAO;IACT;IACA,UAAU;QACR,cAAc;QACd,OAAO;IACT;IACA,KAAK;QACH,cAAc;QACd,OAAO;IACT;AACF;AAEA,IAAM,iBAAiB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAgB;IAAiB,cAAc;CAAC;AAE1G,IAAM,gBAAgB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAc;IAAU;IAAY,KAAK;CAAC;AAGnG,IAAM,gBAAgB,CAAC,SAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,IAAM,eAAe,CAAC,QAAe,eAAe,GAAA,CAAI,KAAK;AAC7D,IAAM,0BAA0B,CAAC,OAAc,cAAc,GAAA,CAAI,IAAI;AAErE,IAAM,gBAAgB,CAAC,QAAkB,MAAM,OAAA,CAAQ,YAAY,MAAM;AAOzE,IAAM,wBAA+C,CAAC,QAAQ,YAAY;IACxE,MAAM,EAAE,KAAA,EAAO,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI;IAC3C,IAAI,CAAC,OAAO,IAAA,IAAQ,CAAC,OAAO,UAAA,EAAY;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;QACzC,OAAO;IACT;IAEA,IAAI,OAAO,UAAA,EAAY;QACrB,OAAO,eAAe,QAAA,CAAS,cAAc,OAAO,UAAU,CAAC;IACjE;IAEA,IAAI,OAAO,IAAA,EAAM;QACf,OAAO,cAAc,OAAO,MAAM,cAAc,OAAO,IAAI;IAC7D;IACA,OAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,OAAe,kBAA0B;IACtE,MAAM,EAAE,KAAK,WAAA,EAAa,MAAM,YAAA,CAAa,CAAA,GAAI,aAAa,KAAK;IACnE,MAAM,CAAC,OAAO,GAAG,CAAA,GAAI,cAAc,KAAA,CAAM,GAAG;IAC5C,MAAM,KAAK,OAAO;IAElB,IAAI,UAAU,OAAO;QACnB,OAAO,YAAY,QAAA,CAAS,EAAE;IAChC,OAAA,IAAW,UAAU,QAAQ;QAC3B,OAAO,aAAa,QAAA,CAAS,EAAE;IACjC,OAAO;QAEL,OAAO,CAAC;eAAG,aAAa;eAAG,YAAY;SAAA,CAAE,QAAA,CAAS,EAAE;IACtD;AACF;AAEA,IAAM,4BAAuD,CAAC,QAAQ,YAAY;IAChF,MAAM,EAAE,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;IAE5B,IAAI,OAAO,OAAA,IAAW,UAAU;QAC9B,OAAO,sBAAsB,UAAU,OAAO,OAAO;IACvD;IAEA,IAAI,OAAO,IAAA,IAAQ,OAAO;QACxB,OAAO,sBAAsB,OAAO,OAAO,IAAI;IACjD;IACA,OAAO;AACT;AAEA,IAAM,eAAe,CAAC,QAAmC;IACvD,MAAM,WAAW,MAAM,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC,IAAI,CAAC,CAAA;IAG5D,OAAO;QACL,KAAK,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;QACjF,MAAM,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IACpF;AACF;AAEA,IAAM,+BAA+B,CAAC,WAAoD;IACxF,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,wBAAwB,CAACA,YAAiC;QAC9D,IAAI,OAAOA,YAAW,UAAU;YAC9B,OAAO,gBAAA,CAAiBA,OAAM,CAAA;QAChC;QACA,OAAOA;IACT;IAEA,MAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;IACvF,MAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;IAE/F,IAAI,sBAAsB,oBAAoB;QAC5C,OAAO,sBAAsB,IAAA,CAAK,MAAM,MAAM;IAChD;IAEA,OAAO;AACT;AAQA,IAAM,mCAAqE,CAAC,QAAQ,EAAE,qBAAA,CAAsB,CAAA,KAAM;IAChH,IAAI,CAAC,OAAO,cAAA,IAAkB,CAAC,uBAAuB;QACpD,OAAO;IACT;IAEA,MAAM,wBAAwB,6BAA6B,OAAO,cAAc;IAChF,IAAI,CAAC,uBAAuB;QAC1B,OAAO;IACT;IAEA,MAAM,EAAE,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI,sBAAsB;IACtD,MAAM,CAAC,YAAY,UAAU,CAAA,GAAI;IAIjC,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IACvE,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IAEvE,OAAQ,OAAO;QACb,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB;QAC9C,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB,kBAAkB;IAClE;AACF;AAQA,IAAM,2BAA2B,CAAC,YAA2E;IAC3G,OAAO,CAAC,WAAoB;QAC1B,IAAI,CAAC,QAAQ,MAAA,EAAQ;YACnB,OAAO;QACT;QAEA,MAAM,uBAAuB,0BAA0B,QAAQ,OAAO;QACtE,MAAM,mBAAmB,sBAAsB,QAAQ,OAAO;QAC9D,MAAM,8BAA8B,iCAAiC,QAAQ,OAAO;QAEpF,IAAI;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI,GAAG;YACjG,OAAO;gBAAC,wBAAwB;gBAAkB,2BAA2B;aAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI;QACrG;QAEA,OAAO;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,KAAA,CAAM,CAAA,IAAK,MAAM,IAAI;IACtG;AACF;AAyBA,IAAM,mBAAmB,CAAC,EACxB,YAAY,EACV,SAAA,EACA,aAAA,EACA,MAAA,EACA,KAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,EACA,GAAA,EACA,aAAA,EACF,EACA,SAAS,EAAE,0BAA0B,IAAA,CAAK,CAAA,EAC5C,KAAmD;IACjD,IAAI,cAAc,KAAA,KAAa,WAAW,KAAA,GAAW;QACnD,OAAO;YACL,UAAU;YACV,YAAY,KAAA;YACZ;YACA,eAAe,KAAA;YACf;YACA,OAAO,KAAA;YACP,OAAO,KAAA;YACP,SAAS,KAAA;YACT,SAAS,KAAA;YACT,KAAK,KAAA;YACL;YACA;QACF;IACF;IAEA,IAAI,cAAc,QAAQ,WAAW,MAAM;QACzC,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,2BAA2B,kBAAkB,WAAW;QAC1D,OAAO;YACL,UAAU;YACV,YAAY;YACZ,WAAW;YACX,QAAQ;YACR,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;QACtE,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB;YACA;YACA,SAAS,WAAW;YACpB;YACA;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;QACxD,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB,OAAO;YACP,SAAS;YACT,SAAS;YACT;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/underscore.ts"], "sourcesContent": ["/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMO,IAAM,aAAa,CAAC,UAA4B;IAErD,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO;IACT;IACA,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO,KAAA,CAAM,CAAC,CAAA;IAChB;IACA,IAAI,WAAW,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,IAAI;IAC3C,YAAY,CAAA,KAAA,EAAQ,MAAM,KAAA,CAAM,CAAA,CAAE,CAAC,EAAA;IACnC,OAAO;AACT;AAEA,IAAM,sBACJ;AAOK,SAAS,cAAc,GAAA,EAAyC;IACrE,OAAO,oBAAoB,IAAA,CAAK,OAAO,EAAE;AAC3C;AAaO,SAAS,SAAS,GAAA,EAAwC;IAC/D,MAAM,IAAI,OAAO;IACjB,OAAO,EAAE,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,EAAE,KAAA,CAAM,CAAC;AAC9C;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,gBAAgB,CAAA,QAAS,MAAM,WAAA,CAAY,EAAE,OAAA,CAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,UAAU,CAAA,SAAU,CAAA,CAAA,EAAI,OAAO,WAAA,CAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;IACtD,MAAM,gBAAgB,CAAC,QAAkB;QACvC,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,IAAI,MAAM,OAAA,CAAQ,GAAG,GAAG;YACtB,OAAO,IAAI,GAAA,CAAI,CAAA,OAAM;gBACnB,IAAI,OAAO,OAAO,YAAY,MAAM,OAAA,CAAQ,EAAE,GAAG;oBAC/C,OAAO,cAAc,EAAE;gBACzB;gBACA,OAAO;YACT,CAAC;QACH;QAEA,MAAM,OAAO;YAAE,GAAG,GAAA;QAAI;QACtB,MAAM,OAAO,OAAO,IAAA,CAAK,IAAI;QAC7B,KAAA,MAAW,WAAW,KAAM;YAC1B,MAAM,UAAU,UAAU,QAAQ,QAAA,CAAS,CAAC;YAC5C,IAAI,YAAY,SAAS;gBACvB,IAAA,CAAK,OAAO,CAAA,GAAI,IAAA,CAAK,OAAO,CAAA;gBAC5B,OAAO,IAAA,CAAK,OAAO,CAAA;YACrB;YACA,IAAI,OAAO,IAAA,CAAK,OAAO,CAAA,KAAM,UAAU;gBACrC,IAAA,CAAK,OAAO,CAAA,GAAI,cAAc,IAAA,CAAK,OAAO,CAAC;YAC7C;QACF;QACA,OAAO;IACT;IAEA,OAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,SAAS,SAAS,KAAA,EAAyB;IAEhD,IAAI,OAAO,UAAU,CAAA,OAAA,CAAA,EAAW;QAC9B,OAAO;IACT;IAGA,IAAI,UAAU,KAAA,KAAa,UAAU,MAAM;QACzC,OAAO;IACT;IAGA,IAAI,OAAO,UAAU,CAAA,MAAA,CAAA,EAAU;QAC7B,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,IAAA,CAAA,EAAQ;YAClC,OAAO;QACT;QAEA,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,KAAA,CAAA,EAAS;YACnC,OAAO;QACT;IACF;IAGA,MAAM,SAAS,SAAS,OAAiB,EAAE;IAC3C,IAAI,MAAM,MAAM,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,SAAS,GAAG;QACd,OAAO;IACT;IAGA,OAAO;AACT;AAKO,SAAS,sBAAwC,GAAA,EAAoB;IAC1E,OAAO,OAAO,OAAA,CAAQ,GAAG,EAAE,MAAA,CAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAA,KAAM;QACvD,IAAI,UAAU,KAAA,GAAW;YACvB,GAAA,CAAI,GAAc,CAAA,GAAI;QACxB;QACA,OAAO;IACT,GAAG,CAAC,CAAe;AACrB", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/isomorphicAtob.ts"], "sourcesContent": ["/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;;AAIO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,wCAAW,eAAe,yDAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,yDAAO,MAAA,CAAO,MAAM,QAAQ,EAAE,QAAA,CAAS;IACpD;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/isomorphicBtoa.ts"], "sourcesContent": ["export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;;AAAO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,wCAAW,eAAe,yDAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,yDAAO,MAAA,CAAO,IAAI,EAAE,QAAA,CAAS,QAAQ;IAClD;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/constants.ts"], "sourcesContent": ["export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,+BAA+B;IAAC;IAAY;IAAiB,eAAe;CAAA;AAClF,IAAM,gCAAgC;IAAC;IAAiB;IAAsB,wBAAwB;CAAA;AACtG,IAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AACO,IAAM,qBAAqB;IAAC;IAAY;IAAgB;IAAiB,wBAAwB;CAAA;AACjG,IAAM,uBAAuB;IAAC,oBAAoB;CAAA;AAClD,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAMrB,SAAS,aAAa,EAAA,EAAY,SAAyB,KAAA,EAAe;IAC/E,OAAO,CAAA,6BAAA,EAAgC,EAAE,CAAA,CAAA,EAAI,MAAM,EAAA;AACrD", "debugId": null}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/keys.ts"], "sourcesContent": ["import type { Publishable<PERSON><PERSON> } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\n/**\n * Configuration options for parsing publishable keys.\n */\ntype ParsePublishableKeyOptions = {\n  /** Whether to throw an error if parsing fails */\n  fatal?: boolean;\n  /** Custom domain to use for satellite instances */\n  domain?: string;\n  /** Proxy URL to use instead of the decoded frontend API */\n  proxyUrl?: string;\n  /** Whether this is a satellite instance */\n  isSatellite?: boolean;\n};\n\n/** Prefix used for production publishable keys */\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\n\n/** Prefix used for development publishable keys */\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n/**\n * Regular expression that matches development frontend API keys.\n * Matches patterns like: foo-bar-13.clerk.accounts.dev.\n */\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\n/**\n * Converts a frontend API URL into a base64-encoded publishable key.\n *\n * @param frontendApi - The frontend API URL (e.g., 'clerk.example.com').\n * @returns A base64-encoded publishable key with appropriate prefix (pk_live_ or pk_test_).\n */\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\n/**\n * Validates that a decoded publishable key has the correct format.\n * The decoded value should be a frontend API followed by exactly one '$' at the end.\n *\n * @param decoded - The decoded publishable key string to validate.\n * @returns `true` if the decoded key has valid format, `false` otherwise.\n */\nfunction isValidDecodedPublishableKey(decoded: string): boolean {\n  if (!decoded.endsWith('$')) {\n    return false;\n  }\n\n  const withoutTrailing = decoded.slice(0, -1);\n  if (withoutTrailing.includes('$')) {\n    return false;\n  }\n\n  return withoutTrailing.includes('.');\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\n/**\n * Parses and validates a publishable key, extracting the frontend API and instance type.\n *\n * @param key - The publishable key to parse.\n * @param options - Configuration options for parsing.\n * @param options.fatal\n * @param options.domain\n * @param options.proxyUrl\n * @param options.isSatellite\n * @returns Parsed publishable key object with instanceType and frontendApi, or null if invalid.\n *\n * @throws {Error} When options.fatal is true and key is missing or invalid.\n */\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let decodedFrontendApi: string;\n  try {\n    decodedFrontendApi = isomorphicAtob(key.split('_')[2]);\n  } catch {\n    if (options.fatal) {\n      throw new Error('Publishable key not valid: Failed to decode key.');\n    }\n    return null;\n  }\n\n  if (!isValidDecodedPublishableKey(decodedFrontendApi)) {\n    if (options.fatal) {\n      throw new Error('Publishable key not valid: Decoded key has invalid format.');\n    }\n    return null;\n  }\n\n  let frontendApi = decodedFrontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    if (!hasValidPrefix) {\n      return false;\n    }\n\n    const parts = key.split('_');\n    if (parts.length !== 3) {\n      return false;\n    }\n\n    const encodedPart = parts[2];\n    if (!encodedPart) {\n      return false;\n    }\n\n    const decoded = isomorphicAtob(encodedPart);\n    return isValidDecodedPublishableKey(decoded);\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Creates a memoized cache for checking if URLs are development or staging environments.\n * Uses a Map to cache results for better performance on repeated checks.\n *\n * @returns An object with an isDevOrStagingUrl method that checks if a URL is dev/staging.\n */\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    /**\n     * Checks if a URL is a development or staging environment.\n     *\n     * @param url - The URL to check (string or URL object).\n     * @returns `true` if the URL is a development or staging environment, `false` otherwise.\n     */\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\n/**\n * Checks if a publishable key is for a development environment.\n * Supports both legacy format (test_) and new format (pk_test_).\n *\n * @param apiKey - The API key to check.\n * @returns `true` if the key is for development, `false` otherwise.\n */\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\n/**\n * Checks if a publishable key is for a production environment.\n * Supports both legacy format (live_) and new format (pk_live_).\n *\n * @param apiKey - The API key to check.\n * @returns `true` if the key is for production, `false` otherwise.\n */\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\n/**\n * Checks if a secret key is for a development environment.\n * Supports both legacy format (test_) and new format (sk_test_).\n *\n * @param apiKey - The secret key to check.\n * @returns `true` if the key is for development, `false` otherwise.\n */\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\n/**\n * Checks if a secret key is for a production environment.\n * Supports both legacy format (live_) and new format (sk_live_).\n *\n * @param apiKey - The secret key to check.\n * @returns `true` if the key is for production, `false` otherwise.\n */\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\n/**\n * Generates a unique cookie suffix based on the publishable key using SHA-1 hashing.\n * The suffix is base64-encoded and URL-safe (+ and / characters are replaced).\n *\n * @param publishableKey - The publishable key to generate suffix from.\n * @param subtle - The SubtleCrypto interface to use for hashing (defaults to globalThis.crypto.subtle).\n * @returns A promise that resolves to an 8-character URL-safe base64 string.\n */\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\n/**\n * Creates a suffixed cookie name by appending the cookie suffix to the base name.\n * Used to create unique cookie names based on the publishable key.\n *\n * @param cookieName - The base cookie name.\n * @param cookieSuffix - The suffix to append (typically generated by getCookieSuffix).\n * @returns The suffixed cookie name in format: `${cookieName}_${cookieSuffix}`.\n */\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,8BAA8B;AAGpC,IAAM,8BAA8B;AAMpC,IAAM,qCAAqC;AAQpC,SAAS,oBAAoB,WAAA,EAA6B;IAC/D,MAAM,WACJ,mCAAmC,IAAA,CAAK,WAAW,KAClD,YAAY,UAAA,CAAW,QAAQ,KAAK,iTAAA,CAA6B,IAAA,CAAK,CAAA,IAAK,YAAY,QAAA,CAAS,CAAC,CAAC;IACrG,MAAM,YAAY,WAAW,8BAA8B;IAC3D,OAAO,GAAG,SAAS,OAAG,mSAAA,EAAe,GAAG,WAAW,CAAA,CAAA,CAAG,CAAC,EAAA;AACzD;AASA,SAAS,6BAA6B,OAAA,EAA0B;IAC9D,IAAI,CAAC,QAAQ,QAAA,CAAS,GAAG,GAAG;QAC1B,OAAO;IACT;IAEA,MAAM,kBAAkB,QAAQ,KAAA,CAAM,GAAG,CAAA,CAAE;IAC3C,IAAI,gBAAgB,QAAA,CAAS,GAAG,GAAG;QACjC,OAAO;IACT;IAEA,OAAO,gBAAgB,QAAA,CAAS,GAAG;AACrC;AAuBO,SAAS,oBACd,GAAA,EACA,UAA0F,CAAC,CAAA,EACpE;IACvB,MAAM,OAAO;IAEb,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;QAClC,IAAI,QAAQ,KAAA,IAAS,CAAC,KAAK;YACzB,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,QAAQ,KAAA,IAAS,CAAC,iBAAiB,GAAG,GAAG;YAC3C,MAAM,IAAI,MAAM,4BAA4B;QAC9C;QACA,OAAO;IACT;IAEA,MAAM,eAAe,IAAI,UAAA,CAAW,2BAA2B,IAAI,eAAe;IAElF,IAAI;IACJ,IAAI;QACF,yBAAqB,mSAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IACvD,EAAA,OAAQ;QACN,IAAI,QAAQ,KAAA,EAAO;YACjB,MAAM,IAAI,MAAM,kDAAkD;QACpE;QACA,OAAO;IACT;IAEA,IAAI,CAAC,6BAA6B,kBAAkB,GAAG;QACrD,IAAI,QAAQ,KAAA,EAAO;YACjB,MAAM,IAAI,MAAM,4DAA4D;QAC9E;QACA,OAAO;IACT;IAEA,IAAI,cAAc,mBAAmB,KAAA,CAAM,GAAG,CAAA,CAAE;IAEhD,IAAI,QAAQ,QAAA,EAAU;QACpB,cAAc,QAAQ,QAAA;IACxB,OAAA,IAAW,iBAAiB,iBAAiB,QAAQ,MAAA,IAAU,QAAQ,WAAA,EAAa;QAClF,cAAc,CAAA,MAAA,EAAS,QAAQ,MAAM,EAAA;IACvC;IAEA,OAAO;QACL;QACA;IACF;AACF;AAQO,SAAS,iBAAiB,MAAc,EAAA,EAAI;IACjD,IAAI;QACF,MAAM,iBAAiB,IAAI,UAAA,CAAW,2BAA2B,KAAK,IAAI,UAAA,CAAW,2BAA2B;QAEhH,IAAI,CAAC,gBAAgB;YACnB,OAAO;QACT;QAEA,MAAM,QAAQ,IAAI,KAAA,CAAM,GAAG;QAC3B,IAAI,MAAM,MAAA,KAAW,GAAG;YACtB,OAAO;QACT;QAEA,MAAM,cAAc,KAAA,CAAM,CAAC,CAAA;QAC3B,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QAEA,MAAM,cAAU,mSAAA,EAAe,WAAW;QAC1C,OAAO,6BAA6B,OAAO;IAC7C,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAQO,SAAS,6BAA6B;IAC3C,MAAM,uBAAuB,aAAA,GAAA,IAAI,IAAqB;IAEtD,OAAO;QAAA;;;;;KAAA,GAOL,mBAAmB,CAAC,QAA+B;YACjD,IAAI,CAAC,KAAK;gBACR,OAAO;YACT;YAEA,MAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI,QAAA;YACrD,IAAI,MAAM,qBAAqB,GAAA,CAAI,QAAQ;YAC3C,IAAI,QAAQ,KAAA,GAAW;gBACrB,MAAM,4SAAA,CAAwB,IAAA,CAAK,CAAA,IAAK,SAAS,QAAA,CAAS,CAAC,CAAC;gBAC5D,qBAAqB,GAAA,CAAI,UAAU,GAAG;YACxC;YACA,OAAO;QACT;IACF;AACF;AASO,SAAS,gCAAgC,MAAA,EAAyB;IACvE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AASO,SAAS,+BAA+B,MAAA,EAAyB;IACtE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AASO,SAAS,2BAA2B,MAAA,EAAyB;IAClE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AASO,SAAS,0BAA0B,MAAA,EAAyB;IACjE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAUA,eAAsB,gBACpB,cAAA,EACA,SAAuB,WAAW,MAAA,CAAO,MAAA,EACxB;IACjB,MAAM,OAAO,IAAI,YAAY,EAAE,MAAA,CAAO,cAAc;IACpD,MAAM,SAAS,MAAM,OAAO,MAAA,CAAO,SAAS,IAAI;IAChD,MAAM,eAAe,OAAO,YAAA,CAAa,GAAG,IAAI,WAAW,MAAM,CAAC;IAElE,WAAO,mSAAA,EAAe,YAAY,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,SAAA,CAAU,GAAG,CAAC;AAC9F;AAUO,IAAM,wBAAwB,CAAC,YAAoB,iBAAiC;IACzF,OAAO,GAAG,UAAU,CAAA,CAAA,EAAI,YAAY,EAAA;AACtC", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/telemetry/throttler.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/telemetry/collector.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/telemetry/events/component-mounted.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/telemetry/events/method-called.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/telemetry/events/framework-metadata.ts"], "sourcesContent": ["import type { TelemetryEvent } from '@clerk/types';\n\ntype TtlInMilliseconds = number;\n\nconst DEFAULT_CACHE_TTL_MS = 86400000; // 24 hours\n\n/**\n * Manages throttling for telemetry events using the browser's localStorage to\n * mitigate event flooding in frequently executed code paths.\n */\nexport class TelemetryEventThrottler {\n  #storageKey = 'clerk_telemetry_throttler';\n  #cacheTtl = DEFAULT_CACHE_TTL_MS;\n\n  isEventThrottled(payload: TelemetryEvent): boolean {\n    if (!this.#isValidBrowser) {\n      return false;\n    }\n\n    const now = Date.now();\n    const key = this.#generateKey(payload);\n    const entry = this.#cache?.[key];\n\n    if (!entry) {\n      const updatedCache = {\n        ...this.#cache,\n        [key]: now,\n      };\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    const shouldInvalidate = entry && now - entry > this.#cacheTtl;\n    if (shouldInvalidate) {\n      const updatedCache = this.#cache;\n      delete updatedCache[key];\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    return !!entry;\n  }\n\n  /**\n   * Generates a consistent unique key for telemetry events by sorting payload properties.\n   * This ensures that payloads with identical content in different orders produce the same key.\n   */\n  #generateKey(event: TelemetryEvent): string {\n    const { sk: _sk, pk: _pk, payload, ...rest } = event;\n\n    const sanitizedEvent: Omit<TelemetryEvent, 'sk' | 'pk' | 'payload'> & TelemetryEvent['payload'] = {\n      ...payload,\n      ...rest,\n    };\n\n    return JSON.stringify(\n      Object.keys({\n        ...payload,\n        ...rest,\n      })\n        .sort()\n        .map(key => sanitizedEvent[key]),\n    );\n  }\n\n  get #cache(): Record<string, TtlInMilliseconds> | undefined {\n    const cacheString = localStorage.getItem(this.#storageKey);\n\n    if (!cacheString) {\n      return {};\n    }\n\n    return JSON.parse(cacheString);\n  }\n\n  /**\n   * Checks if the browser's localStorage is supported and writable.\n   *\n   * If any of these operations fail, it indicates that localStorage is either\n   * not supported or not writable (e.g., in cases where the storage is full or\n   * the browser is in a privacy mode that restricts localStorage usage).\n   */\n  get #isValidBrowser(): boolean {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n\n    const storage = window.localStorage;\n    if (!storage) {\n      return false;\n    }\n\n    try {\n      const testKey = 'test';\n      storage.setItem(testKey, testKey);\n      storage.removeItem(testKey);\n\n      return true;\n    } catch (err: unknown) {\n      const isQuotaExceededError =\n        err instanceof DOMException &&\n        // Check error names for different browsers\n        (err.name === 'QuotaExceededError' || err.name === 'NS_ERROR_DOM_QUOTA_REACHED');\n\n      if (isQuotaExceededError && storage.length > 0) {\n        storage.removeItem(this.#storageKey);\n      }\n\n      return false;\n    }\n  }\n}\n", "/**\n * The `TelemetryCollector` class handles collection of telemetry events from <PERSON>. Telemetry is opt-out and can be disabled by setting a CLERK_TELEMETRY_DISABLED environment variable.\n * The `ClerkProvider` also accepts a `telemetry` prop that will be passed to the collector during initialization:.\n *\n * ```jsx\n * <ClerkProvider telemetry={false}>\n *    ...\n * </ClerkProvider>\n * ```\n *\n * For more information, please see the telemetry documentation page: https://clerk.com/docs/telemetry.\n */\nimport type {\n  InstanceType,\n  SDKMetadata,\n  TelemetryCollector as TelemetryCollectorInterface,\n  TelemetryEvent,\n  TelemetryEventRaw,\n  TelemetryLogEntry,\n} from '@clerk/types';\n\nimport { parsePublishableKey } from '../keys';\nimport { isTruthy } from '../underscore';\nimport { TelemetryEventThrottler } from './throttler';\nimport type { TelemetryCollectorOptions } from './types';\n\n/**\n * Local interface for window.Clerk to avoid global type pollution.\n * This is only used within this module and doesn't affect other packages.\n */\ninterface WindowWithClerk extends Window {\n  Clerk?: {\n    constructor?: {\n      sdkMetadata?: SDKMetadata;\n    };\n  };\n}\n\n/**\n * Type guard to check if window.Clerk exists and has the expected structure.\n */\nfunction isWindowClerkWithMetadata(clerk: unknown): clerk is { constructor: { sdkMetadata?: SDKMetadata } } {\n  return (\n    typeof clerk === 'object' && clerk !== null && 'constructor' in clerk && typeof clerk.constructor === 'function'\n  );\n}\n\ntype TelemetryCollectorConfig = Pick<\n  TelemetryCollectorOptions,\n  'samplingRate' | 'disabled' | 'debug' | 'maxBufferSize' | 'perEventSampling'\n> & {\n  endpoint: string;\n};\n\ntype TelemetryMetadata = Required<\n  Pick<TelemetryCollectorOptions, 'clerkVersion' | 'sdk' | 'sdkVersion' | 'publishableKey' | 'secretKey'>\n> & {\n  /**\n   * The instance type, derived from the provided publishableKey.\n   */\n  instanceType: InstanceType;\n};\n\n/**\n * Structure of log data sent to the telemetry endpoint.\n */\ntype TelemetryLogData = {\n  /** Service that generated the log. */\n  sdk: string;\n  /** The version of the SDK where the event originated from. */\n  sdkv: string;\n  /** The version of Clerk where the event originated from. */\n  cv: string;\n  /** Log level (info, warn, error, debug, etc.). */\n  lvl: TelemetryLogEntry['level'];\n  /** Log message. */\n  msg: string;\n  /** Instance ID - optional. */\n  iid?: string;\n  /** Timestamp when log was generated. */\n  ts: string;\n  /** Primary key. */\n  pk: string | null;\n  /** Additional payload for the log. */\n  payload: Record<string, unknown> | null;\n};\n\ntype TelemetryBufferItem = { kind: 'event'; value: TelemetryEvent } | { kind: 'log'; value: TelemetryLogData };\n\n// Accepted log levels for runtime validation\nconst VALID_LOG_LEVELS = new Set<string>(['error', 'warn', 'info', 'debug', 'trace']);\n\nconst DEFAULT_CONFIG: Partial<TelemetryCollectorConfig> = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: 'https://clerk-telemetry.com',\n};\n\nexport class TelemetryCollector implements TelemetryCollectorInterface {\n  #config: Required<TelemetryCollectorConfig>;\n  #eventThrottler: TelemetryEventThrottler;\n  #metadata: TelemetryMetadata = {} as TelemetryMetadata;\n  #buffer: TelemetryBufferItem[] = [];\n  #pendingFlush: number | ReturnType<typeof setTimeout> | null = null;\n\n  constructor(options: TelemetryCollectorOptions) {\n    this.#config = {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      perEventSampling: options.perEventSampling ?? true,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint,\n    } as Required<TelemetryCollectorConfig>;\n\n    if (!options.clerkVersion && typeof window === 'undefined') {\n      // N/A in a server environment\n      this.#metadata.clerkVersion = '';\n    } else {\n      this.#metadata.clerkVersion = options.clerkVersion ?? '';\n    }\n\n    // We will try to grab the SDK data lazily when an event is triggered, so it should always be defined once the event is sent.\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdk = options.sdk!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdkVersion = options.sdkVersion!;\n\n    this.#metadata.publishableKey = options.publishableKey ?? '';\n\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      this.#metadata.instanceType = parsedKey.instanceType;\n    }\n\n    if (options.secretKey) {\n      // Only send the first 16 characters of the secret key to to avoid sending the full key. We can still query against the partial key.\n      this.#metadata.secretKey = options.secretKey.substring(0, 16);\n    }\n\n    this.#eventThrottler = new TelemetryEventThrottler();\n  }\n\n  get isEnabled(): boolean {\n    if (this.#metadata.instanceType !== 'development') {\n      return false;\n    }\n\n    // In browser or client environments, we most likely pass the disabled option to the collector, but in environments\n    // where environment variables are available we also check for `CLERK_TELEMETRY_DISABLED`.\n    if (\n      this.#config.disabled ||\n      (typeof process !== 'undefined' && process.env && isTruthy(process.env.CLERK_TELEMETRY_DISABLED))\n    ) {\n      return false;\n    }\n\n    // navigator.webdriver is a property generally set by headless browsers that are running in an automated testing environment.\n    // Data from these environments is not meaningful for us and has the potential to produce a large volume of events, so we disable\n    // collection in this case. (ref: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/webdriver)\n    if (typeof window !== 'undefined' && !!window?.navigator?.webdriver) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get isDebug(): boolean {\n    return (\n      this.#config.debug ||\n      (typeof process !== 'undefined' && process.env && isTruthy(process.env.CLERK_TELEMETRY_DEBUG))\n    );\n  }\n\n  record(event: TelemetryEventRaw): void {\n    const preparedPayload = this.#preparePayload(event.event, event.payload);\n\n    this.#logEvent(preparedPayload.event, preparedPayload);\n\n    if (!this.#shouldRecord(preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n\n    this.#buffer.push({ kind: 'event', value: preparedPayload });\n\n    this.#scheduleFlush();\n  }\n\n  /**\n   * Records a telemetry log entry if logging is enabled and not in debug mode.\n   *\n   * @param entry - The telemetry log entry to record.\n   */\n  recordLog(entry: TelemetryLogEntry): void {\n    if (!this.#shouldRecordLog(entry)) {\n      return;\n    }\n\n    const levelIsValid = typeof entry?.level === 'string' && VALID_LOG_LEVELS.has(entry.level);\n    const messageIsValid = typeof entry?.message === 'string' && entry.message.trim().length > 0;\n\n    let normalizedTimestamp: Date | null = null;\n    const timestampInput: unknown = (entry as unknown as { timestamp?: unknown })?.timestamp;\n    if (typeof timestampInput === 'number' || typeof timestampInput === 'string') {\n      const candidate = new Date(timestampInput);\n      if (!Number.isNaN(candidate.getTime())) {\n        normalizedTimestamp = candidate;\n      }\n    }\n\n    if (!levelIsValid || !messageIsValid || normalizedTimestamp === null) {\n      if (this.isDebug && typeof console !== 'undefined') {\n        console.warn('[clerk/telemetry] Dropping invalid telemetry log entry', {\n          levelIsValid,\n          messageIsValid,\n          timestampIsValid: normalizedTimestamp !== null,\n        });\n      }\n      return;\n    }\n\n    const sdkMetadata = this.#getSDKMetadata();\n\n    const logData: TelemetryLogData = {\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      cv: this.#metadata.clerkVersion ?? '',\n      lvl: entry.level,\n      msg: entry.message,\n      ts: normalizedTimestamp.toISOString(),\n      pk: this.#metadata.publishableKey || null,\n      payload: this.#sanitizeContext(entry.context),\n    };\n\n    this.#buffer.push({ kind: 'log', value: logData });\n\n    this.#scheduleFlush();\n  }\n\n  #shouldRecord(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    return this.isEnabled && !this.isDebug && this.#shouldBeSampled(preparedPayload, eventSamplingRate);\n  }\n\n  #shouldRecordLog(_entry: TelemetryLogEntry): boolean {\n    // Always allow logs from debug logger to be sent. Debug logger itself is already gated elsewhere.\n    return true;\n  }\n\n  #shouldBeSampled(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    const randomSeed = Math.random();\n\n    const toBeSampled =\n      randomSeed <= this.#config.samplingRate &&\n      (this.#config.perEventSampling === false ||\n        typeof eventSamplingRate === 'undefined' ||\n        randomSeed <= eventSamplingRate);\n\n    if (!toBeSampled) {\n      return false;\n    }\n\n    return !this.#eventThrottler.isEventThrottled(preparedPayload);\n  }\n\n  #scheduleFlush(): void {\n    // On the server, we want to flush immediately as we have less guarantees about the lifecycle of the process\n    if (typeof window === 'undefined') {\n      this.#flush();\n      return;\n    }\n\n    const isBufferFull = this.#buffer.length >= this.#config.maxBufferSize;\n    if (isBufferFull) {\n      // If the buffer is full, flush immediately to make sure we minimize the chance of event loss.\n      // Cancel any pending flushes as we're going to flush immediately\n      if (this.#pendingFlush) {\n        if (typeof cancelIdleCallback !== 'undefined') {\n          cancelIdleCallback(Number(this.#pendingFlush));\n        } else {\n          clearTimeout(Number(this.#pendingFlush));\n        }\n      }\n      this.#flush();\n      return;\n    }\n\n    // If we have a pending flush, do nothing\n    if (this.#pendingFlush) {\n      return;\n    }\n\n    if ('requestIdleCallback' in window) {\n      this.#pendingFlush = requestIdleCallback(() => {\n        this.#flush();\n        this.#pendingFlush = null;\n      });\n    } else {\n      // This is not an ideal solution, but it at least waits until the next tick\n      this.#pendingFlush = setTimeout(() => {\n        this.#flush();\n        this.#pendingFlush = null;\n      }, 0);\n    }\n  }\n\n  #flush(): void {\n    // Capture the current buffer and clear it immediately to avoid closure references\n    const itemsToSend = [...this.#buffer];\n    this.#buffer = [];\n\n    this.#pendingFlush = null;\n\n    if (itemsToSend.length === 0) {\n      return;\n    }\n\n    const eventsToSend = itemsToSend\n      .filter(item => item.kind === 'event')\n      .map(item => (item as { kind: 'event'; value: TelemetryEvent }).value);\n\n    const logsToSend = itemsToSend\n      .filter(item => item.kind === 'log')\n      .map(item => (item as { kind: 'log'; value: TelemetryLogData }).value);\n\n    if (eventsToSend.length > 0) {\n      const eventsUrl = new URL('/v1/event', this.#config.endpoint);\n      fetch(eventsUrl, {\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        keepalive: true,\n        method: 'POST',\n        // TODO: We send an array here with that idea that we can eventually send multiple events.\n        body: JSON.stringify({ events: eventsToSend }),\n      }).catch(() => void 0);\n    }\n\n    if (logsToSend.length > 0) {\n      const logsUrl = new URL('/v1/logs', this.#config.endpoint);\n      fetch(logsUrl, {\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        keepalive: true,\n        method: 'POST',\n        body: JSON.stringify({ logs: logsToSend }),\n      }).catch(() => void 0);\n    }\n  }\n\n  /**\n   * If running in debug mode, log the event and its payload to the console.\n   */\n  #logEvent(event: TelemetryEvent['event'], payload: Record<string, any>) {\n    if (!this.isDebug) {\n      return;\n    }\n\n    if (typeof console.groupCollapsed !== 'undefined') {\n      console.groupCollapsed('[clerk/telemetry]', event);\n      console.log(payload);\n      console.groupEnd();\n    } else {\n      console.log('[clerk/telemetry]', event, payload);\n    }\n  }\n\n  /**\n   * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n   *\n   * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n   */\n  #getSDKMetadata() {\n    const sdkMetadata = {\n      name: this.#metadata.sdk,\n      version: this.#metadata.sdkVersion,\n    };\n\n    if (typeof window !== 'undefined') {\n      const windowWithClerk = window as WindowWithClerk;\n\n      if (windowWithClerk.Clerk) {\n        const windowClerk = windowWithClerk.Clerk;\n\n        if (isWindowClerkWithMetadata(windowClerk) && windowClerk.constructor.sdkMetadata) {\n          const { name, version } = windowClerk.constructor.sdkMetadata;\n\n          if (name !== undefined) {\n            sdkMetadata.name = name;\n          }\n          if (version !== undefined) {\n            sdkMetadata.version = version;\n          }\n        }\n      }\n    }\n\n    return sdkMetadata;\n  }\n\n  /**\n   * Append relevant metadata from the Clerk singleton to the event payload.\n   */\n  #preparePayload(event: TelemetryEvent['event'], payload: TelemetryEvent['payload']): TelemetryEvent {\n    const sdkMetadata = this.#getSDKMetadata();\n\n    return {\n      event,\n      cv: this.#metadata.clerkVersion ?? '',\n      it: this.#metadata.instanceType ?? '',\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      ...(this.#metadata.publishableKey ? { pk: this.#metadata.publishableKey } : {}),\n      ...(this.#metadata.secretKey ? { sk: this.#metadata.secretKey } : {}),\n      payload,\n    };\n  }\n\n  /**\n   * Best-effort sanitization of the context payload. Returns a plain object with JSON-serializable\n   * values or null when the input is missing or not serializable. Arrays are not accepted.\n   */\n  #sanitizeContext(context: unknown): Record<string, unknown> | null {\n    if (context === null || typeof context === 'undefined') {\n      return null;\n    }\n    if (typeof context !== 'object') {\n      return null;\n    }\n    try {\n      const cleaned = JSON.parse(JSON.stringify(context));\n      if (cleaned && typeof cleaned === 'object' && !Array.isArray(cleaned)) {\n        return cleaned as Record<string, unknown>;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  }\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_COMPONENT_MOUNTED = 'COMPONENT_MOUNTED';\nconst EVENT_COMPONENT_OPENED = 'COMPONENT_OPENED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype ComponentMountedBase = {\n  component: string;\n};\n\ntype EventPrebuiltComponent = ComponentMountedBase & {\n  appearanceProp: boolean;\n  elements: boolean;\n  variables: boolean;\n  baseTheme: boolean;\n};\n\ntype EventComponentMounted = ComponentMountedBase & TelemetryEventRaw['payload'];\n\n/**\n * @internal\n */\nfunction createPrebuiltComponentEvent(event: typeof EVENT_COMPONENT_MOUNTED | typeof EVENT_COMPONENT_OPENED) {\n  return function (\n    component: string,\n    props?: Record<string, any>,\n    additionalPayload?: TelemetryEventRaw['payload'],\n  ): TelemetryEventRaw<EventPrebuiltComponent> {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Boolean(props?.appearance),\n        baseTheme: Bo<PERSON>an(props?.appearance?.baseTheme),\n        elements: Boolean(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload,\n      },\n    };\n  };\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is mounted.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n * @example\n * telemetry.record(eventPrebuiltComponentMounted('SignUp', props));\n */\nexport function eventPrebuiltComponentMounted(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is opened as a modal.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n * @example\n * telemetry.record(eventPrebuiltComponentOpened('GoogleOneTap', props));\n */\nexport function eventPrebuiltComponentOpened(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a component is mounted. Use `eventPrebuiltComponentMounted` for prebuilt components.\n *\n * **Caution:** Filter the `props` you pass to this function to avoid sending too much data.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Ideally you only pass a handful of props here.\n * @example\n * telemetry.record(eventComponentMounted('SignUp', props));\n */\nexport function eventComponentMounted(\n  component: string,\n  props: TelemetryEventRaw['payload'] = {},\n): TelemetryEventRaw<EventComponentMounted> {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_METHOD_CALLED = 'METHOD_CALLED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventMethodCalled = {\n  method: string;\n} & Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventMethodCalled(\n  method: string,\n  payload?: Record<string, unknown>,\n): TelemetryEventRaw<EventMethodCalled> {\n  return {\n    event: EVENT_METHOD_CALLED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      method,\n      ...payload,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_FRAMEWORK_METADATA = 'FRAMEWORK_METADATA';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventFrameworkMetadata = Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventFrameworkMetadata(payload: EventFrameworkMetadata): TelemetryEventRaw<EventFrameworkMetadata> {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload,\n  };\n}\n"], "names": ["EVENT_SAMPLING_RATE", "EVENT_SAMPLING_RATE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,uBAAuB;AAJ7B,IAAA,aAAA,WAAA,oCAAA,gBAAA,WAAA;AAUO,IAAM,0BAAN,MAA8B;IAA9B,aAAA;QAAA,IAAA,iSAAA,EAAA,IAAA,EAAA;QACL,IAAA,iSAAA,EAAA,IAAA,EAAA,aAAc;QACd,IAAA,iSAAA,EAAA,IAAA,EAAA,WAAY;IAAA;IAEZ,iBAAiB,OAAA,EAAkC;QACjD,IAAI,KAAC,iSAAA,EAAA,IAAA,EAAK,oCAAA,qBAAiB;YACzB,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAA,CAAI;QACrB,MAAM,UAAM,oSAAA,EAAA,IAAA,EAAK,oCAAA,gBAAL,IAAA,CAAA,IAAA,EAAkB;QAC9B,MAAM,YAAQ,iSAAA,EAAA,IAAA,EAAK,oCAAA,YAAA,CAAS,GAAG,CAAA;QAE/B,IAAI,CAAC,OAAO;YACV,MAAM,eAAe;gBACnB,OAAG,iSAAA,EAAA,IAAA,EAAK,oCAAA,UAAA;gBACR,CAAC,GAAG,CAAA,EAAG;YACT;YAEA,aAAa,OAAA,KAAQ,iSAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,MAAM,mBAAmB,SAAS,MAAM,YAAQ,iSAAA,EAAA,IAAA,EAAK;QACrD,IAAI,kBAAkB;YACpB,MAAM,mBAAe,iSAAA,EAAA,IAAA,EAAK,oCAAA;YAC1B,OAAO,YAAA,CAAa,GAAG,CAAA;YAEvB,aAAa,OAAA,KAAQ,iSAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,OAAO,CAAC,CAAC;IACX;AAsEF;AApGE,cAAA,IAAA;AACA,YAAA,IAAA;AAFK,qCAAA,IAAA;AAAA;;;CAAA,GAqCL,iBAAY,SAAC,KAAA,EAA+B;IAC1C,MAAM,EAAE,IAAI,GAAA,EAAK,IAAI,GAAA,EAAK,OAAA,EAAS,GAAG,KAAK,CAAA,GAAI;IAE/C,MAAM,iBAA4F;QAChG,GAAG,OAAA;QACH,GAAG,IAAA;IACL;IAEA,OAAO,KAAK,SAAA,CACV,OAAO,IAAA,CAAK;QACV,GAAG,OAAA;QACH,GAAG,IAAA;IACL,CAAC,EACE,IAAA,CAAK,EACL,GAAA,CAAI,CAAA,MAAO,cAAA,CAAe,GAAG,CAAC;AAErC;AAEI,YAAM,WAAkD;IAC1D,MAAM,cAAc,aAAa,OAAA,KAAQ,iSAAA,EAAA,IAAA,EAAK,YAAW;IAEzD,IAAI,CAAC,aAAa;QAChB,OAAO,CAAC;IACV;IAEA,OAAO,KAAK,KAAA,CAAM,WAAW;AAC/B;AASI,qBAAe,WAAY;IAC7B,IAAI,OAAO,WAAW,kBAAa;QACjC,OAAO;IACT;;;IAEA,MAAM,UAAU,OAAO;AAuBzB;;ACrEF,SAAS,0BAA0B,KAAA,EAAyE;IAC1G,OACE,OAAO,UAAU,YAAY,UAAU,QAAQ,iBAAiB,SAAS,OAAO,MAAM,WAAA,KAAgB;AAE1G;AA6CA,IAAM,mBAAmB,aAAA,GAAA,IAAI,IAAY;IAAC;IAAS;IAAQ;IAAQ;IAAS,OAAO;CAAC;AAEpF,IAAM,iBAAoD;IACxD,cAAc;IACd,eAAe;IAAA,mDAAA;IAAA,wDAAA;IAAA,+BAAA;IAIf,UAAU;AACZ;AAnGA,IAAA,SAAA,iBAAA,WAAA,SAAA,eAAA,+BAAA,iBAAA,oBAAA,oBAAA,kBAAA,UAAA,aAAA,mBAAA,mBAAA;AAqGO,IAAM,qBAAN,MAAgE;IAOrE,YAAY,OAAA,CAAoC;QAP3C,IAAA,iSAAA,EAAA,IAAA,EAAA;QACL,IAAA,iSAAA,EAAA,IAAA,EAAA;QACA,IAAA,iSAAA,EAAA,IAAA,EAAA;QACA,IAAA,iSAAA,EAAA,IAAA,EAAA,WAA+B,CAAC;QAChC,IAAA,iSAAA,EAAA,IAAA,EAAA,SAAiC,CAAC,CAAA;QAClC,IAAA,iSAAA,EAAA,IAAA,EAAA,eAA+D;QAG7D,IAAA,iSAAA,EAAA,IAAA,EAAK,SAAU;YACb,eAAe,QAAQ,aAAA,IAAiB,eAAe,aAAA;YACvD,cAAc,QAAQ,YAAA,IAAgB,eAAe,YAAA;YACrD,kBAAkB,QAAQ,gBAAA,IAAoB;YAC9C,UAAU,QAAQ,QAAA,IAAY;YAC9B,OAAO,QAAQ,KAAA,IAAS;YACxB,UAAU,eAAe,QAAA;QAC3B;QAEA,IAAI,CAAC,QAAQ,YAAA,IAAgB,OAAO,SAAW,aAAa;YAE1D,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe;QAChC,OAAO;YACL,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,QAAQ,YAAA,IAAgB;QACxD;QAIA,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,GAAA,GAAM,QAAQ,GAAA;QAE7B,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,UAAA,GAAa,QAAQ,UAAA;QAEpC,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB,QAAQ,cAAA,IAAkB;QAE1D,MAAM,gBAAY,wSAAA,EAAoB,QAAQ,cAAc;QAC5D,IAAI,WAAW;YACb,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,UAAU,YAAA;QAC1C;QAEA,IAAI,QAAQ,SAAA,EAAW;YAErB,IAAA,iSAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY,QAAQ,SAAA,CAAU,SAAA,CAAU,GAAG,EAAE;QAC9D;QAEA,IAAA,iSAAA,EAAA,IAAA,EAAK,iBAAkB,IAAI,wBAAwB;IACrD;IAEA,IAAI,YAAqB;QACvB,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,KAAiB,eAAe;YACjD,OAAO;QACT;QAIA,QACE,iSAAA,EAAA,IAAA,EAAK,SAAQ,QAAA,IACZ,OAAO,YAAY,eAAe,QAAQ,GAAA,QAAO,6RAAA,EAAS,QAAQ,GAAA,CAAI,wBAAwB,GAC/F;YACA,OAAO;QACT;QAKA,IAAI,OAAO,SAAW,eAAe,CAAC,CAAC,QAAQ,WAAW,WAAW;;QAIrE,OAAO;IACT;IAEA,IAAI,UAAmB;QACrB,WACE,iSAAA,EAAA,IAAA,EAAK,SAAQ,KAAA,IACZ,OAAO,YAAY,eAAe,QAAQ,GAAA,QAAO,6RAAA,EAAS,QAAQ,GAAA,CAAI,qBAAqB;IAEhG;IAEA,OAAO,KAAA,EAAgC;QACrC,MAAM,sBAAkB,oSAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA,EAAqB,MAAM,KAAA,EAAO,MAAM,OAAA;QAEhE,IAAA,oSAAA,EAAA,IAAA,EAAK,+BAAA,aAAL,IAAA,CAAA,IAAA,EAAe,gBAAgB,KAAA,EAAO;QAEtC,IAAI,KAAC,oSAAA,EAAA,IAAA,EAAK,+BAAA,iBAAL,IAAA,CAAA,IAAA,EAAmB,iBAAiB,MAAM,iBAAA,GAAoB;YACjE;QACF;QAEA,IAAA,iSAAA,EAAA,IAAA,EAAK,SAAQ,IAAA,CAAK;YAAE,MAAM;YAAS,OAAO;QAAgB,CAAC;QAE3D,IAAA,oSAAA,EAAA,IAAA,EAAK,+BAAA,kBAAL,IAAA,CAAA,IAAA;IACF;IAAA;;;;GAAA,GAOA,UAAU,KAAA,EAAgC;QACxC,IAAI,KAAC,oSAAA,EAAA,IAAA,EAAK,+BAAA,oBAAL,IAAA,CAAA,IAAA,EAAsB,QAAQ;YACjC;QACF;QAEA,MAAM,eAAe,OAAO,OAAO,UAAU,YAAY,iBAAiB,GAAA,CAAI,MAAM,KAAK;QACzF,MAAM,iBAAiB,OAAO,OAAO,YAAY,YAAY,MAAM,OAAA,CAAQ,IAAA,CAAK,EAAE,MAAA,GAAS;QAE3F,IAAI,sBAAmC;QACvC,MAAM,iBAA2B,OAA8C;QAC/E,IAAI,OAAO,mBAAmB,YAAY,OAAO,mBAAmB,UAAU;YAC5E,MAAM,YAAY,IAAI,KAAK,cAAc;YACzC,IAAI,CAAC,OAAO,KAAA,CAAM,UAAU,OAAA,CAAQ,CAAC,GAAG;gBACtC,sBAAsB;YACxB;QACF;QAEA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,wBAAwB,MAAM;YACpE,IAAI,IAAA,CAAK,OAAA,IAAW,OAAO,YAAY,aAAa;gBAClD,QAAQ,IAAA,CAAK,0DAA0D;oBACrE;oBACA;oBACA,kBAAkB,wBAAwB;gBAC5C,CAAC;YACH;YACA;QACF;QAEA,MAAM,kBAAc,oSAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA;QAEpB,MAAM,UAA4B;YAChC,KAAK,YAAY,IAAA;YACjB,MAAM,YAAY,OAAA;YAClB,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;YACnC,KAAK,MAAM,KAAA;YACX,KAAK,MAAM,OAAA;YACX,IAAI,oBAAoB,WAAA,CAAY;YACpC,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,cAAA,IAAkB;YACrC,aAAS,oSAAA,EAAA,IAAA,EAAK,+BAAA,oBAAL,IAAA,CAAA,IAAA,EAAsB,MAAM,OAAA;QACvC;QAEA,IAAA,iSAAA,EAAA,IAAA,EAAK,SAAQ,IAAA,CAAK;YAAE,MAAM;YAAO,OAAO;QAAQ,CAAC;QAEjD,IAAA,oSAAA,EAAA,IAAA,EAAK,+BAAA,kBAAL,IAAA,CAAA,IAAA;IACF;AA0MF;AApVE,UAAA,IAAA;AACA,kBAAA,IAAA;AACA,YAAA,IAAA;AACA,UAAA,IAAA;AACA,gBAAA,IAAA;AALK,gCAAA,IAAA;AA6IL,kBAAa,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IACzE,OAAO,IAAA,CAAK,SAAA,IAAa,CAAC,IAAA,CAAK,OAAA,QAAW,oSAAA,EAAA,IAAA,EAAK,+BAAA,oBAAL,IAAA,CAAA,IAAA,EAAsB,iBAAiB;AACnF;AAEA,qBAAgB,SAAC,MAAA,EAAoC;IAEnD,OAAO;AACT;AAEA,qBAAgB,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IAC5E,MAAM,aAAa,KAAK,MAAA,CAAO;IAE/B,MAAM,cACJ,kBAAc,iSAAA,EAAA,IAAA,EAAK,SAAQ,YAAA,IAAA,KAC1B,iSAAA,EAAA,IAAA,EAAK,SAAQ,gBAAA,KAAqB,SACjC,OAAO,sBAAsB,eAC7B,cAAc,iBAAA;IAElB,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,OAAO,KAAC,iSAAA,EAAA,IAAA,EAAK,iBAAgB,gBAAA,CAAiB,eAAe;AAC/D;AAEA,mBAAc,WAAS;IAErB,IAAI,OAAO,WAAW,kBAAa;QACjC,IAAA,oSAAA,EAAA,IAAA,EAAK,+BAAA,UAAL,IAAA,CAAA,IAAA;QACA;IACF;;;IAEA,MAAM,eAAe,mBAAK,SAAQ,UAAU,mBAAK,SAAQ;AAgC3D;AAEA,WAAM,WAAS;IAEb,MAAM,cAAc,CAAC;eAAG,iSAAA,EAAA,IAAA,EAAK,QAAO;KAAA;IACpC,IAAA,iSAAA,EAAA,IAAA,EAAK,SAAU,CAAC,CAAA;IAEhB,IAAA,iSAAA,EAAA,IAAA,EAAK,eAAgB;IAErB,IAAI,YAAY,MAAA,KAAW,GAAG;QAC5B;IACF;IAEA,MAAM,eAAe,YAClB,MAAA,CAAO,CAAA,OAAQ,KAAK,IAAA,KAAS,OAAO,EACpC,GAAA,CAAI,CAAA,OAAS,KAAkD,KAAK;IAEvE,MAAM,aAAa,YAChB,MAAA,CAAO,CAAA,OAAQ,KAAK,IAAA,KAAS,KAAK,EAClC,GAAA,CAAI,CAAA,OAAS,KAAkD,KAAK;IAEvE,IAAI,aAAa,MAAA,GAAS,GAAG;QAC3B,MAAM,YAAY,IAAI,IAAI,iBAAa,iSAAA,EAAA,IAAA,EAAK,SAAQ,QAAQ;QAC5D,MAAM,WAAW;YACf,SAAS;gBACP,gBAAgB;YAClB;YACA,WAAW;YACX,QAAQ;YAAA,0FAAA;YAER,MAAM,KAAK,SAAA,CAAU;gBAAE,QAAQ;YAAa,CAAC;QAC/C,CAAC,EAAE,KAAA,CAAM,IAAM,KAAA,CAAM;IACvB;IAEA,IAAI,WAAW,MAAA,GAAS,GAAG;QACzB,MAAM,UAAU,IAAI,IAAI,gBAAY,iSAAA,EAAA,IAAA,EAAK,SAAQ,QAAQ;QACzD,MAAM,SAAS;YACb,SAAS;gBACP,gBAAgB;YAClB;YACA,WAAW;YACX,QAAQ;YACR,MAAM,KAAK,SAAA,CAAU;gBAAE,MAAM;YAAW,CAAC;QAC3C,CAAC,EAAE,KAAA,CAAM,IAAM,KAAA,CAAM;IACvB;AACF;AAAA;;CAAA,GAKA,cAAS,SAAC,KAAA,EAAgC,OAAA,EAA8B;IACtE,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;QACjB;IACF;IAEA,IAAI,OAAO,QAAQ,cAAA,KAAmB,aAAa;QACjD,QAAQ,cAAA,CAAe,qBAAqB,KAAK;QACjD,QAAQ,GAAA,CAAI,OAAO;QACnB,QAAQ,QAAA,CAAS;IACnB,OAAO;QACL,QAAQ,GAAA,CAAI,qBAAqB,OAAO,OAAO;IACjD;AACF;AAAA;;;;CAAA,GAOA,oBAAe,WAAG;IAChB,MAAM,cAAc;QAClB,UAAM,iSAAA,EAAA,IAAA,EAAK,WAAU,GAAA;QACrB,aAAS,iSAAA,EAAA,IAAA,EAAK,WAAU,UAAA;IAC1B;IAEA,IAAI,OAAO,WAAW,aAAa;;IAmBnC,OAAO;AACT;AAAA;;CAAA,GAKA,oBAAe,SAAC,KAAA,EAAgC,OAAA,EAAoD;IAClG,MAAM,kBAAc,oSAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA;IAEpB,OAAO;QACL;QACA,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,KAAK,YAAY,IAAA;QACjB,MAAM,YAAY,OAAA;QAClB,OAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB;YAAE,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,cAAA;QAAe,IAAI,CAAC,CAAA;QAC7E,OAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY;YAAE,QAAI,iSAAA,EAAA,IAAA,EAAK,WAAU,SAAA;QAAU,IAAI,CAAC,CAAA;QACnE;IACF;AACF;AAAA;;;CAAA,GAMA,qBAAgB,SAAC,OAAA,EAAkD;IACjE,IAAI,YAAY,QAAQ,OAAO,YAAY,aAAa;QACtD,OAAO;IACT;IACA,IAAI,OAAO,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,IAAI;QACF,MAAM,UAAU,KAAK,KAAA,CAAM,KAAK,SAAA,CAAU,OAAO,CAAC;QAClD,IAAI,WAAW,OAAO,YAAY,YAAY,CAAC,MAAM,OAAA,CAAQ,OAAO,GAAG;YACrE,OAAO;QACT;QACA,OAAO;IACT,EAAA,OAAQ;QACN,OAAO;IACT;AACF;;ACvbF,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAkB5B,SAAS,6BAA6B,KAAA,EAAuE;IAC3G,OAAO,SACL,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;QAC3C,OAAO;YACL;YACA,mBAAmB;YACnB,SAAS;gBACP;gBACA,gBAAgB,QAAQ,OAAO,UAAU;gBACzC,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,UAAU,QAAQ,OAAO,YAAY,QAAQ;gBAC7C,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,GAAG,iBAAA;YACL;QACF;IACF;AACF;AAWO,SAAS,8BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,uBAAuB,EAAE,WAAW,OAAO,iBAAiB;AAClG;AAWO,SAAS,6BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,sBAAsB,EAAE,WAAW,OAAO,iBAAiB;AACjG;AAYO,SAAS,sBACd,SAAA,EACA,QAAsC,CAAC,CAAA,EACG;IAC1C,OAAO;QACL,OAAO;QACP,mBAAmB;QACnB,SAAS;YACP;YACA,GAAG,KAAA;QACL;IACF;AACF;;ACjGA,IAAM,sBAAsB;AAC5B,IAAMA,uBAAsB;AASrB,SAAS,kBACd,MAAA,EACA,OAAA,EACsC;IACtC,OAAO;QACL,OAAO;QACP,mBAAmBA;QACnB,SAAS;YACP;YACA,GAAG,OAAA;QACL;IACF;AACF;;ACtBA,IAAM,2BAA2B;AACjC,IAAMC,uBAAsB;AAOrB,SAAS,uBAAuB,OAAA,EAA4E;IACjH,OAAO;QACL,OAAO;QACP,mBAAmBA;QACnB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1361, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/organization.ts"], "sourcesContent": ["import type { OrganizationMembershipResource } from '@clerk/types';\n\n/**\n * Finds the organization membership for a given organization ID from a list of memberships\n * @param organizationMemberships - Array of organization memberships to search through\n * @param organizationId - ID of the organization to find the membership for\n * @returns The matching organization membership or undefined if not found\n */\nexport function getCurrentOrganizationMembership(\n  organizationMemberships: OrganizationMembershipResource[],\n  organizationId: string,\n) {\n  return organizationMemberships.find(\n    organizationMembership => organizationMembership.organization.id === organizationId,\n  );\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,iCACd,uBAAA,EACA,cAAA,EACA;IACA,OAAO,wBAAwB,IAAA,CAC7B,CAAA,yBAA0B,uBAAuB,YAAA,CAAa,EAAA,KAAO;AAEzE", "debugId": null}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/noop.ts"], "sourcesContent": ["export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n"], "names": [], "mappings": ";;;;;AAAO,IAAM,OAAO,CAAA,GAAI,SAExB,CAF+C", "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/createDeferredPromise.ts"], "sourcesContent": ["import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n"], "names": [], "mappings": ";;;;;;;AAUO,IAAM,wBAAwB,MAAM;IACzC,IAAI,UAAoB,yRAAA;IACxB,IAAI,SAAmB,yRAAA;IACvB,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;QACxC,UAAU;QACV,SAAS;IACX,CAAC;IACD,OAAO;QAAE;QAAS;QAAS;IAAO;AACpC", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/authorization-errors.ts"], "sourcesContent": ["import type { ReverificationConfig } from '@clerk/types';\n\ntype ClerkError<T> = {\n  clerk_error: T;\n};\n\nconst REVERIFICATION_REASON = 'reverification-error';\n\ntype ReverificationError<M extends { metadata?: any } = { metadata: unknown }> = ClerkError<\n  {\n    type: 'forbidden';\n    reason: typeof REVERIFICATION_REASON;\n  } & M\n>;\n\nconst reverificationError = <MC extends ReverificationConfig>(\n  missingConfig?: MC,\n): ReverificationError<{\n  metadata?: {\n    reverification?: MC;\n  };\n}> => ({\n  clerk_error: {\n    type: 'forbidden',\n    reason: REVERIFICATION_REASON,\n    metadata: {\n      reverification: missingConfig,\n    },\n  },\n});\n\nconst reverificationErrorResponse = (...args: Parameters<typeof reverificationError>) =>\n  new Response(JSON.stringify(reverificationError(...args)), {\n    status: 403,\n  });\n\nconst isReverificationHint = (result: any): result is ReturnType<typeof reverificationError> => {\n  return (\n    result &&\n    typeof result === 'object' &&\n    'clerk_error' in result &&\n    result.clerk_error?.type === 'forbidden' &&\n    result.clerk_error?.reason === REVERIFICATION_REASON\n  );\n};\n\nexport { reverificationError, reverificationErrorResponse, isReverificationHint };\n"], "names": [], "mappings": ";;;;;;;;;AAMA,IAAM,wBAAwB;AAS9B,IAAM,sBAAsB,CAC1B,gBAAA,CAKK;QACL,aAAa;YACX,MAAM;YACN,QAAQ;YACR,UAAU;gBACR,gBAAgB;YAClB;QACF;IACF,CAAA;AAEA,IAAM,8BAA8B,CAAA,GAAI,OACtC,IAAI,SAAS,KAAK,SAAA,CAAU,oBAAoB,GAAG,IAAI,CAAC,GAAG;QACzD,QAAQ;IACV,CAAC;AAEH,IAAM,uBAAuB,CAAC,WAAkE;IAC9F,OACE,UACA,OAAO,WAAW,YAClB,iBAAiB,UACjB,OAAO,WAAA,EAAa,SAAS,eAC7B,OAAO,WAAA,EAAa,WAAW;AAEnC", "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/createContextAndHook.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/contexts.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/clerk-swr.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/usePagesOrInfinite.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useOrganization.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useOrganizationList.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useSafeLayoutEffect.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useSession.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useSessionList.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useUser.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useClerk.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useDeepEqualMemo.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useReverification.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/createCommerceHook.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useStatements.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/usePaymentAttempts.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/usePaymentMethods.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/usePlans.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useSubscription.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/hooks/useCheckout.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/commerce.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/stripe-react/index.tsx", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/react/stripe-react/utils.ts"], "sourcesContent": ["'use client';\nimport React from 'react';\n\n/**\n * Assert that the context value exists, otherwise throw an error.\n *\n * @internal\n */\nexport function assertContextExists(contextVal: unknown, msgOrCtx: string | React.Context<any>): asserts contextVal {\n  if (!contextVal) {\n    throw typeof msgOrCtx === 'string' ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);\n  }\n}\n\ntype Options = { assertCtxFn?: (v: unknown, msg: string) => void };\ntype ContextOf<T> = React.Context<{ value: T } | undefined>;\ntype UseCtxFn<T> = () => T;\n\n/**\n * Create and return a Context and two hooks that return the context value.\n * The Context type is derived from the type passed in by the user.\n *\n * The first hook returned guarantees that the context exists so the returned value is always `CtxValue`\n * The second hook makes no guarantees, so the returned value can be `CtxValue | undefined`\n *\n * @internal\n */\nexport const createContextAndHook = <CtxVal>(\n  displayName: string,\n  options?: Options,\n): [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>] => {\n  const { assertCtxFn = assertContextExists } = options || {};\n  const Ctx = React.createContext<{ value: CtxVal } | undefined>(undefined);\n  Ctx.displayName = displayName;\n\n  const useCtx = () => {\n    const ctx = React.useContext(Ctx);\n    assertCtxFn(ctx, `${displayName} not found`);\n    return (ctx as any).value as CtxVal;\n  };\n\n  const useCtxWithoutGuarantee = () => {\n    const ctx = React.useContext(Ctx);\n    return ctx ? ctx.value : {};\n  };\n\n  return [Ctx, useCtx, useCtxWithoutGuarantee];\n};\n", "'use client';\n\nimport type {\n  ClerkO<PERSON>s,\n  ClientResource,\n  CommerceSubscriptionPlanPeriod,\n  ForPayerType,\n  LoadedClerk,\n  OrganizationResource,\n  SignedInSessionResource,\n  UserResource,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport { SWRConfig } from './clerk-swr';\nimport { createContextAndHook } from './hooks/createContextAndHook';\n\nconst [ClerkInstanceContext, useClerkInstanceContext] = createContextAndHook<LoadedClerk>('ClerkInstanceContext');\nconst [UserContext, useUserContext] = createContextAndHook<UserResource | null | undefined>('UserContext');\nconst [ClientContext, useClientContext] = createContextAndHook<ClientResource | null | undefined>('ClientContext');\nconst [SessionContext, useSessionContext] = createContextAndHook<SignedInSessionResource | null | undefined>(\n  'SessionContext',\n);\n\nconst OptionsContext = React.createContext<ClerkOptions>({});\n\ntype UseCheckoutOptions = {\n  for?: ForPayerType;\n  planPeriod: CommerceSubscriptionPlanPeriod;\n  planId: string;\n};\n\nconst [CheckoutContext, useCheckoutContext] = createContextAndHook<UseCheckoutOptions>('CheckoutContext');\n\nconst __experimental_CheckoutProvider = ({ children, ...rest }: PropsWithChildren<UseCheckoutOptions>) => {\n  return <CheckoutContext.Provider value={{ value: rest }}>{children}</CheckoutContext.Provider>;\n};\n\n/**\n * @internal\n */\nfunction useOptionsContext(): ClerkOptions {\n  const context = React.useContext(OptionsContext);\n  if (context === undefined) {\n    throw new Error('useOptions must be used within an OptionsContext');\n  }\n  return context;\n}\n\ntype OrganizationContextProps = {\n  organization: OrganizationResource | null | undefined;\n};\nconst [OrganizationContextInternal, useOrganizationContext] = createContextAndHook<{\n  organization: OrganizationResource | null | undefined;\n}>('OrganizationContext');\n\nconst OrganizationProvider = ({\n  children,\n  organization,\n  swrConfig,\n}: PropsWithChildren<\n  OrganizationContextProps & {\n    // Exporting inferred types  directly from SWR will result in error while building declarations\n    swrConfig?: any;\n  }\n>) => {\n  return (\n    <SWRConfig value={swrConfig}>\n      <OrganizationContextInternal.Provider\n        value={{\n          value: { organization },\n        }}\n      >\n        {children}\n      </OrganizationContextInternal.Provider>\n    </SWRConfig>\n  );\n};\n\n/**\n * @internal\n */\nfunction useAssertWrappedByClerkProvider(displayNameOrFn: string | (() => void)): void {\n  const ctx = React.useContext(ClerkInstanceContext);\n\n  if (!ctx) {\n    if (typeof displayNameOrFn === 'function') {\n      displayNameOrFn();\n      return;\n    }\n\n    throw new Error(\n      `${displayNameOrFn} can only be used within the <ClerkProvider /> component.\n\nPossible fixes:\n1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.\n2. Check for multiple versions of the \\`@clerk/shared\\` package in your project. Use a tool like \\`npm ls @clerk/shared\\` to identify multiple versions, and update your dependencies to only rely on one.\n\nLearn more: https://clerk.com/docs/components/clerk-provider`.trim(),\n    );\n  }\n}\n\nexport {\n  ClientContext,\n  useClientContext,\n  OrganizationProvider,\n  useOrganizationContext,\n  UserContext,\n  OptionsContext,\n  useOptionsContext,\n  useUserContext,\n  SessionContext,\n  useSessionContext,\n  ClerkInstanceContext,\n  useClerkInstanceContext,\n  useCheckoutContext,\n  __experimental_CheckoutProvider,\n  useAssertWrappedByClerkProvider,\n};\n", "'use client';\n\nexport * from 'swr';\n\nexport { default as useSWR } from 'swr';\nexport { default as useSWRInfinite } from 'swr/infinite';\n", "'use client';\n\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nimport { useSWR, useSWRInfinite } from '../clerk-swr';\nimport type {\n  CacheSetter,\n  PagesOrInfiniteConfig,\n  PagesOrInfiniteOptions,\n  PaginatedResources,\n  ValueOrSetter,\n} from '../types';\n\n/**\n * Returns an object containing only the keys from the first object that are not present in the second object.\n * Useful for extracting unique parameters that should be passed to a request while excluding common cache keys.\n *\n * @internal\n *\n * @example\n * ```typescript\n * // Example 1: Basic usage\n * const obj1 = { name: '<PERSON>', age: 30, city: 'NY' };\n * const obj2 = { name: '<PERSON>', age: 30 };\n * getDifferentKeys(obj1, obj2); // Returns { city: 'NY' }\n *\n * // Example 2: With cache keys\n * const requestParams = { page: 1, limit: 10, userId: '123' };\n * const cacheKeys = { userId: '123' };\n * getDifferentKeys(requestParams, cacheKeys); // Returns { page: 1, limit: 10 }\n * ```\n */\nfunction getDifferentKeys(obj1: Record<string, unknown>, obj2: Record<string, unknown>): Record<string, unknown> {\n  const keysSet = new Set(Object.keys(obj2));\n  const differentKeysObject: Record<string, unknown> = {};\n\n  for (const key1 of Object.keys(obj1)) {\n    if (!keysSet.has(key1)) {\n      differentKeysObject[key1] = obj1[key1];\n    }\n  }\n\n  return differentKeysObject;\n}\n\n/**\n * A hook that safely merges user-provided pagination options with default values.\n * It caches initial pagination values (page and size) until component unmount to prevent unwanted rerenders.\n *\n * @internal\n *\n * @example\n * ```typescript\n * // Example 1: With user-provided options\n * const userOptions = { initialPage: 2, pageSize: 20, infinite: true };\n * const defaults = { initialPage: 1, pageSize: 10, infinite: false };\n * useWithSafeValues(userOptions, defaults);\n * // Returns { initialPage: 2, pageSize: 20, infinite: true }\n *\n * // Example 2: With boolean true (use defaults)\n * const params = true;\n * const defaults = { initialPage: 1, pageSize: 10, infinite: false };\n * useWithSafeValues(params, defaults);\n * // Returns { initialPage: 1, pageSize: 10, infinite: false }\n *\n * // Example 3: With undefined options (fallback to defaults)\n * const params = undefined;\n * const defaults = { initialPage: 1, pageSize: 10, infinite: false };\n * useWithSafeValues(params, defaults);\n * // Returns { initialPage: 1, pageSize: 10, infinite: false }\n * ```\n */\nexport const useWithSafeValues = <T extends PagesOrInfiniteOptions>(params: T | true | undefined, defaultValues: T) => {\n  const shouldUseDefaults = typeof params === 'boolean' && params;\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(\n    shouldUseDefaults ? defaultValues.initialPage : (params?.initialPage ?? defaultValues.initialPage),\n  );\n  const pageSizeRef = useRef(shouldUseDefaults ? defaultValues.pageSize : (params?.pageSize ?? defaultValues.pageSize));\n\n  const newObj: Record<string, unknown> = {};\n  for (const key of Object.keys(defaultValues)) {\n    // @ts-ignore\n    newObj[key] = shouldUseDefaults ? defaultValues[key] : (params?.[key] ?? defaultValues[key]);\n  }\n\n  return {\n    ...newObj,\n    initialPage: initialPageRef.current,\n    pageSize: pageSizeRef.current,\n  } as T;\n};\n\nconst cachingSWROptions = {\n  dedupingInterval: 1000 * 60,\n  focusThrottleInterval: 1000 * 60 * 2,\n} satisfies Parameters<typeof useSWR>[2];\n\ntype ArrayType<DataArray> = DataArray extends Array<infer ElementType> ? ElementType : never;\ntype ExtractData<Type> = Type extends { data: infer Data } ? ArrayType<Data> : Type;\n\ntype UsePagesOrInfinite = <\n  Params extends PagesOrInfiniteOptions,\n  FetcherReturnData extends Record<string, any>,\n  CacheKeys extends Record<string, unknown> = Record<string, unknown>,\n  TConfig extends PagesOrInfiniteConfig = PagesOrInfiniteConfig,\n>(\n  /**\n   * The parameters will be passed to the fetcher.\n   */\n  params: Params,\n  /**\n   * A Promise returning function to fetch your data.\n   */\n  fetcher: ((p: Params) => FetcherReturnData | Promise<FetcherReturnData>) | undefined,\n  /**\n   * Internal configuration of the hook.\n   */\n  config: TConfig,\n  cacheKeys: CacheKeys,\n) => PaginatedResources<ExtractData<FetcherReturnData>, TConfig['infinite']>;\n\n/**\n * A flexible pagination hook that supports both traditional pagination and infinite loading.\n * It provides a unified API for handling paginated data fetching, with built-in caching through SWR.\n * The hook can operate in two modes:\n * - Traditional pagination: Fetches one page at a time with page navigation\n * - Infinite loading: Accumulates data as more pages are loaded.\n *\n * Features:\n * - Cache management with SWR\n * - Loading and error states\n * - Page navigation helpers\n * - Data revalidation and updates\n * - Support for keeping previous data while loading.\n *\n * @internal\n */\nexport const usePagesOrInfinite: UsePagesOrInfinite = (params, fetcher, config, cacheKeys) => {\n  const [paginatedPage, setPaginatedPage] = useState(params.initialPage ?? 1);\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(params.initialPage ?? 1);\n  const pageSizeRef = useRef(params.pageSize ?? 10);\n\n  const enabled = config.enabled ?? true;\n  const cacheMode = config.__experimental_mode === 'cache';\n  const triggerInfinite = config.infinite ?? false;\n  const keepPreviousData = config.keepPreviousData ?? false;\n\n  const pagesCacheKey = {\n    ...cacheKeys,\n    ...params,\n    initialPage: paginatedPage,\n    pageSize: pageSizeRef.current,\n  };\n\n  // cacheMode being `true` indicates that the cache key is defined, but the fetcher is not.\n  // This allows to ready the cache instead of firing a request.\n  const shouldFetch = !triggerInfinite && enabled && (!cacheMode ? !!fetcher : true);\n  const swrKey = shouldFetch ? pagesCacheKey : null;\n  const swrFetcher =\n    !cacheMode && !!fetcher\n      ? (cacheKeyParams: Record<string, unknown>) => {\n          const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n          return fetcher({ ...params, ...requestParams });\n        }\n      : null;\n\n  const {\n    data: swrData,\n    isValidating: swrIsValidating,\n    isLoading: swrIsLoading,\n    error: swrError,\n    mutate: swrMutate,\n  } = useSWR(swrKey, swrFetcher, { keepPreviousData, ...cachingSWROptions });\n\n  const {\n    data: swrInfiniteData,\n    isLoading: swrInfiniteIsLoading,\n    isValidating: swrInfiniteIsValidating,\n    error: swrInfiniteError,\n    size,\n    setSize,\n    mutate: swrInfiniteMutate,\n  } = useSWRInfinite(\n    pageIndex => {\n      if (!triggerInfinite || !enabled) {\n        return null;\n      }\n\n      return {\n        ...params,\n        ...cacheKeys,\n        initialPage: initialPageRef.current + pageIndex,\n        pageSize: pageSizeRef.current,\n      };\n    },\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n    cachingSWROptions,\n  );\n\n  const page = useMemo(() => {\n    if (triggerInfinite) {\n      return size;\n    }\n    return paginatedPage;\n  }, [triggerInfinite, size, paginatedPage]);\n\n  const fetchPage: ValueOrSetter<number> = useCallback(\n    numberOrgFn => {\n      if (triggerInfinite) {\n        void setSize(numberOrgFn);\n        return;\n      }\n      return setPaginatedPage(numberOrgFn);\n    },\n    [setSize],\n  );\n\n  const data = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.map(a => a?.data).flat() ?? [];\n    }\n    return swrData?.data ?? [];\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const count = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.[swrInfiniteData?.length - 1]?.total_count || 0;\n    }\n    return swrData?.total_count ?? 0;\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const isLoading = triggerInfinite ? swrInfiniteIsLoading : swrIsLoading;\n  const isFetching = triggerInfinite ? swrInfiniteIsValidating : swrIsValidating;\n  const error = (triggerInfinite ? swrInfiniteError : swrError) ?? null;\n  const isError = !!error;\n  /**\n   * Helpers.\n   */\n  const fetchNext = useCallback(() => {\n    fetchPage(n => Math.max(0, n + 1));\n  }, [fetchPage]);\n\n  const fetchPrevious = useCallback(() => {\n    fetchPage(n => Math.max(0, n - 1));\n  }, [fetchPage]);\n\n  const offsetCount = (initialPageRef.current - 1) * pageSizeRef.current;\n\n  const pageCount = Math.ceil((count - offsetCount) / pageSizeRef.current);\n  const hasNextPage = count - offsetCount * pageSizeRef.current > page * pageSizeRef.current;\n  const hasPreviousPage = (page - 1) * pageSizeRef.current > offsetCount * pageSizeRef.current;\n\n  const setData: CacheSetter = triggerInfinite\n    ? value =>\n        swrInfiniteMutate(value, {\n          revalidate: false,\n        })\n    : value =>\n        swrMutate(value, {\n          revalidate: false,\n        });\n\n  const revalidate = triggerInfinite ? () => swrInfiniteMutate() : () => swrMutate();\n\n  return {\n    data,\n    count,\n    error,\n    isLoading,\n    isFetching,\n    isError,\n    page,\n    pageCount,\n    fetchPage,\n    fetchNext,\n    fetchPrevious,\n    hasNextPage,\n    hasPreviousPage,\n    // Let the hook return type define this type\n    revalidate: revalidate as any,\n    // Let the hook return type define this type\n    setData: setData as any,\n  };\n};\n", "/* eslint-disable jsdoc/require-description-complete-sentence */\nimport type {\n  ClerkPaginatedResponse,\n  CommerceSubscriptionItemResource,\n  GetDomainsParams,\n  GetInvitationsParams,\n  GetMembershipRequestParams,\n  GetMembersParams,\n  GetSubscriptionsParams,\n  OrganizationDomainResource,\n  OrganizationInvitationResource,\n  OrganizationMembershipRequestResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n} from '@clerk/types';\n\nimport { getCurrentOrganizationMembership } from '../../organization';\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport {\n  useAssertWrappedByClerkProvider,\n  useClerkInstanceContext,\n  useOrganizationContext,\n  useSessionContext,\n} from '../contexts';\nimport type { PaginatedHookConfig, PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\n/**\n * @interface\n */\nexport type UseOrganizationParams = {\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`enrollmentMode`: A string that filters the domains by the provided [enrollment mode](https://clerk.com/docs/organizations/verified-domains#enrollment-mode).</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  domains?: true | PaginatedHookConfig<GetDomainsParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`status`: A string that filters the membership requests by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  membershipRequests?: true | PaginatedHookConfig<GetMembershipRequestParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`role`: An array of [`OrganizationCustomRoleKey`](https://clerk.com/docs/references/javascript/types/organization-custom-role-key).</li>\n   *  <li>`query`: A string that filters the memberships by the provided string.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  memberships?: true | PaginatedHookConfig<GetMembersParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`status`: A string that filters the invitations by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  invitations?: true | PaginatedHookConfig<GetInvitationsParams>;\n  /**\n   * @experimental This is an experimental API for the Billing feature that is available under a public beta, and the API is subject to change.\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   * <ul>\n   *  <li>`orgId`: A string that filters the subscriptions by the provided organization ID.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  subscriptions?: true | PaginatedHookConfig<GetSubscriptionsParams>;\n};\n\n/**\n * @interface\n */\nexport type UseOrganizationReturn<T extends UseOrganizationParams> =\n  | {\n      /**\n       * A boolean that indicates whether Clerk has completed initialization. Initially `false`, becomes `true` once Clerk loads.\n       */\n      isLoaded: false;\n      /**\n       * The currently active organization.\n       */\n      organization: undefined;\n      /**\n       * The current organization membership.\n       */\n      membership: undefined;\n      /**\n       * Includes a paginated list of the organization's domains.\n       */\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      /**\n       * Includes a paginated list of the organization's membership requests.\n       */\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      /**\n       * Includes a paginated list of the organization's memberships.\n       */\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      /**\n       * Includes a paginated list of the organization's invitations.\n       */\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n      /**\n       * @experimental This is an experimental API for the Billing feature that is available under a public beta, and the API is subject to change.\n       * Includes a paginated list of the organization's subscriptions.\n       */\n      subscriptions: PaginatedResourcesWithDefault<CommerceSubscriptionItemResource>;\n    }\n  | {\n      isLoaded: true;\n      organization: OrganizationResource;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n      subscriptions: PaginatedResourcesWithDefault<CommerceSubscriptionItemResource>;\n    }\n  | {\n      isLoaded: boolean;\n      organization: OrganizationResource | null;\n      membership: OrganizationMembershipResource | null | undefined;\n      domains: PaginatedResources<\n        OrganizationDomainResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      membershipRequests: PaginatedResources<\n        OrganizationMembershipRequestResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      memberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['memberships'] extends { infinite: true } ? true : false\n      > | null;\n      invitations: PaginatedResources<\n        OrganizationInvitationResource,\n        T['invitations'] extends { infinite: true } ? true : false\n      > | null;\n      subscriptions: PaginatedResources<\n        CommerceSubscriptionItemResource,\n        T['subscriptions'] extends { infinite: true } ? true : false\n      > | null;\n    };\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  error: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\n/**\n * The `useOrganization()` hook retrieves attributes of the currently active organization.\n *\n * @example\n * ### Expand and paginate attributes\n *\n * To keep network usage to a minimum, developers are required to opt-in by specifying which resource they need to fetch and paginate through. By default, the `memberships`, `invitations`, `membershipRequests`, and `domains` attributes are not populated. You must pass `true` or an object with the desired properties to fetch and paginate the data.\n *\n * ```tsx\n * // invitations.data will never be populated.\n * const { invitations } = useOrganization()\n *\n * // Use default values to fetch invitations, such as initialPage = 1 and pageSize = 10\n * const { invitations } = useOrganization({\n *   invitations: true,\n * })\n *\n * // Pass your own values to fetch invitations\n * const { invitations } = useOrganization({\n *   invitations: {\n *     pageSize: 20,\n *     initialPage: 2, // skips the first page\n *   },\n * })\n *\n * // Aggregate pages in order to render an infinite list\n * const { invitations } = useOrganization({\n *   invitations: {\n *     infinite: true,\n *   },\n * })\n * ```\n *\n * @example\n * ### Infinite pagination\n *\n * The following example demonstrates how to use the `infinite` property to fetch and append new data to the existing list. The `memberships` attribute will be populated with the first page of the organization's memberships. When the \"Load more\" button is clicked, the `fetchNext` helper function will be called to append the next page of memberships to the list.\n *\n * ```tsx\n * import { useOrganization } from '@clerk/clerk-react'\n *\n * export default function MemberList() {\n *   const { memberships } = useOrganization({\n *     memberships: {\n *       infinite: true, // Append new data to the existing list\n *       keepPreviousData: true, // Persist the cached data until the new data has been fetched\n *     },\n *   })\n *\n *   if (!memberships) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <h2>Organization members</h2>\n *       <ul>\n *         {memberships.data?.map((membership) => (\n *           <li key={membership.id}>\n *             {membership.publicUserData.firstName} {membership.publicUserData.lastName} <\n *             {membership.publicUserData.identifier}> :: {membership.role}\n *           </li>\n *         ))}\n *       </ul>\n *\n *       <button\n *         disabled={!memberships.hasNextPage} // Disable the button if there are no more available pages to be fetched\n *         onClick={memberships.fetchNext}\n *       >\n *         Load more\n *       </button>\n *     </div>\n *   )\n * }\n * ```\n *\n * @example\n * ### Simple pagination\n *\n * The following example demonstrates how to use the `fetchPrevious` and `fetchNext` helper functions to paginate through the data. The `memberships` attribute will be populated with the first page of the organization's memberships. When the \"Previous page\" or \"Next page\" button is clicked, the `fetchPrevious` or `fetchNext` helper function will be called to fetch the previous or next page of memberships.\n *\n * Notice the difference between this example's pagination and the infinite pagination example above.\n *\n * ```tsx\n * import { useOrganization } from '@clerk/clerk-react'\n *\n * export default function MemberList() {\n *   const { memberships } = useOrganization({\n *     memberships: {\n *       keepPreviousData: true, // Persist the cached data until the new data has been fetched\n *     },\n *   })\n *\n *   if (!memberships) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <h2>Organization members</h2>\n *       <ul>\n *         {memberships.data?.map((membership) => (\n *           <li key={membership.id}>\n *             {membership.publicUserData.firstName} {membership.publicUserData.lastName} <\n *             {membership.publicUserData.identifier}> :: {membership.role}\n *           </li>\n *         ))}\n *       </ul>\n *\n *       <button disabled={!memberships.hasPreviousPage} onClick={memberships.fetchPrevious}>\n *         Previous page\n *       </button>\n *\n *       <button disabled={!memberships.hasNextPage} onClick={memberships.fetchNext}>\n *         Next page\n *       </button>\n *     </div>\n *   )\n * }\n * ```\n */\nexport function useOrganization<T extends UseOrganizationParams>(params?: T): UseOrganizationReturn<T> {\n  const {\n    domains: domainListParams,\n    membershipRequests: membershipRequestsListParams,\n    memberships: membersListParams,\n    invitations: invitationsListParams,\n    subscriptions: subscriptionsListParams,\n  } = params || {};\n\n  useAssertWrappedByClerkProvider('useOrganization');\n\n  const { organization } = useOrganizationContext();\n  const session = useSessionContext();\n\n  const domainSafeValues = useWithSafeValues(domainListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n    enrollmentMode: undefined,\n  });\n\n  const membershipRequestSafeValues = useWithSafeValues(membershipRequestsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const membersSafeValues = useWithSafeValues(membersListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    role: undefined,\n    keepPreviousData: false,\n    infinite: false,\n    query: undefined,\n  });\n\n  const invitationsSafeValues = useWithSafeValues(invitationsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: ['pending'],\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const subscriptionsSafeValues = useWithSafeValues(subscriptionsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled('useOrganization'));\n\n  const domainParams =\n    typeof domainListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: domainSafeValues.initialPage,\n          pageSize: domainSafeValues.pageSize,\n          enrollmentMode: domainSafeValues.enrollmentMode,\n        };\n\n  const membershipRequestParams =\n    typeof membershipRequestsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membershipRequestSafeValues.initialPage,\n          pageSize: membershipRequestSafeValues.pageSize,\n          status: membershipRequestSafeValues.status,\n        };\n\n  const membersParams =\n    typeof membersListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membersSafeValues.initialPage,\n          pageSize: membersSafeValues.pageSize,\n          role: membersSafeValues.role,\n          query: membersSafeValues.query,\n        };\n\n  const invitationsParams =\n    typeof invitationsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: invitationsSafeValues.initialPage,\n          pageSize: invitationsSafeValues.pageSize,\n          status: invitationsSafeValues.status,\n        };\n\n  const subscriptionsParams =\n    typeof subscriptionsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: subscriptionsSafeValues.initialPage,\n          pageSize: subscriptionsSafeValues.pageSize,\n          orgId: organization?.id,\n        };\n\n  const domains = usePagesOrInfinite<GetDomainsParams, ClerkPaginatedResponse<OrganizationDomainResource>>(\n    {\n      ...domainParams,\n    },\n    organization?.getDomains,\n    {\n      keepPreviousData: domainSafeValues.keepPreviousData,\n      infinite: domainSafeValues.infinite,\n      enabled: !!domainParams,\n    },\n    {\n      type: 'domains',\n      organizationId: organization?.id,\n    },\n  );\n\n  const membershipRequests = usePagesOrInfinite<\n    GetMembershipRequestParams,\n    ClerkPaginatedResponse<OrganizationMembershipRequestResource>\n  >(\n    {\n      ...membershipRequestParams,\n    },\n    organization?.getMembershipRequests,\n    {\n      keepPreviousData: membershipRequestSafeValues.keepPreviousData,\n      infinite: membershipRequestSafeValues.infinite,\n      enabled: !!membershipRequestParams,\n    },\n    {\n      type: 'membershipRequests',\n      organizationId: organization?.id,\n    },\n  );\n\n  const memberships = usePagesOrInfinite<GetMembersParams, ClerkPaginatedResponse<OrganizationMembershipResource>>(\n    membersParams || {},\n    organization?.getMemberships,\n    {\n      keepPreviousData: membersSafeValues.keepPreviousData,\n      infinite: membersSafeValues.infinite,\n      enabled: !!membersParams,\n    },\n    {\n      type: 'members',\n      organizationId: organization?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<GetInvitationsParams, ClerkPaginatedResponse<OrganizationInvitationResource>>(\n    {\n      ...invitationsParams,\n    },\n    organization?.getInvitations,\n    {\n      keepPreviousData: invitationsSafeValues.keepPreviousData,\n      infinite: invitationsSafeValues.infinite,\n      enabled: !!invitationsParams,\n    },\n    {\n      type: 'invitations',\n      organizationId: organization?.id,\n    },\n  );\n\n  const subscriptions = usePagesOrInfinite<\n    GetSubscriptionsParams,\n    ClerkPaginatedResponse<CommerceSubscriptionItemResource>\n  >(\n    {\n      ...subscriptionsParams,\n    },\n    organization?.getSubscriptions,\n    {\n      keepPreviousData: subscriptionsSafeValues.keepPreviousData,\n      infinite: subscriptionsSafeValues.infinite,\n      enabled: !!subscriptionsParams,\n    },\n    {\n      type: 'subscriptions',\n      organizationId: organization?.id,\n    },\n  );\n\n  if (organization === undefined) {\n    return {\n      isLoaded: false,\n      organization: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n      subscriptions: undefinedPaginatedResource,\n    };\n  }\n\n  if (organization === null) {\n    return {\n      isLoaded: true,\n      organization: null,\n      membership: null,\n      domains: null,\n      membershipRequests: null,\n      memberships: null,\n      invitations: null,\n      subscriptions: null,\n    };\n  }\n\n  /** In SSR context we include only the organization object when loadOrg is set to true. */\n  if (!clerk.loaded && organization) {\n    return {\n      isLoaded: true,\n      organization,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n      subscriptions: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: clerk.loaded,\n    organization,\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    membership: getCurrentOrganizationMembership(session!.user.organizationMemberships, organization.id), // your membership in the current org\n    domains,\n    membershipRequests,\n    memberships,\n    invitations,\n    subscriptions,\n  };\n}\n", "/* eslint-disable jsdoc/require-description-complete-sentence */\nimport type {\n  ClerkPaginatedResponse,\n  CreateOrganizationParams,\n  GetUserOrganizationInvitationsParams,\n  GetUserOrganizationMembershipParams,\n  GetUserOrganizationSuggestionsParams,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  OrganizationSuggestionResource,\n  SetActive,\n  UserOrganizationInvitationResource,\n} from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useUserContext } from '../contexts';\nimport type { PaginatedHookConfig, PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\n/**\n * @interface\n */\nexport type UseOrganizationListParams = {\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   *\n   * <ul>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  userMemberships?: true | PaginatedHookConfig<GetUserOrganizationMembershipParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   *\n   * <ul>\n   *  <li>`status`: A string that filters the invitations by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  userInvitations?: true | PaginatedHookConfig<GetUserOrganizationInvitationsParams>;\n  /**\n   * If set to `true`, all default properties will be used.<br />\n   * Otherwise, accepts an object with the following optional properties:\n   *\n   * <ul>\n   *  <li>`status`: A string that filters the suggestions by the provided status.</li>\n   *  <li>Any of the properties described in [Shared properties](#shared-properties).</li>\n   * </ul>\n   */\n  userSuggestions?: true | PaginatedHookConfig<GetUserOrganizationSuggestionsParams>;\n};\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  error: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\n/**\n * @interface\n */\nexport type UseOrganizationListReturn<T extends UseOrganizationListParams> =\n  | {\n      /**\n       * A boolean that indicates whether Clerk has completed initialization and there is an authenticated user. Initially `false`, becomes `true` once Clerk loads with a user.\n       */\n      isLoaded: false;\n      /**\n       * A function that returns a `Promise` which resolves to the newly created `Organization`.\n       */\n      createOrganization: undefined;\n      /**\n       * A function that sets the active session and/or organization.\n       */\n      setActive: undefined;\n      /**\n       * Returns `PaginatedResources` which includes a list of the user's organization memberships.\n       */\n      userMemberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      /**\n       * Returns `PaginatedResources` which includes a list of the user's organization invitations.\n       */\n      userInvitations: PaginatedResourcesWithDefault<UserOrganizationInvitationResource>;\n      /**\n       * Returns `PaginatedResources` which includes a list of suggestions for organizations that the user can join.\n       */\n      userSuggestions: PaginatedResourcesWithDefault<OrganizationSuggestionResource>;\n    }\n  | {\n      isLoaded: boolean;\n      createOrganization: (CreateOrganizationParams: CreateOrganizationParams) => Promise<OrganizationResource>;\n      setActive: SetActive;\n      userMemberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['userMemberships'] extends { infinite: true } ? true : false\n      >;\n      userInvitations: PaginatedResources<\n        UserOrganizationInvitationResource,\n        T['userInvitations'] extends { infinite: true } ? true : false\n      >;\n      userSuggestions: PaginatedResources<\n        OrganizationSuggestionResource,\n        T['userSuggestions'] extends { infinite: true } ? true : false\n      >;\n    };\n\n/**\n * The `useOrganizationList()` hook provides access to the current user's organization memberships, invitations, and suggestions. It also includes methods for creating new organizations and managing the active organization.\n *\n * @example\n * ### Expanding and paginating attributes\n *\n * To keep network usage to a minimum, developers are required to opt-in by specifying which resource they need to fetch and paginate through. So by default, the `userMemberships`, `userInvitations`, and `userSuggestions` attributes are not populated. You must pass true or an object with the desired properties to fetch and paginate the data.\n *\n * ```tsx\n * // userMemberships.data will never be populated\n * const { userMemberships } = useOrganizationList()\n *\n * // Use default values to fetch userMemberships, such as initialPage = 1 and pageSize = 10\n * const { userMemberships } = useOrganizationList({\n *   userMemberships: true,\n * })\n *\n * // Pass your own values to fetch userMemberships\n * const { userMemberships } = useOrganizationList({\n *   userMemberships: {\n *     pageSize: 20,\n *     initialPage: 2, // skips the first page\n *   },\n * })\n *\n * // Aggregate pages in order to render an infinite list\n * const { userMemberships } = useOrganizationList({\n *   userMemberships: {\n *     infinite: true,\n *   },\n * })\n * ```\n *\n * @example\n * ### Infinite pagination\n *\n * The following example demonstrates how to use the `infinite` property to fetch and append new data to the existing list. The `userMemberships` attribute will be populated with the first page of the user's organization memberships. When the \"Load more\" button is clicked, the `fetchNext` helper function will be called to append the next page of memberships to the list.\n *\n * ```tsx {{ filename: 'src/components/JoinedOrganizations.tsx' }}\n * import { useOrganizationList } from '@clerk/clerk-react'\n * import React from 'react'\n *\n * const JoinedOrganizations = () => {\n *   const { isLoaded, setActive, userMemberships } = useOrganizationList({\n *     userMemberships: {\n *       infinite: true,\n *     },\n *   })\n *\n *   if (!isLoaded) {\n *     return <>Loading</>\n *   }\n *\n *   return (\n *     <>\n *       <ul>\n *         {userMemberships.data?.map((mem) => (\n *           <li key={mem.id}>\n *             <span>{mem.organization.name}</span>\n *             <button onClick={() => setActive({ organization: mem.organization.id })}>Select</button>\n *           </li>\n *         ))}\n *       </ul>\n *\n *       <button disabled={!userMemberships.hasNextPage} onClick={() => userMemberships.fetchNext()}>\n *         Load more\n *       </button>\n *     </>\n *   )\n * }\n *\n * export default JoinedOrganizations\n * ```\n *\n * @example\n * ### Simple pagination\n *\n * The following example demonstrates how to use the `fetchPrevious` and `fetchNext` helper functions to paginate through the data. The `userInvitations` attribute will be populated with the first page of invitations. When the \"Previous page\" or \"Next page\" button is clicked, the `fetchPrevious` or `fetchNext` helper function will be called to fetch the previous or next page of invitations.\n *\n * Notice the difference between this example's pagination and the infinite pagination example above.\n *\n * ```tsx {{ filename: 'src/components/UserInvitationsTable.tsx' }}\n * import { useOrganizationList } from '@clerk/clerk-react'\n * import React from 'react'\n *\n * const UserInvitationsTable = () => {\n *   const { isLoaded, userInvitations } = useOrganizationList({\n *     userInvitations: {\n *       infinite: true,\n *       keepPreviousData: true,\n *     },\n *   })\n *\n *   if (!isLoaded || userInvitations.isLoading) {\n *     return <>Loading</>\n *   }\n *\n *   return (\n *     <>\n *       <table>\n *         <thead>\n *           <tr>\n *             <th>Email</th>\n *             <th>Org name</th>\n *           </tr>\n *         </thead>\n *\n *         <tbody>\n *           {userInvitations.data?.map((inv) => (\n *             <tr key={inv.id}>\n *               <th>{inv.emailAddress}</th>\n *               <th>{inv.publicOrganizationData.name}</th>\n *             </tr>\n *           ))}\n *         </tbody>\n *       </table>\n *\n *       <button disabled={!userInvitations.hasPreviousPage} onClick={userInvitations.fetchPrevious}>\n *         Prev\n *       </button>\n *       <button disabled={!userInvitations.hasNextPage} onClick={userInvitations.fetchNext}>\n *         Next\n *       </button>\n *     </>\n *   )\n * }\n *\n * export default UserInvitationsTable\n * ```\n */\nexport function useOrganizationList<T extends UseOrganizationListParams>(params?: T): UseOrganizationListReturn<T> {\n  const { userMemberships, userInvitations, userSuggestions } = params || {};\n\n  useAssertWrappedByClerkProvider('useOrganizationList');\n\n  const userMembershipsSafeValues = useWithSafeValues(userMemberships, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userInvitationsSafeValues = useWithSafeValues(userInvitations, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userSuggestionsSafeValues = useWithSafeValues(userSuggestions, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n\n  clerk.telemetry?.record(eventMethodCalled('useOrganizationList'));\n\n  const userMembershipsParams =\n    typeof userMemberships === 'undefined'\n      ? undefined\n      : {\n          initialPage: userMembershipsSafeValues.initialPage,\n          pageSize: userMembershipsSafeValues.pageSize,\n        };\n\n  const userInvitationsParams =\n    typeof userInvitations === 'undefined'\n      ? undefined\n      : {\n          initialPage: userInvitationsSafeValues.initialPage,\n          pageSize: userInvitationsSafeValues.pageSize,\n          status: userInvitationsSafeValues.status,\n        };\n\n  const userSuggestionsParams =\n    typeof userSuggestions === 'undefined'\n      ? undefined\n      : {\n          initialPage: userSuggestionsSafeValues.initialPage,\n          pageSize: userSuggestionsSafeValues.pageSize,\n          status: userSuggestionsSafeValues.status,\n        };\n\n  const isClerkLoaded = !!(clerk.loaded && user);\n\n  const memberships = usePagesOrInfinite<\n    GetUserOrganizationMembershipParams,\n    ClerkPaginatedResponse<OrganizationMembershipResource>\n  >(\n    userMembershipsParams || {},\n    user?.getOrganizationMemberships,\n    {\n      keepPreviousData: userMembershipsSafeValues.keepPreviousData,\n      infinite: userMembershipsSafeValues.infinite,\n      enabled: !!userMembershipsParams,\n    },\n    {\n      type: 'userMemberships',\n      userId: user?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<\n    GetUserOrganizationInvitationsParams,\n    ClerkPaginatedResponse<UserOrganizationInvitationResource>\n  >(\n    {\n      ...userInvitationsParams,\n    },\n    user?.getOrganizationInvitations,\n    {\n      keepPreviousData: userInvitationsSafeValues.keepPreviousData,\n      infinite: userInvitationsSafeValues.infinite,\n      enabled: !!userInvitationsParams,\n    },\n    {\n      type: 'userInvitations',\n      userId: user?.id,\n    },\n  );\n\n  const suggestions = usePagesOrInfinite<\n    GetUserOrganizationSuggestionsParams,\n    ClerkPaginatedResponse<OrganizationSuggestionResource>\n  >(\n    {\n      ...userSuggestionsParams,\n    },\n    user?.getOrganizationSuggestions,\n    {\n      keepPreviousData: userSuggestionsSafeValues.keepPreviousData,\n      infinite: userSuggestionsSafeValues.infinite,\n      enabled: !!userSuggestionsParams,\n    },\n    {\n      type: 'userSuggestions',\n      userId: user?.id,\n    },\n  );\n\n  // TODO: Properly check for SSR user values\n  if (!isClerkLoaded) {\n    return {\n      isLoaded: false,\n      createOrganization: undefined,\n      setActive: undefined,\n      userMemberships: undefinedPaginatedResource,\n      userInvitations: undefinedPaginatedResource,\n      userSuggestions: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: isClerkLoaded,\n    setActive: clerk.setActive,\n    createOrganization: clerk.createOrganization,\n    userMemberships: memberships,\n    userInvitations: invitations,\n    userSuggestions: suggestions,\n  };\n}\n", "import React from 'react';\n\n/**\n * @internal\n */\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import type { UseSessionReturn } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useSessionContext } from '../contexts';\n\ntype UseSession = () => UseSessionReturn;\n\nconst hookName = `useSession`;\n/**\n * The `useSession()` hook provides access to the current user's [`Session`](https://clerk.com/docs/references/javascript/session) object, as well as helpers for setting the active session.\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Signed out\", \"Signed in\"]\n *\n * @function\n *\n * @param [options] - An object containing options for the `useSession()` hook.\n *\n * @example\n * ### Access the `Session` object\n *\n * The following example uses the `useSession()` hook to access the `Session` object, which has the `lastActiveAt` property. The `lastActiveAt` property is a `Date` object used to show the time the session was last active.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useSession } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, session, isSignedIn } = useSession()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *   if (!isSignedIn) {\n *     // Handle signed out state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <p>This session has been active since {session.lastActiveAt.toLocaleString()}</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-session.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useSession: UseSession = () => {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const session = useSessionContext();\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  if (session === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, session: undefined };\n  }\n\n  if (session === null) {\n    return { isLoaded: true, isSignedIn: false, session: null };\n  }\n\n  return { isLoaded: true, isSignedIn: clerk.isSignedIn, session };\n};\n", "import type { UseSessionListReturn } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useClientContext } from '../contexts';\n\nconst hookName = 'useSessionList';\n/**\n * The `useSessionList()` hook returns an array of [`Session`](https://clerk.com/docs/references/javascript/session) objects that have been registered on the client device.\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Loaded\"]\n *\n * @function\n *\n * @example\n * ### Get a list of sessions\n *\n * The following example uses `useSessionList()` to get a list of sessions that have been registered on the client device. The `sessions` property is used to show the number of times the user has visited the page.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useSessionList } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, sessions } = useSessionList()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return (\n *     <div>\n *       <p>Welcome back. You've been here {sessions.length} times before.</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-session-list.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useSessionList = (): UseSessionListReturn => {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const isomorphicClerk = useClerkInstanceContext();\n  const client = useClientContext();\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  if (!client) {\n    return { isLoaded: false, sessions: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    sessions: client.sessions,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import type { UseUserReturn } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext, useUserContext } from '../contexts';\n\nconst hookName = 'useUser';\n/**\n * The `useUser()` hook provides access to the current user's [`User`](https://clerk.com/docs/references/javascript/user) object, which contains all the data for a single user in your application and provides methods to manage their account. This hook also allows you to check if the user is signed in and if Clerk has loaded and initialized.\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Signed out\", \"Signed in\"]\n *\n * @example\n * ### Get the current user\n *\n * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user) object, which contains the current user's data such as their full name. The `isLoaded` and `isSignedIn` properties are used to handle the loading state and to check if the user is signed in, respectively.\n *\n * ```tsx {{ filename: 'src/Example.tsx' }}\n * export default function Example() {\n *   const { isSignedIn, user, isLoaded } = useUser()\n *\n *   if (!isLoaded) {\n *     return <div>Loading...</div>\n *   }\n *\n *   if (!isSignedIn) {\n *     return <div>Sign in to view this page</div>\n *   }\n *\n *   return <div>Hello {user.firstName}!</div>\n * }\n * ```\n *\n * @example\n * ### Update user data\n *\n * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user) object, which calls the [`update()`](https://clerk.com/docs/references/javascript/user#update) method to update the current user's information.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useUser } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, user } = useUser()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   if (!user) return null\n *\n *   const updateUser = async () => {\n *     await user.update({\n *       firstName: 'John',\n *       lastName: 'Doe',\n *     })\n *   }\n *\n *   return (\n *     <>\n *       <button onClick={updateUser}>Update your name</button>\n *       <p>user.firstName: {user?.firstName}</p>\n *       <p>user.lastName: {user?.lastName}</p>\n *     </>\n *   )\n * }\n * ```\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-user.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n *\n * @example\n * ### Reload user data\n *\n * The following example uses the `useUser()` hook to access the [`User`](https://clerk.com/docs/references/javascript/user) object, which calls the [`reload()`](https://clerk.com/docs/references/javascript/user#reload) method to get the latest user's information.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useUser } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const { isLoaded, user } = useUser()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   if (!user) return null\n *\n *   const updateUser = async () => {\n *     // Update data via an API endpoint\n *     const updateMetadata = await fetch('/api/updateMetadata')\n *\n *     // Check if the update was successful\n *     if (updateMetadata.message !== 'success') {\n *       throw new Error('Error updating')\n *     }\n *\n *     // If the update was successful, reload the user data\n *     await user.reload()\n *   }\n *\n *   return (\n *     <>\n *       <button onClick={updateUser}>Update your metadata</button>\n *       <p>user role: {user?.publicMetadata.role}</p>\n *     </>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-user.md#nextjs-02}\n *\n * </Tab>\n * </Tabs>\n */\nexport function useUser(): UseUserReturn {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const user = useUserContext();\n  const clerk = useClerkInstanceContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  if (user === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, user: undefined };\n  }\n\n  if (user === null) {\n    return { isLoaded: true, isSignedIn: false, user: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, user };\n}\n", "import type { LoadedClerk } from '@clerk/types';\n\nimport { useAssertWrappedByClerkProvider, useClerkInstanceContext } from '../contexts';\n\n/**\n * > [!WARNING]\n * > This hook should only be used for advanced use cases, such as building a completely custom OAuth flow or as an escape hatch to access to the `Clerk` object.\n *\n * The `useClerk()` hook provides access to the [`Clerk`](https://clerk.com/docs/references/javascript/clerk) object, allowing you to build alternatives to any Clerk Component.\n *\n * @function\n *\n * @returns The `useClerk()` hook returns the `Clerk` object, which includes all the methods and properties listed in the [`Clerk` reference](https://clerk.com/docs/references/javascript/clerk).\n *\n * @example\n *\n * The following example uses the `useClerk()` hook to access the `clerk` object. The `clerk` object is used to call the [`openSignIn()`](https://clerk.com/docs/references/javascript/clerk#sign-in) method to open the sign-in modal.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/Home.tsx' }}\n * import { useClerk } from '@clerk/clerk-react'\n *\n * export default function Home() {\n *   const clerk = useClerk()\n *\n *   return <button onClick={() => clerk.openSignIn({})}>Sign in</button>\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../../docs/use-clerk.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useClerk = (): LoadedClerk => {\n  useAssertWrappedByClerkProvider('useClerk');\n  return useClerkInstanceContext();\n};\n", "import { dequal as deepEqual } from 'dequal';\nimport React from 'react';\n\ntype UseMemoFactory<T> = () => T;\ntype UseMemoDependencyArray = Exclude<Parameters<typeof React.useMemo>[1], 'undefined'>;\ntype UseDeepEqualMemo = <T>(factory: UseMemoFactory<T>, dependencyArray: UseMemoDependencyArray) => T;\n\nconst useDeepEqualMemoize = <T>(value: T) => {\n  const ref = React.useRef<T>(value);\n  if (!deepEqual(value, ref.current)) {\n    ref.current = value;\n  }\n  return React.useMemo(() => ref.current, [ref.current]);\n};\n\n/**\n * @internal\n */\nexport const useDeepEqualMemo: UseDeepEqualMemo = (factory, dependencyArray) => {\n  return React.useMemo(factory, useDeepEqualMemoize(dependencyArray));\n};\n\n/**\n * @internal\n */\nexport const isDeeplyEqual = deepEqual;\n", "import type { Clerk, SessionVerificationLevel } from '@clerk/types';\nimport { useCallback, useRef } from 'react';\n\nimport { validateReverificationConfig } from '../../authorization';\nimport { isReverificationHint, reverificationError } from '../../authorization-errors';\nimport { ClerkRuntimeError, isClerkAPIResponseError } from '../../error';\nimport { eventMethodCalled } from '../../telemetry';\nimport { createDeferredPromise } from '../../utils/createDeferredPromise';\nimport { useClerk } from './useClerk';\nimport { useSafeLayoutEffect } from './useSafeLayoutEffect';\n\nconst CLERK_API_REVERIFICATION_ERROR_CODE = 'session_reverification_required';\n\nasync function resolveResult<T>(result: Promise<T> | T): Promise<T | ReturnType<typeof reverificationError>> {\n  try {\n    const r = await result;\n    if (r instanceof Response) {\n      return r.json();\n    }\n    return r;\n  } catch (e) {\n    // Treat fapi assurance as an assurance hint\n    if (isClerkAPIResponseError(e) && e.errors.find(({ code }) => code === CLERK_API_REVERIFICATION_ERROR_CODE)) {\n      return reverificationError();\n    }\n\n    // rethrow\n    throw e;\n  }\n}\n\ntype ExcludeClerkError<T> = T extends { clerk_error: any } ? never : T;\n\n/**\n * @interface\n */\ntype NeedsReverificationParameters = {\n  cancel: () => void;\n  complete: () => void;\n  level: SessionVerificationLevel | undefined;\n};\n\n/**\n * The optional options object.\n * @interface\n */\ntype UseReverificationOptions = {\n  /**\n   * A handler that is called when reverification is needed, this will opt-out of using the default UI when provided.\n   *\n   * @param cancel - A function that will cancel the reverification process.\n   * @param complete - A function that will retry the original request after reverification.\n   * @param level - The level returned with the reverification hint.\n   *\n   */\n  onNeedsReverification?: (properties: NeedsReverificationParameters) => void;\n};\n\n/**\n * @interface\n */\ntype UseReverificationResult<Fetcher extends (...args: any[]) => Promise<any> | undefined> = (\n  ...args: Parameters<Fetcher>\n) => Promise<ExcludeClerkError<Awaited<ReturnType<Fetcher>>>>;\n\n/**\n * @interface\n */\ntype UseReverification = <\n  Fetcher extends (...args: any[]) => Promise<any> | undefined,\n  Options extends UseReverificationOptions = UseReverificationOptions,\n>(\n  fetcher: Fetcher,\n  options?: Options,\n) => UseReverificationResult<Fetcher>;\n\ntype CreateReverificationHandlerParams = UseReverificationOptions & {\n  openUIComponent: Clerk['__internal_openReverification'];\n  telemetry: Clerk['telemetry'];\n};\n\nfunction createReverificationHandler(params: CreateReverificationHandlerParams) {\n  function assertReverification<Fetcher extends (...args: any[]) => Promise<any> | undefined>(\n    fetcher: Fetcher,\n  ): (...args: Parameters<Fetcher>) => Promise<ExcludeClerkError<Awaited<ReturnType<Fetcher>>>> {\n    return (async (...args: Parameters<Fetcher>) => {\n      let result = await resolveResult(fetcher(...args));\n\n      if (isReverificationHint(result)) {\n        /**\n         * Create a promise\n         */\n        const resolvers = createDeferredPromise();\n\n        const isValidMetadata = validateReverificationConfig(result.clerk_error.metadata?.reverification);\n\n        const level = isValidMetadata ? isValidMetadata().level : undefined;\n\n        const cancel = () => {\n          resolvers.reject(\n            new ClerkRuntimeError('User cancelled attempted verification', {\n              code: 'reverification_cancelled',\n            }),\n          );\n        };\n\n        const complete = () => {\n          resolvers.resolve(true);\n        };\n\n        if (params.onNeedsReverification === undefined) {\n          /**\n           * On success resolve the pending promise\n           * On cancel reject the pending promise\n           */\n          params.openUIComponent?.({\n            level: level,\n            afterVerification: complete,\n            afterVerificationCancelled: cancel,\n          });\n        } else {\n          params.onNeedsReverification({\n            cancel,\n            complete,\n            level,\n          });\n        }\n\n        /**\n         * Wait until the promise from above have been resolved or rejected\n         */\n        await resolvers.promise;\n\n        /**\n         * After the promise resolved successfully try the original request one more time\n         */\n        result = await resolveResult(fetcher(...args));\n      }\n\n      return result;\n    }) as ExcludeClerkError<Awaited<ReturnType<Fetcher>>>;\n  }\n\n  return assertReverification;\n}\n\n/**\n * > [!WARNING]\n * >\n * > Depending on the SDK you're using, this feature requires `@clerk/nextjs@6.12.7` or later, `@clerk/clerk-react@5.25.1` or later, and `@clerk/clerk-js@5.57.1` or later.\n *\n * The `useReverification()` hook is used to handle a session's reverification flow. If a request requires reverification, a modal will display, prompting the user to verify their credentials. Upon successful verification, the original request will automatically retry.\n *\n * @function\n *\n * @returns The `useReverification()` hook returns an array with the \"enhanced\" fetcher.\n *\n * @example\n * ### Handle cancellation of the reverification process\n *\n * The following example demonstrates how to handle scenarios where a user cancels the reverification flow, such as closing the modal, which might result in `myData` being `null`.\n *\n * In the following example, `myFetcher` would be a function in your backend that fetches data from the route that requires reverification. See the [guide on how to require reverification](https://clerk.com/docs/guides/reverification) for more information.\n *\n * ```tsx {{ filename: 'src/components/MyButton.tsx' }}\n * import { useReverification } from '@clerk/clerk-react'\n * import { isReverificationCancelledError } from '@clerk/clerk-react/error'\n *\n * type MyData = {\n *   balance: number\n * }\n *\n * export function MyButton() {\n *   const fetchMyData = () => fetch('/api/balance').then(res=> res.json() as Promise<MyData>)\n *   const enhancedFetcher = useReverification(fetchMyData);\n *\n *   const handleClick = async () => {\n *     try {\n *       const myData = await enhancedFetcher()\n *       //     ^ is types as `MyData`\n *     } catch (e) {\n *       // Handle error returned from the fetcher here\n *\n *       // You can also handle cancellation with the following\n *       if (isReverificationCancelledError(err)) {\n *         // Handle the cancellation error here\n *       }\n *     }\n *   }\n *\n *   return <button onClick={handleClick}>Update User</button>\n * }\n * ```\n *\n */\nexport const useReverification: UseReverification = (fetcher, options) => {\n  const { __internal_openReverification, telemetry } = useClerk();\n  const fetcherRef = useRef(fetcher);\n  const optionsRef = useRef(options);\n\n  telemetry?.record(\n    eventMethodCalled('useReverification', {\n      onNeedsReverification: Boolean(options?.onNeedsReverification),\n    }),\n  );\n\n  // Keep fetcher and options ref in sync\n  useSafeLayoutEffect(() => {\n    fetcherRef.current = fetcher;\n    optionsRef.current = options;\n  });\n\n  return useCallback(\n    (...args) => {\n      const handler = createReverificationHandler({\n        openUIComponent: __internal_openReverification,\n        telemetry,\n        ...optionsRef.current,\n      })(fetcherRef.current);\n      return handler(...args);\n    },\n    [__internal_openReverification, telemetry],\n  );\n};\n", "import type { ClerkPaginatedResponse, ClerkResource, ForPayerType } from '@clerk/types';\n\nimport { eventMethodCalled } from '../../telemetry/events/method-called';\nimport {\n  useAssertWrappedByClerkProvider,\n  useClerkInstanceContext,\n  useOrganizationContext,\n  useUserContext,\n} from '../contexts';\nimport type { PagesOrInfiniteOptions, PaginatedHookConfig, PaginatedResources } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\n/**\n * @internal\n */\ntype CommerceHookConfig<TResource extends ClerkResource, TParams extends PagesOrInfiniteOptions> = {\n  hookName: string;\n  resourceType: string;\n  useFetcher: (\n    param: ForPayerType,\n  ) => ((params: TParams & { orgId?: string }) => Promise<ClerkPaginatedResponse<TResource>>) | undefined;\n  options?: {\n    unauthenticated?: boolean;\n  };\n};\n\n/**\n * A hook factory that creates paginated data fetching hooks for commerce-related resources.\n * It provides a standardized way to create hooks that can fetch either user or organization resources\n * with built-in pagination support.\n *\n * The generated hooks handle:\n * - Clerk authentication context\n * - Resource-specific data fetching\n * - Pagination (both traditional and infinite scroll)\n * - Telemetry tracking\n * - Type safety for the specific resource.\n *\n * @internal\n */\nexport function createCommercePaginatedHook<TResource extends ClerkResource, TParams extends PagesOrInfiniteOptions>({\n  hookName,\n  resourceType,\n  useFetcher,\n  options,\n}: CommerceHookConfig<TResource, TParams>) {\n  type HookParams = PaginatedHookConfig<PagesOrInfiniteOptions> & {\n    for: ForPayerType;\n  };\n\n  return function useCommerceHook<T extends HookParams>(\n    params?: T,\n  ): PaginatedResources<TResource, T extends { infinite: true } ? true : false> {\n    const { for: _for, ...paginationParams } = params || ({ for: 'user' } as T);\n\n    useAssertWrappedByClerkProvider(hookName);\n\n    const fetchFn = useFetcher(_for);\n\n    const safeValues = useWithSafeValues(paginationParams, {\n      initialPage: 1,\n      pageSize: 10,\n      keepPreviousData: false,\n      infinite: false,\n      __experimental_mode: undefined,\n    } as unknown as T);\n\n    const clerk = useClerkInstanceContext();\n    const user = useUserContext();\n    const { organization } = useOrganizationContext();\n\n    clerk.telemetry?.record(eventMethodCalled(hookName));\n\n    const hookParams =\n      typeof paginationParams === 'undefined'\n        ? undefined\n        : ({\n            initialPage: safeValues.initialPage,\n            pageSize: safeValues.pageSize,\n            ...(_for === 'organization' ? { orgId: organization?.id } : {}),\n          } as TParams);\n\n    const isClerkLoaded = !!(clerk.loaded && (options?.unauthenticated ? true : user));\n\n    const isEnabled = !!hookParams && isClerkLoaded;\n\n    const result = usePagesOrInfinite<TParams, ClerkPaginatedResponse<TResource>>(\n      (hookParams || {}) as TParams,\n      fetchFn,\n      {\n        keepPreviousData: safeValues.keepPreviousData,\n        infinite: safeValues.infinite,\n        enabled: isEnabled,\n        __experimental_mode: safeValues.__experimental_mode,\n      },\n      {\n        type: resourceType,\n        userId: user?.id,\n        ...(_for === 'organization' ? { orgId: organization?.id } : {}),\n      },\n    );\n\n    return result;\n  };\n}\n", "import type { CommerceStatementResource, GetStatementsParams } from '@clerk/types';\n\nimport { useClerkInstanceContext } from '../contexts';\nimport { createCommercePaginatedHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const useStatements = createCommercePaginatedHook<CommerceStatementResource, GetStatementsParams>({\n  hookName: 'useStatements',\n  resourceType: 'commerce-statements',\n  useFetcher: () => {\n    const clerk = useClerkInstanceContext();\n    return clerk.billing.getStatements;\n  },\n});\n", "import type { CommercePaymentResource, GetPaymentAttemptsParams } from '@clerk/types';\n\nimport { useClerkInstanceContext } from '../contexts';\nimport { createCommercePaginatedHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const usePaymentAttempts = createCommercePaginatedHook<CommercePaymentResource, GetPaymentAttemptsParams>({\n  hookName: 'usePaymentAttempts',\n  resourceType: 'commerce-payment-attempts',\n  useFetcher: () => {\n    const clerk = useClerkInstanceContext();\n    return clerk.billing.getPaymentAttempts;\n  },\n});\n", "import type { CommercePaymentSourceResource, GetPaymentSourcesParams } from '@clerk/types';\n\nimport { useOrganizationContext, useUserContext } from '../contexts';\nimport { createCommercePaginatedHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const usePaymentMethods = createCommercePaginatedHook<CommercePaymentSourceResource, GetPaymentSourcesParams>({\n  hookName: 'usePaymentMethods',\n  resourceType: 'commerce-payment-methods',\n  useFetcher: resource => {\n    const { organization } = useOrganizationContext();\n    const user = useUserContext();\n\n    if (resource === 'organization') {\n      return organization?.getPaymentSources;\n    }\n    return user?.getPaymentSources;\n  },\n});\n", "import type { CommercePlanResource, GetPlansParams } from '@clerk/types';\n\nimport { useClerkInstanceContext } from '../contexts';\nimport { createCommercePaginatedHook } from './createCommerceHook';\n\n/**\n * @internal\n */\nexport const usePlans = createCommercePaginatedHook<CommercePlanResource, GetPlansParams>({\n  hookName: 'usePlans',\n  resourceType: 'commerce-plans',\n  useFetcher: _for => {\n    const clerk = useClerkInstanceContext();\n    return ({ orgId, ...rest }) => {\n      // Cleanup `orgId` from the params\n      return clerk.billing.getPlans({ ...rest, for: _for });\n    };\n  },\n  options: {\n    unauthenticated: true,\n  },\n});\n", "import type { ForPayerType } from '@clerk/types';\nimport { useCallback } from 'react';\n\nimport { eventMethodCalled } from '../../telemetry/events';\nimport { useSWR } from '../clerk-swr';\nimport {\n  useAssertWrappedByClerkProvider,\n  useClerkInstanceContext,\n  useOrganizationContext,\n  useUserContext,\n} from '../contexts';\n\nconst hookName = 'useSubscription';\n\ntype UseSubscriptionParams = {\n  for?: ForPayerType;\n  /**\n   * If `true`, the previous data will be kept in the cache until new data is fetched.\n   *\n   * @default false\n   */\n  keepPreviousData?: boolean;\n};\n\n/**\n * @internal\n *\n * @experimental This is an experimental API for the Billing feature that is available under a public beta, and the API is subject to change.\n *\n * Fetches subscription data for the current user or organization.\n */\nexport const useSubscription = (params?: UseSubscriptionParams) => {\n  useAssertWrappedByClerkProvider(hookName);\n\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n  const { organization } = useOrganizationContext();\n\n  clerk.telemetry?.record(eventMethodCalled(hookName));\n\n  const swr = useSWR(\n    user?.id\n      ? {\n          type: 'commerce-subscription',\n          userId: user.id,\n          args: { orgId: params?.for === 'organization' ? organization?.id : undefined },\n        }\n      : null,\n    ({ args }) => clerk.billing.getSubscription(args),\n    {\n      dedupingInterval: 1_000 * 60,\n      keepPreviousData: params?.keepPreviousData,\n    },\n  );\n\n  const revalidate = useCallback(() => swr.mutate(), [swr.mutate]);\n\n  return {\n    data: swr.data,\n    error: swr.error,\n    isLoading: swr.isLoading,\n    isFetching: swr.isValidating,\n    revalidate,\n  };\n};\n", "import type {\n  __experimental_CheckoutCacheState,\n  __experimental_CheckoutInstance,\n  CommerceCheckoutResource,\n  SetActiveNavigate,\n} from '@clerk/types';\nimport { useMemo, useSyncExternalStore } from 'react';\n\nimport type { ClerkAPIResponseError } from '../..';\nimport type { __experimental_CheckoutProvider } from '../contexts';\nimport { useCheckoutContext } from '../contexts';\nimport { useClerk } from './useClerk';\nimport { useOrganization } from './useOrganization';\nimport { useUser } from './useUser';\n\n/**\n * Utility type that removes function properties from a type.\n */\ntype RemoveFunctions<T> = {\n  [K in keyof T as T[K] extends (...args: any[]) => any ? never : K]: T[K];\n};\n\n/**\n * Utility type that makes all properties `null`.\n */\ntype ForceNull<T> = {\n  [K in keyof T]: null;\n};\n\ntype CheckoutProperties = Omit<RemoveFunctions<CommerceCheckoutResource>, 'pathRoot' | 'status'>;\n\ntype FetchStatusAndError =\n  | {\n      error: ClerkAPIResponseError;\n      fetchStatus: 'error';\n    }\n  | {\n      error: null;\n      fetchStatus: 'idle' | 'fetching';\n    };\n\n/**\n * @internal\n * On status === 'needs_initialization', all properties are null.\n * On status === 'needs_confirmation' or 'completed', all properties are defined the same as the CommerceCheckoutResource.\n */\ntype CheckoutPropertiesPerStatus =\n  | ({\n      status: Extract<__experimental_CheckoutCacheState['status'], 'needs_initialization'>;\n    } & ForceNull<CheckoutProperties>)\n  | ({\n      status: Extract<__experimental_CheckoutCacheState['status'], 'needs_confirmation' | 'completed'>;\n    } & CheckoutProperties);\n\ntype __experimental_UseCheckoutReturn = {\n  checkout: FetchStatusAndError &\n    CheckoutPropertiesPerStatus & {\n      confirm: __experimental_CheckoutInstance['confirm'];\n      start: __experimental_CheckoutInstance['start'];\n      clear: () => void;\n      finalize: (params?: { navigate?: SetActiveNavigate }) => void;\n      getState: () => __experimental_CheckoutCacheState;\n      isStarting: boolean;\n      isConfirming: boolean;\n    };\n};\n\ntype Params = Parameters<typeof __experimental_CheckoutProvider>[0];\n\nexport const useCheckout = (options?: Params): __experimental_UseCheckoutReturn => {\n  const contextOptions = useCheckoutContext();\n  const { for: forOrganization, planId, planPeriod } = options || contextOptions;\n\n  const clerk = useClerk();\n  const { organization } = useOrganization();\n  const { isLoaded, user } = useUser();\n\n  if (!isLoaded) {\n    throw new Error('Clerk: Ensure that `useCheckout` is inside a component wrapped with `<ClerkLoaded />`.');\n  }\n\n  if (!user) {\n    throw new Error('Clerk: Ensure that `useCheckout` is inside a component wrapped with `<SignedIn />`.');\n  }\n\n  if (forOrganization === 'organization' && !organization) {\n    throw new Error('Clerk: Wrap your flow with a check for an active organization');\n  }\n\n  const manager = useMemo(\n    () => clerk.__experimental_checkout({ planId, planPeriod, for: forOrganization }),\n    [user.id, organization?.id, planId, planPeriod, forOrganization],\n  );\n\n  const managerProperties = useSyncExternalStore(\n    cb => manager.subscribe(cb),\n    () => manager.getState(),\n    () => manager.getState(),\n  );\n\n  const properties = useMemo<CheckoutProperties | ForceNull<CheckoutProperties>>(() => {\n    if (!managerProperties.checkout) {\n      return {\n        id: null,\n        externalClientSecret: null,\n        externalGatewayId: null,\n        status: null,\n        totals: null,\n        isImmediatePlanChange: null,\n        planPeriod: null,\n        plan: null,\n        paymentSource: null,\n        freeTrialEndsAt: null,\n      };\n    }\n    const {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      reload,\n      confirm,\n      pathRoot,\n      // All the above need to be removed from the properties\n      ...rest\n    } = managerProperties.checkout;\n    return rest;\n  }, [managerProperties.checkout]);\n\n  const checkout = {\n    ...properties,\n    getState: manager.getState,\n    start: manager.start,\n    confirm: manager.confirm,\n    clear: manager.clear,\n    finalize: manager.finalize,\n    isStarting: managerProperties.isStarting,\n    isConfirming: managerProperties.isConfirming,\n    error: managerProperties.error,\n    status: managerProperties.status,\n    fetchStatus: managerProperties.fetchStatus,\n  };\n\n  return {\n    checkout,\n  } as __experimental_UseCheckoutReturn;\n};\n", "/* eslint-disable @typescript-eslint/consistent-type-imports */\nimport type { CommerceCheckoutResource, EnvironmentResource, ForPayerType } from '@clerk/types';\nimport type { Stripe, StripeElements } from '@stripe/stripe-js';\nimport { type PropsWithChildren, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';\nimport React from 'react';\nimport useSWR from 'swr';\nimport useSWRMutation from 'swr/mutation';\n\nimport { createContextAndHook } from './hooks/createContextAndHook';\nimport type { useCheckout } from './hooks/useCheckout';\nimport { useClerk } from './hooks/useClerk';\nimport { useOrganization } from './hooks/useOrganization';\nimport { useUser } from './hooks/useUser';\nimport { Elements, PaymentElement as StripePaymentElement, useElements, useStripe } from './stripe-react';\n\ntype LoadStripeFn = typeof import('@stripe/stripe-js').loadStripe;\n\ntype PaymentElementError = {\n  gateway: 'stripe';\n  error: {\n    /**\n     * For some errors that could be handled programmatically, a short string indicating the [error code](https://stripe.com/docs/error-codes) reported.\n     */\n    code?: string;\n    message?: string;\n    type: string;\n  };\n};\n\nconst [StripeLibsContext, useStripeLibsContext] = createContextAndHook<{\n  loadStripe: LoadStripeFn;\n} | null>('StripeLibsContext');\n\nconst StripeLibsProvider = ({ children }: PropsWithChildren) => {\n  const clerk = useClerk();\n  const { data: stripeClerkLibs } = useSWR(\n    'clerk-stripe-sdk',\n    async () => {\n      const loadStripe = (await clerk.__internal_loadStripeJs()) as LoadStripeFn;\n      return { loadStripe };\n    },\n    {\n      keepPreviousData: true,\n      revalidateOnFocus: false,\n      dedupingInterval: Infinity,\n    },\n  );\n\n  return (\n    <StripeLibsContext.Provider\n      value={{\n        value: stripeClerkLibs || null,\n      }}\n    >\n      {children}\n    </StripeLibsContext.Provider>\n  );\n};\n\nconst useInternalEnvironment = () => {\n  const clerk = useClerk();\n  // @ts-expect-error `__unstable__environment` is not typed\n  return clerk.__unstable__environment as unknown as EnvironmentResource | null | undefined;\n};\n\nconst usePaymentSourceUtils = (forResource: ForPayerType = 'user') => {\n  const { organization } = useOrganization();\n  const { user } = useUser();\n  const resource = forResource === 'organization' ? organization : user;\n  const stripeClerkLibs = useStripeLibsContext();\n\n  const { data: initializedPaymentSource, trigger: initializePaymentSource } = useSWRMutation(\n    {\n      key: 'commerce-payment-source-initialize',\n      resourceId: resource?.id,\n    },\n    () => {\n      return resource?.initializePaymentSource({\n        gateway: 'stripe',\n      });\n    },\n  );\n\n  const environment = useInternalEnvironment();\n\n  useEffect(() => {\n    if (!resource?.id) return;\n    initializePaymentSource().catch(() => {\n      // ignore errors\n    });\n  }, [resource?.id]);\n\n  const externalGatewayId = initializedPaymentSource?.externalGatewayId;\n  const externalClientSecret = initializedPaymentSource?.externalClientSecret;\n  const paymentMethodOrder = initializedPaymentSource?.paymentMethodOrder;\n  const stripePublishableKey = environment?.commerceSettings.billing.stripePublishableKey;\n\n  const { data: stripe } = useSWR(\n    stripeClerkLibs && externalGatewayId && stripePublishableKey\n      ? { key: 'stripe-sdk', externalGatewayId, stripePublishableKey }\n      : null,\n    ({ stripePublishableKey, externalGatewayId }) => {\n      return stripeClerkLibs?.loadStripe(stripePublishableKey, {\n        stripeAccount: externalGatewayId,\n      });\n    },\n    {\n      keepPreviousData: true,\n      revalidateOnFocus: false,\n      dedupingInterval: 1_000 * 60, // 1 minute\n    },\n  );\n\n  return {\n    stripe,\n    initializePaymentSource,\n    externalClientSecret,\n    paymentMethodOrder,\n  };\n};\n\ntype internalStripeAppearance = {\n  colorPrimary: string;\n  colorBackground: string;\n  colorText: string;\n  colorTextSecondary: string;\n  colorSuccess: string;\n  colorDanger: string;\n  colorWarning: string;\n  fontWeightNormal: string;\n  fontWeightMedium: string;\n  fontWeightBold: string;\n  fontSizeXl: string;\n  fontSizeLg: string;\n  fontSizeSm: string;\n  fontSizeXs: string;\n  borderRadius: string;\n  spacingUnit: string;\n};\n\ntype PaymentElementProviderProps = {\n  checkout?: CommerceCheckoutResource | ReturnType<typeof useCheckout>['checkout'];\n  stripeAppearance?: internalStripeAppearance;\n  /**\n   * Default to `user` if not provided.\n   *\n   * @default 'user'\n   */\n  for?: ForPayerType;\n  paymentDescription?: string;\n};\n\nconst [PaymentElementContext, usePaymentElementContext] = createContextAndHook<\n  ReturnType<typeof usePaymentSourceUtils> &\n    PaymentElementProviderProps & {\n      setIsPaymentElementReady: (isPaymentElementReady: boolean) => void;\n      isPaymentElementReady: boolean;\n    }\n>('PaymentElementContext');\n\nconst [StripeUtilsContext, useStripeUtilsContext] = createContextAndHook<{\n  stripe: Stripe | undefined | null;\n  elements: StripeElements | undefined | null;\n}>('StripeUtilsContext');\n\nconst ValidateStripeUtils = ({ children }: PropsWithChildren) => {\n  const stripe = useStripe();\n  const elements = useElements();\n\n  return <StripeUtilsContext.Provider value={{ value: { stripe, elements } }}>{children}</StripeUtilsContext.Provider>;\n};\n\nconst DummyStripeUtils = ({ children }: PropsWithChildren) => {\n  return <StripeUtilsContext.Provider value={{ value: {} as any }}>{children}</StripeUtilsContext.Provider>;\n};\n\nconst PropsProvider = ({ children, ...props }: PropsWithChildren<PaymentElementProviderProps>) => {\n  const utils = usePaymentSourceUtils(props.for);\n  const [isPaymentElementReady, setIsPaymentElementReady] = useState(false);\n  return (\n    <PaymentElementContext.Provider\n      value={{\n        value: {\n          ...props,\n          ...utils,\n          setIsPaymentElementReady,\n          isPaymentElementReady,\n        },\n      }}\n    >\n      {children}\n    </PaymentElementContext.Provider>\n  );\n};\n\nconst PaymentElementProvider = ({ children, ...props }: PropsWithChildren<PaymentElementProviderProps>) => {\n  return (\n    <StripeLibsProvider>\n      <PropsProvider {...props}>\n        <PaymentElementInternalRoot>{children}</PaymentElementInternalRoot>\n      </PropsProvider>\n    </StripeLibsProvider>\n  );\n};\n\nconst PaymentElementInternalRoot = (props: PropsWithChildren) => {\n  const { stripe, externalClientSecret, stripeAppearance } = usePaymentElementContext();\n\n  if (stripe && externalClientSecret) {\n    return (\n      <Elements\n        // This key is used to reset the payment intent, since Stripe doesn't provide a way to reset the payment intent.\n        key={externalClientSecret}\n        stripe={stripe}\n        options={{\n          loader: 'never',\n          clientSecret: externalClientSecret,\n          appearance: {\n            variables: stripeAppearance,\n          },\n        }}\n      >\n        <ValidateStripeUtils>{props.children}</ValidateStripeUtils>\n      </Elements>\n    );\n  }\n\n  return <DummyStripeUtils>{props.children}</DummyStripeUtils>;\n};\n\nconst PaymentElement = ({ fallback }: { fallback?: ReactNode }) => {\n  const {\n    setIsPaymentElementReady,\n    paymentMethodOrder,\n    checkout,\n    stripe,\n    externalClientSecret,\n    paymentDescription,\n    for: _for,\n  } = usePaymentElementContext();\n  const environment = useInternalEnvironment();\n\n  const applePay = useMemo(() => {\n    if (!checkout || !checkout.totals || !checkout.plan) {\n      return undefined;\n    }\n\n    return {\n      recurringPaymentRequest: {\n        paymentDescription: paymentDescription || '',\n        managementURL:\n          _for === 'organization'\n            ? environment?.displayConfig.organizationProfileUrl || ''\n            : environment?.displayConfig.userProfileUrl || '',\n        regularBilling: {\n          amount: checkout.totals.totalDueNow?.amount || checkout.totals.grandTotal.amount,\n          label: checkout.plan.name,\n          recurringPaymentIntervalUnit: checkout.planPeriod === 'annual' ? 'year' : 'month',\n        },\n      },\n    } as const;\n  }, [checkout, paymentDescription, _for, environment]);\n\n  const options = useMemo(() => {\n    return {\n      layout: {\n        type: 'tabs',\n        defaultCollapsed: false,\n      },\n      paymentMethodOrder,\n      applePay,\n    } as const;\n  }, [applePay, paymentMethodOrder]);\n\n  const onReady = useCallback(() => {\n    setIsPaymentElementReady(true);\n  }, [setIsPaymentElementReady]);\n\n  if (!stripe || !externalClientSecret) {\n    return <>{fallback}</>;\n  }\n\n  return (\n    <StripePaymentElement\n      fallback={fallback}\n      onReady={onReady}\n      options={options}\n    />\n  );\n};\n\nconst throwLibsMissingError = () => {\n  throw new Error(\n    'Clerk: Unable to submit, Stripe libraries are not yet loaded. Be sure to check `isFormReady` before calling `submit`.',\n  );\n};\n\ntype UsePaymentElementReturn = {\n  submit: () => Promise<\n    | {\n        data: { gateway: 'stripe'; paymentToken: string };\n        error: null;\n      }\n    | {\n        data: null;\n        error: PaymentElementError;\n      }\n  >;\n  reset: () => Promise<void>;\n  isFormReady: boolean;\n} & (\n  | {\n      provider: {\n        name: 'stripe';\n      };\n      isProviderReady: true;\n    }\n  | {\n      provider: undefined;\n      isProviderReady: false;\n    }\n);\n\nconst usePaymentElement = (): UsePaymentElementReturn => {\n  const { isPaymentElementReady, initializePaymentSource } = usePaymentElementContext();\n  const { stripe, elements } = useStripeUtilsContext();\n  const { externalClientSecret } = usePaymentElementContext();\n\n  const submit = useCallback(async () => {\n    if (!stripe || !elements) {\n      return throwLibsMissingError();\n    }\n\n    const { setupIntent, error } = await stripe.confirmSetup({\n      elements,\n      confirmParams: {\n        return_url: window.location.href,\n      },\n      redirect: 'if_required',\n    });\n    if (error) {\n      return {\n        data: null,\n        error: {\n          gateway: 'stripe',\n          error: {\n            code: error.code,\n            message: error.message,\n            type: error.type,\n          },\n        },\n      } as const;\n    }\n    return {\n      data: { gateway: 'stripe', paymentToken: setupIntent.payment_method as string },\n      error: null,\n    } as const;\n  }, [stripe, elements]);\n\n  const reset = useCallback(async () => {\n    if (!stripe || !elements) {\n      return throwLibsMissingError();\n    }\n\n    await initializePaymentSource();\n  }, [stripe, elements, initializePaymentSource]);\n\n  const isProviderReady = Boolean(stripe && externalClientSecret);\n\n  if (!isProviderReady) {\n    return {\n      submit: throwLibsMissingError,\n      reset: throwLibsMissingError,\n      isFormReady: false,\n      provider: undefined,\n      isProviderReady: false,\n    };\n  }\n  return {\n    submit,\n    reset,\n    isFormReady: isPaymentElementReady,\n    provider: {\n      name: 'stripe',\n    },\n    isProviderReady: isProviderReady,\n  };\n};\n\nexport {\n  PaymentElementProvider as __experimental_PaymentElementProvider,\n  PaymentElement as __experimental_PaymentElement,\n  usePaymentElement as __experimental_usePaymentElement,\n};\n", "/**\n * Original source: https://github.com/stripe/react-stripe-js.\n *\n * The current version of this file is a fork of the original version.\n * The main difference is that we have kept only the necessary parts of the file.\n * This is because we don't need it and it's not used in the Clerk codebase.\n *\n * The original version of this file is licensed under the MIT license.\n * Https://github.com/stripe/react-stripe-js/blob/master/LICENSE.\n */\n\nimport type { ElementProps, PaymentElementProps } from '@stripe/react-stripe-js';\nimport type {\n  Stripe,\n  StripeElement,\n  StripeElements,\n  StripeElementsOptions,\n  StripeElementType,\n} from '@stripe/stripe-js';\nimport type { FunctionComponent, PropsWithChildren, ReactNode } from 'react';\nimport React, { useState } from 'react';\n\nimport { useAttachEvent, usePrevious } from './utils';\n\ninterface ElementsContextValue {\n  elements: StripeElements | null;\n  stripe: Stripe | null;\n}\n\nconst ElementsContext = React.createContext<ElementsContextValue | null>(null);\nElementsContext.displayName = 'ElementsContext';\n\nconst parseElementsContext = (ctx: ElementsContextValue | null, useCase: string): ElementsContextValue => {\n  if (!ctx) {\n    throw new Error(\n      `Could not find Elements context; You need to wrap the part of your app that ${useCase} in an <Elements> provider.`,\n    );\n  }\n\n  return ctx;\n};\n\ninterface ElementsProps {\n  /**\n   * A [Stripe object](https://stripe.com/docs/js/initializing) or a `Promise` resolving to a `Stripe` object.\n   * The easiest way to initialize a `Stripe` object is with the the [Stripe.js wrapper module](https://github.com/stripe/stripe-js/blob/master/README.md#readme).\n   * Once this prop has been set, it can not be changed.\n   *\n   * You can also pass in `null` or a `Promise` resolving to `null` if you are performing an initial server-side render or when generating a static site.\n   */\n  stripe: PromiseLike<Stripe | null> | Stripe | null;\n\n  /**\n   * Optional [Elements configuration options](https://stripe.com/docs/js/elements_object/create).\n   * Once the stripe prop has been set, these options cannot be changed.\n   */\n  options?: StripeElementsOptions;\n}\n\ntype UnknownOptions = { [k: string]: unknown };\n\ninterface PrivateElementsProps {\n  stripe: unknown;\n  options?: UnknownOptions;\n  children?: ReactNode;\n}\n\n/**\n * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n *\n * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n * Pass the returned `Promise` to `Elements`.\n *\n * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n */\nconst Elements: FunctionComponent<PropsWithChildren<ElementsProps>> = (({\n  stripe: rawStripeProp,\n  options,\n  children,\n}: PrivateElementsProps) => {\n  const parsed = React.useMemo(() => parseStripeProp(rawStripeProp), [rawStripeProp]);\n\n  // For a sync stripe instance, initialize into context\n  const [ctx, setContext] = React.useState<ElementsContextValue>(() => ({\n    stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n    elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null,\n  }));\n\n  React.useEffect(() => {\n    let isMounted = true;\n\n    const safeSetContext = (stripe: Stripe) => {\n      setContext(ctx => {\n        // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n        if (ctx.stripe) return ctx;\n        return {\n          stripe,\n          elements: stripe.elements(options),\n        };\n      });\n    };\n\n    // For an async stripePromise, store it in context once resolved\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(stripe => {\n        if (stripe && isMounted) {\n          // Only update Elements context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          safeSetContext(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !ctx.stripe) {\n      // Or, handle a sync stripe instance going from null -> populated\n      safeSetContext(parsed.stripe);\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options]);\n\n  // Warn on changes to stripe prop\n  const prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(() => {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]);\n\n  // Apply updates to elements when options prop has relevant changes\n  const prevOptions = usePrevious(options);\n  React.useEffect(() => {\n    if (!ctx.elements) {\n      return;\n    }\n\n    const updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n    if (updates) {\n      ctx.elements.update(updates);\n    }\n  }, [options, prevOptions, ctx.elements]);\n\n  return <ElementsContext.Provider value={ctx}>{children}</ElementsContext.Provider>;\n}) as FunctionComponent<PropsWithChildren<ElementsProps>>;\n\nconst useElementsContextWithUseCase = (useCaseMessage: string): ElementsContextValue => {\n  const ctx = React.useContext(ElementsContext);\n  return parseElementsContext(ctx, useCaseMessage);\n};\n\nconst useElements = (): StripeElements | null => {\n  const { elements } = useElementsContextWithUseCase('calls useElements()');\n  return elements;\n};\n\nconst INVALID_STRIPE_ERROR =\n  'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n\n// We are using types to enforce the `stripe` prop in this lib, but in a real\n// integration `stripe` could be anything, so we need to do some sanity\n// validation to prevent type errors.\nconst validateStripe = (maybeStripe: unknown, errorMsg = INVALID_STRIPE_ERROR): null | Stripe => {\n  if (maybeStripe === null || isStripe(maybeStripe)) {\n    return maybeStripe;\n  }\n\n  throw new Error(errorMsg);\n};\n\ntype ParsedStripeProp =\n  | { tag: 'empty' }\n  | { tag: 'sync'; stripe: Stripe }\n  | { tag: 'async'; stripePromise: Promise<Stripe | null> };\n\nconst parseStripeProp = (raw: unknown, errorMsg = INVALID_STRIPE_ERROR): ParsedStripeProp => {\n  if (isPromise(raw)) {\n    return {\n      tag: 'async',\n      stripePromise: Promise.resolve(raw).then(result => validateStripe(result, errorMsg)),\n    };\n  }\n\n  const stripe = validateStripe(raw, errorMsg);\n\n  if (stripe === null) {\n    return { tag: 'empty' };\n  }\n\n  return { tag: 'sync', stripe };\n};\n\nconst isUnknownObject = (raw: unknown): raw is { [key in PropertyKey]: unknown } => {\n  return raw !== null && typeof raw === 'object';\n};\n\nconst isPromise = (raw: unknown): raw is PromiseLike<unknown> => {\n  return isUnknownObject(raw) && typeof raw.then === 'function';\n};\n\n// We are using types to enforce the `stripe` prop in this lib,\n// but in an untyped integration `stripe` could be anything, so we need\n// to do some sanity validation to prevent type errors.\nconst isStripe = (raw: unknown): raw is Stripe => {\n  return (\n    isUnknownObject(raw) &&\n    typeof raw.elements === 'function' &&\n    typeof raw.createToken === 'function' &&\n    typeof raw.createPaymentMethod === 'function' &&\n    typeof raw.confirmCardPayment === 'function'\n  );\n};\n\nconst extractAllowedOptionsUpdates = (\n  options: unknown | void,\n  prevOptions: unknown | void,\n  immutableKeys: string[],\n): UnknownOptions | null => {\n  if (!isUnknownObject(options)) {\n    return null;\n  }\n\n  return Object.keys(options).reduce((newOptions: null | UnknownOptions, key) => {\n    const isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n    if (immutableKeys.includes(key)) {\n      if (isUpdated) {\n        console.warn(`Unsupported prop change: options.${key} is not a mutable property.`);\n      }\n\n      return newOptions;\n    }\n\n    if (!isUpdated) {\n      return newOptions;\n    }\n\n    return { ...(newOptions || {}), [key]: options[key] };\n  }, null);\n};\n\nconst PLAIN_OBJECT_STR = '[object Object]';\n\nconst isEqual = (left: unknown, right: unknown): boolean => {\n  if (!isUnknownObject(left) || !isUnknownObject(right)) {\n    return left === right;\n  }\n\n  const leftArray = Array.isArray(left);\n  const rightArray = Array.isArray(right);\n\n  if (leftArray !== rightArray) return false;\n\n  const leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n  const rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n\n  if (leftPlainObject !== rightPlainObject) return false;\n\n  // not sure what sort of special object this is (regexp is one option), so\n  // fallback to reference check.\n  if (!leftPlainObject && !leftArray) return left === right;\n\n  const leftKeys = Object.keys(left);\n  const rightKeys = Object.keys(right);\n\n  if (leftKeys.length !== rightKeys.length) return false;\n\n  const keySet: { [key: string]: boolean } = {};\n  for (let i = 0; i < leftKeys.length; i += 1) {\n    keySet[leftKeys[i]] = true;\n  }\n  for (let i = 0; i < rightKeys.length; i += 1) {\n    keySet[rightKeys[i]] = true;\n  }\n  const allKeys = Object.keys(keySet);\n  if (allKeys.length !== leftKeys.length) {\n    return false;\n  }\n\n  const l = left;\n  const r = right;\n  const pred = (key: string): boolean => {\n    return isEqual(l[key], r[key]);\n  };\n\n  return allKeys.every(pred);\n};\n\nconst useStripe = (): Stripe | null => {\n  const { stripe } = useElementsOrCheckoutSdkContextWithUseCase('calls useStripe()');\n  return stripe;\n};\n\nconst useElementsOrCheckoutSdkContextWithUseCase = (useCaseString: string): ElementsContextValue => {\n  const elementsContext = React.useContext(ElementsContext);\n\n  return parseElementsContext(elementsContext, useCaseString);\n};\n\ntype UnknownCallback = (...args: unknown[]) => any;\n\ninterface PrivateElementProps {\n  id?: string;\n  className?: string;\n  fallback?: ReactNode;\n  onChange?: UnknownCallback;\n  onBlur?: UnknownCallback;\n  onFocus?: UnknownCallback;\n  onEscape?: UnknownCallback;\n  onReady?: UnknownCallback;\n  onClick?: UnknownCallback;\n  onLoadError?: UnknownCallback;\n  onLoaderStart?: UnknownCallback;\n  onNetworksChange?: UnknownCallback;\n  onConfirm?: UnknownCallback;\n  onCancel?: UnknownCallback;\n  onShippingAddressChange?: UnknownCallback;\n  onShippingRateChange?: UnknownCallback;\n  options?: UnknownOptions;\n}\n\nconst capitalized = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);\n\nconst createElementComponent = (type: StripeElementType, isServer: boolean): FunctionComponent<ElementProps> => {\n  const displayName = `${capitalized(type)}Element`;\n\n  const ClientElement: FunctionComponent<PrivateElementProps> = ({\n    id,\n    className,\n    fallback,\n    options = {},\n    onBlur,\n    onFocus,\n    onReady,\n    onChange,\n    onEscape,\n    onClick,\n    onLoadError,\n    onLoaderStart,\n    onNetworksChange,\n    onConfirm,\n    onCancel,\n    onShippingAddressChange,\n    onShippingRateChange,\n  }) => {\n    const ctx = useElementsOrCheckoutSdkContextWithUseCase(`mounts <${displayName}>`);\n    const elements = 'elements' in ctx ? ctx.elements : null;\n    const [element, setElement] = React.useState<StripeElement | null>(null);\n    const elementRef = React.useRef<StripeElement | null>(null);\n    const domNode = React.useRef<HTMLDivElement | null>(null);\n    const [isReady, setReady] = useState(false);\n\n    // For every event where the merchant provides a callback, call element.on\n    // with that callback. If the merchant ever changes the callback, removes\n    // the old callback with element.off and then call element.on with the new one.\n    useAttachEvent(element, 'blur', onBlur);\n    useAttachEvent(element, 'focus', onFocus);\n    useAttachEvent(element, 'escape', onEscape);\n    useAttachEvent(element, 'click', onClick);\n    useAttachEvent(element, 'loaderror', onLoadError);\n    useAttachEvent(element, 'loaderstart', onLoaderStart);\n    useAttachEvent(element, 'networkschange', onNetworksChange);\n    useAttachEvent(element, 'confirm', onConfirm);\n    useAttachEvent(element, 'cancel', onCancel);\n    useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n    useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n    useAttachEvent(element, 'change', onChange);\n\n    let readyCallback: UnknownCallback | undefined;\n    if (onReady) {\n      // For other Elements, pass through the Element itself.\n      readyCallback = () => {\n        setReady(true);\n        onReady(element);\n      };\n    }\n\n    useAttachEvent(element, 'ready', readyCallback);\n\n    React.useLayoutEffect(() => {\n      if (elementRef.current === null && domNode.current !== null && elements) {\n        let newElement: StripeElement | null = null;\n        if (elements) {\n          newElement = elements.create(type as any, options);\n        }\n\n        // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n        elementRef.current = newElement;\n        // Store element in state to facilitate event listener attachment\n        setElement(newElement);\n\n        if (newElement) {\n          newElement.mount(domNode.current);\n        }\n      }\n    }, [elements, options]);\n\n    const prevOptions = usePrevious(options);\n    React.useEffect(() => {\n      if (!elementRef.current) {\n        return;\n      }\n\n      const updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n      if (updates && 'update' in elementRef.current) {\n        elementRef.current.update(updates);\n      }\n    }, [options, prevOptions]);\n\n    React.useLayoutEffect(() => {\n      return () => {\n        if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n          try {\n            elementRef.current.destroy();\n            elementRef.current = null;\n          } catch {\n            // Do nothing\n          }\n        }\n      };\n    }, []);\n\n    return (\n      <>\n        {!isReady && fallback}\n        <div\n          id={id}\n          style={{\n            height: isReady ? 'unset' : '0px',\n            visibility: isReady ? 'visible' : 'hidden',\n          }}\n          className={className}\n          ref={domNode}\n        />\n      </>\n    );\n  };\n\n  // Only render the Element wrapper in a server environment.\n  const ServerElement: FunctionComponent<PrivateElementProps> = props => {\n    useElementsOrCheckoutSdkContextWithUseCase(`mounts <${displayName}>`);\n    const { id, className } = props;\n    return (\n      <div\n        id={id}\n        className={className}\n      />\n    );\n  };\n\n  const Element = isServer ? ServerElement : ClientElement;\n  Element.displayName = displayName;\n  (Element as any).__elementType = type;\n\n  return Element as FunctionComponent<ElementProps>;\n};\n\nconst isServer = typeof window === 'undefined';\nconst PaymentElement: FunctionComponent<\n  PaymentElementProps & {\n    fallback?: ReactNode;\n  }\n> = createElementComponent('payment', isServer);\n\nexport { Elements, useElements, useStripe, PaymentElement };\n", "import type { StripeElement } from '@stripe/stripe-js';\nimport { useEffect, useRef } from 'react';\n\nexport const usePrevious = <T>(value: T): T => {\n  const ref = useRef(value);\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n};\n\nexport const useAttachEvent = <A extends unknown[]>(\n  element: StripeElement | null,\n  event: string,\n  cb?: (...args: A) => any,\n) => {\n  const cbDefined = !!cb;\n  const cbRef = useRef(cb);\n\n  // In many integrations the callback prop changes on each render.\n  // Using a ref saves us from calling element.on/.off every render.\n  useEffect(() => {\n    cbRef.current = cb;\n  }, [cb]);\n\n  useEffect(() => {\n    if (!cbDefined || !element) {\n      return () => {};\n    }\n\n    const decoratedCb = (...args: A): void => {\n      if (cbRef.current) {\n        cbRef.current(...args);\n      }\n    };\n\n    (element as any).on(event, decoratedCb);\n\n    return () => {\n      (element as any).off(event, decoratedCb);\n    };\n  }, [cbDefined, event, element, cbRef]);\n};\n"], "names": ["React", "default", "React", "default", "undefinedPaginatedResource", "React", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "React", "useCallback", "useRef", "useRef", "useCallback", "<PERSON><PERSON><PERSON>", "useCallback", "<PERSON><PERSON><PERSON>", "default", "useCallback", "useMemo", "useMemo", "useCallback", "useEffect", "useMemo", "useState", "React", "React", "useState", "useRef", "React", "ctx", "isServer", "useState", "React", "useEffect", "stripePublishableKey", "externalGatewayId", "useState", "PaymentElement", "useMemo", "useCallback"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,OAAO,WAAW;AEClB,0BAAc;AAEd,SAAoB,WAAXC,gBAAyB;AAClC,SAAoB,WAAXA,gBAAiC;;ASL1C,SAAS,UAAU,iBAAiB;ASMpC,OAAO,oBAAoB;;;;;;;;;;;;;;;ApBEpB,SAAS,oBAAoB,UAAA,EAAqB,QAAA,EAA2D;IAClH,IAAI,CAAC,YAAY;QACf,MAAM,OAAO,aAAa,WAAW,IAAI,MAAM,QAAQ,IAAI,IAAI,MAAM,GAAG,SAAS,WAAW,CAAA,UAAA,CAAY;IAC1G;AACF;AAeO,IAAM,uBAAuB,CAClC,aACA,YAC8E;IAC9E,MAAM,EAAE,cAAc,mBAAA,CAAoB,CAAA,GAAI,WAAW,CAAC;IAC1D,MAAM,MAAM,4WAAA,CAAM,aAAA,CAA6C,KAAA,CAAS;IACxE,IAAI,WAAA,GAAc;IAElB,MAAM,SAAS,MAAM;QACnB,MAAM,MAAM,4WAAA,CAAM,UAAA,CAAW,GAAG;QAChC,YAAY,KAAK,GAAG,WAAW,CAAA,UAAA,CAAY;QAC3C,OAAQ,IAAY,KAAA;IACtB;IAEA,MAAM,yBAAyB,MAAM;QACnC,MAAM,MAAM,4WAAA,CAAM,UAAA,CAAW,GAAG;QAChC,OAAO,MAAM,IAAI,KAAA,GAAQ,CAAC;IAC5B;IAEA,OAAO;QAAC;QAAK;QAAQ,sBAAsB;KAAA;AAC7C;;;AE/CA,IAAA,oBAAA,CAAA;IAAA,6RAAA,EAAA,mBAAA;IAAA,QAAA,IAAAA,+OAAAA;IAAA,gBAAA,IAAAA,kOAAAA;AAAA;IAEA,+RAAA,EAAA,mBAAA;;;;;ADgBA,IAAM,CAAC,sBAAsB,uBAAuB,CAAA,GAAI,qBAAkC,sBAAsB;AAChH,IAAM,CAAC,aAAa,cAAc,CAAA,GAAI,qBAAsD,aAAa;AACzG,IAAM,CAAC,eAAe,gBAAgB,CAAA,GAAI,qBAAwD,eAAe;AACjH,IAAM,CAAC,gBAAgB,iBAAiB,CAAA,GAAI,qBAC1C;AAGF,IAAM,iBAAiBC,4WAAAA,CAAM,aAAA,CAA4B,CAAC,CAAC;AAQ3D,IAAM,CAAC,iBAAiB,kBAAkB,CAAA,GAAI,qBAAyC,iBAAiB;AAExG,IAAM,kCAAkC,CAAC,EAAE,QAAA,EAAU,GAAG,KAAK,CAAA,KAA6C;IACxG,OAAO,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,gBAAgB,QAAA,EAAhB;QAAyB,OAAO;YAAE,OAAO;QAAK;IAAA,GAAI,QAAS;AACrE;AAKA,SAAS,oBAAkC;IACzC,MAAM,UAAUA,4WAAAA,CAAM,UAAA,CAAW,cAAc;IAC/C,IAAI,YAAY,KAAA,GAAW;QACzB,MAAM,IAAI,MAAM,kDAAkD;IACpE;IACA,OAAO;AACT;AAKA,IAAM,CAAC,6BAA6B,sBAAsB,CAAA,GAAI,qBAE3D,qBAAqB;AAExB,IAAM,uBAAuB,CAAC,EAC5B,QAAA,EACA,YAAA,EACA,SAAA,EACF,KAKM;IACJ,OACE,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,kBAAA,SAAA,EAAA;QAAU,OAAO;IAAA,GAChB,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,4BAA4B,QAAA,EAA5B;QACC,OAAO;YACL,OAAO;gBAAE;YAAa;QACxB;IAAA,GAEC;AAIT;AAKA,SAAS,gCAAgC,eAAA,EAA8C;IACrF,MAAM,MAAMA,4WAAAA,CAAM,UAAA,CAAW,oBAAoB;IAEjD,IAAI,CAAC,KAAK;QACR,IAAI,OAAO,oBAAoB,YAAY;YACzC,gBAAgB;YAChB;QACF;QAEA,MAAM,IAAI,MACR,GAAG,eAAe,CAAA;;;;;;4DAAA,CAAA,CAMsC,IAAA,CAAK;IAEjE;AACF;;AEtEA,SAAS,iBAAiB,IAAA,EAA+B,IAAA,EAAwD;IAC/G,MAAM,UAAU,IAAI,IAAI,OAAO,IAAA,CAAK,IAAI,CAAC;IACzC,MAAM,sBAA+C,CAAC;IAEtD,KAAA,MAAW,QAAQ,OAAO,IAAA,CAAK,IAAI,EAAG;QACpC,IAAI,CAAC,QAAQ,GAAA,CAAI,IAAI,GAAG;YACtB,mBAAA,CAAoB,IAAI,CAAA,GAAI,IAAA,CAAK,IAAI,CAAA;QACvC;IACF;IAEA,OAAO;AACT;AA6BO,IAAM,oBAAoB,CAAmC,QAA8B,kBAAqB;IACrH,MAAM,oBAAoB,OAAO,WAAW,aAAa;IAGzD,MAAM,qBAAiB,2WAAA,EACrB,oBAAoB,cAAc,WAAA,GAAe,QAAQ,eAAe,cAAc,WAAA;IAExF,MAAM,kBAAc,2WAAA,EAAO,oBAAoB,cAAc,QAAA,GAAY,QAAQ,YAAY,cAAc,QAAS;IAEpH,MAAM,SAAkC,CAAC;IACzC,KAAA,MAAW,OAAO,OAAO,IAAA,CAAK,aAAa,EAAG;QAE5C,MAAA,CAAO,GAAG,CAAA,GAAI,oBAAoB,aAAA,CAAc,GAAG,CAAA,GAAK,QAAA,CAAS,GAAG,CAAA,IAAK,aAAA,CAAc,GAAG,CAAA;IAC5F;IAEA,OAAO;QACL,GAAG,MAAA;QACH,aAAa,eAAe,OAAA;QAC5B,UAAU,YAAY,OAAA;IACxB;AACF;AAEA,IAAM,oBAAoB;IACxB,kBAAkB,MAAO;IACzB,uBAAuB,MAAO,KAAK;AACrC;AA0CO,IAAM,qBAAyC,CAAC,QAAQ,SAAS,QAAQ,cAAc;IAC5F,MAAM,CAAC,eAAe,gBAAgB,CAAA,OAAI,6WAAA,EAAS,OAAO,WAAA,IAAe,CAAC;IAG1E,MAAM,qBAAiB,2WAAA,EAAO,OAAO,WAAA,IAAe,CAAC;IACrD,MAAM,kBAAc,2WAAA,EAAO,OAAO,QAAA,IAAY,EAAE;IAEhD,MAAM,UAAU,OAAO,OAAA,IAAW;IAClC,MAAM,YAAY,OAAO,mBAAA,KAAwB;IACjD,MAAM,kBAAkB,OAAO,QAAA,IAAY;IAC3C,MAAM,mBAAmB,OAAO,gBAAA,IAAoB;IAEpD,MAAM,gBAAgB;QACpB,GAAG,SAAA;QACH,GAAG,MAAA;QACH,aAAa;QACb,UAAU,YAAY,OAAA;IACxB;IAIA,MAAM,cAAc,CAAC,mBAAmB,WAAA,CAAY,CAAC,YAAY,CAAC,CAAC,UAAU,IAAA;IAC7E,MAAM,SAAS,cAAc,gBAAgB;IAC7C,MAAM,aACJ,CAAC,aAAa,CAAC,CAAC,UACZ,CAAC,mBAA4C;QAC3C,MAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;QAChE,OAAO,QAAQ;YAAE,GAAG,MAAA;YAAQ,GAAG,aAAA;QAAc,CAAC;IAChD,IACA;IAEN,MAAM,EACJ,MAAM,OAAA,EACN,cAAc,eAAA,EACd,WAAW,YAAA,EACX,OAAO,QAAA,EACP,QAAQ,SAAA,EACV,GAAIC,mPAAAA,EAAO,QAAQ,YAAY;QAAE;QAAkB,GAAG,iBAAA;IAAkB,CAAC;IAEzE,MAAM,EACJ,MAAM,eAAA,EACN,WAAW,oBAAA,EACX,cAAc,uBAAA,EACd,OAAO,gBAAA,EACP,IAAA,EACA,OAAA,EACA,QAAQ,iBAAA,EACV,OAAIA,kOAAAA,EACF,CAAA,cAAa;QACX,IAAI,CAAC,mBAAmB,CAAC,SAAS;YAChC,OAAO;QACT;QAEA,OAAO;YACL,GAAG,MAAA;YACH,GAAG,SAAA;YACH,aAAa,eAAe,OAAA,GAAU;YACtC,UAAU,YAAY,OAAA;QACxB;IACF,GACA,CAAA,mBAAkB;QAEhB,MAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;QAEhE,OAAO,UAAU,aAAa;IAChC,GACA;IAGF,MAAM,WAAO,4WAAA,EAAQ,MAAM;QACzB,IAAI,iBAAiB;YACnB,OAAO;QACT;QACA,OAAO;IACT,GAAG;QAAC;QAAiB;QAAM,aAAa;KAAC;IAEzC,MAAM,gBAAmC,gXAAA,EACvC,CAAA,gBAAe;QACb,IAAI,iBAAiB;YACnB,KAAK,QAAQ,WAAW;YACxB;QACF;QACA,OAAO,iBAAiB,WAAW;IACrC,GACA;QAAC,OAAO;KAAA;IAGV,MAAM,WAAO,4WAAA,EAAQ,MAAM;QACzB,IAAI,iBAAiB;YACnB,OAAO,iBAAiB,IAAI,CAAA,IAAK,GAAG,IAAI,EAAE,KAAK,KAAK,CAAC,CAAA;QACvD;QACA,OAAO,SAAS,QAAQ,CAAC,CAAA;IAC3B,GAAG;QAAC;QAAiB;QAAS,eAAe;KAAC;IAE9C,MAAM,YAAQ,4WAAA,EAAQ,MAAM;QAC1B,IAAI,iBAAiB;YACnB,OAAO,iBAAA,CAAkB,iBAAiB,SAAS,CAAC,CAAA,EAAG,eAAe;QACxE;QACA,OAAO,SAAS,eAAe;IACjC,GAAG;QAAC;QAAiB;QAAS,eAAe;KAAC;IAE9C,MAAM,YAAY,kBAAkB,uBAAuB;IAC3D,MAAM,aAAa,kBAAkB,0BAA0B;IAC/D,MAAM,QAAA,CAAS,kBAAkB,mBAAmB,QAAA,KAAa;IACjE,MAAM,UAAU,CAAC,CAAC;IAIlB,MAAM,gBAAY,gXAAA,EAAY,MAAM;QAClC,UAAU,CAAA,IAAK,KAAK,GAAA,CAAI,GAAG,IAAI,CAAC,CAAC;IACnC,GAAG;QAAC,SAAS;KAAC;IAEd,MAAM,oBAAgB,gXAAA,EAAY,MAAM;QACtC,UAAU,CAAA,IAAK,KAAK,GAAA,CAAI,GAAG,IAAI,CAAC,CAAC;IACnC,GAAG;QAAC,SAAS;KAAC;IAEd,MAAM,cAAA,CAAe,eAAe,OAAA,GAAU,CAAA,IAAK,YAAY,OAAA;IAE/D,MAAM,YAAY,KAAK,IAAA,CAAA,CAAM,QAAQ,WAAA,IAAe,YAAY,OAAO;IACvE,MAAM,cAAc,QAAQ,cAAc,YAAY,OAAA,GAAU,OAAO,YAAY,OAAA;IACnF,MAAM,kBAAA,CAAmB,OAAO,CAAA,IAAK,YAAY,OAAA,GAAU,cAAc,YAAY,OAAA;IAErF,MAAM,UAAuB,kBACzB,CAAA,QACE,kBAAkB,OAAO;YACvB,YAAY;QACd,CAAC,IACH,CAAA,QACE,UAAU,OAAO;YACf,YAAY;QACd,CAAC;IAEP,MAAM,aAAa,kBAAkB,IAAM,kBAAkB,IAAI,IAAM,UAAU;IAEjF,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA,4CAAA;QAEA;QAAA,4CAAA;QAEA;IACF;AACF;;ACzIA,IAAM,6BAA6B;IACjC,MAAM,KAAA;IACN,OAAO,KAAA;IACP,OAAO,KAAA;IACP,WAAW;IACX,YAAY;IACZ,SAAS;IACT,MAAM,KAAA;IACN,WAAW,KAAA;IACX,WAAW,KAAA;IACX,WAAW,KAAA;IACX,eAAe,KAAA;IACf,aAAa;IACb,iBAAiB;IACjB,YAAY,KAAA;IACZ,SAAS,KAAA;AACX;AA6HO,SAAS,gBAAiD,MAAA,EAAsC;IACrG,MAAM,EACJ,SAAS,gBAAA,EACT,oBAAoB,4BAAA,EACpB,aAAa,iBAAA,EACb,aAAa,qBAAA,EACb,eAAe,uBAAA,EACjB,GAAI,UAAU,CAAC;IAEf,gCAAgC,iBAAiB;IAEjD,MAAM,EAAE,YAAA,CAAa,CAAA,GAAI,uBAAuB;IAChD,MAAM,UAAU,kBAAkB;IAElC,MAAM,mBAAmB,kBAAkB,kBAAkB;QAC3D,aAAa;QACb,UAAU;QACV,kBAAkB;QAClB,UAAU;QACV,gBAAgB,KAAA;IAClB,CAAC;IAED,MAAM,8BAA8B,kBAAkB,8BAA8B;QAClF,aAAa;QACb,UAAU;QACV,QAAQ;QACR,kBAAkB;QAClB,UAAU;IACZ,CAAC;IAED,MAAM,oBAAoB,kBAAkB,mBAAmB;QAC7D,aAAa;QACb,UAAU;QACV,MAAM,KAAA;QACN,kBAAkB;QAClB,UAAU;QACV,OAAO,KAAA;IACT,CAAC;IAED,MAAM,wBAAwB,kBAAkB,uBAAuB;QACrE,aAAa;QACb,UAAU;QACV,QAAQ;YAAC,SAAS;SAAA;QAClB,kBAAkB;QAClB,UAAU;IACZ,CAAC;IAED,MAAM,0BAA0B,kBAAkB,yBAAyB;QACzE,aAAa;QACb,UAAU;QACV,kBAAkB;QAClB,UAAU;IACZ,CAAC;IAED,MAAM,QAAQ,wBAAwB;IAEtC,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkB,iBAAiB,CAAC;IAE5D,MAAM,eACJ,OAAO,qBAAqB,cACxB,KAAA,IACA;QACE,aAAa,iBAAiB,WAAA;QAC9B,UAAU,iBAAiB,QAAA;QAC3B,gBAAgB,iBAAiB,cAAA;IACnC;IAEN,MAAM,0BACJ,OAAO,iCAAiC,cACpC,KAAA,IACA;QACE,aAAa,4BAA4B,WAAA;QACzC,UAAU,4BAA4B,QAAA;QACtC,QAAQ,4BAA4B,MAAA;IACtC;IAEN,MAAM,gBACJ,OAAO,sBAAsB,cACzB,KAAA,IACA;QACE,aAAa,kBAAkB,WAAA;QAC/B,UAAU,kBAAkB,QAAA;QAC5B,MAAM,kBAAkB,IAAA;QACxB,OAAO,kBAAkB,KAAA;IAC3B;IAEN,MAAM,oBACJ,OAAO,0BAA0B,cAC7B,KAAA,IACA;QACE,aAAa,sBAAsB,WAAA;QACnC,UAAU,sBAAsB,QAAA;QAChC,QAAQ,sBAAsB,MAAA;IAChC;IAEN,MAAM,sBACJ,OAAO,4BAA4B,cAC/B,KAAA,IACA;QACE,aAAa,wBAAwB,WAAA;QACrC,UAAU,wBAAwB,QAAA;QAClC,OAAO,cAAc;IACvB;IAEN,MAAM,UAAU,mBACd;QACE,GAAG,YAAA;IACL,GACA,cAAc,YACd;QACE,kBAAkB,iBAAiB,gBAAA;QACnC,UAAU,iBAAiB,QAAA;QAC3B,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,gBAAgB,cAAc;IAChC;IAGF,MAAM,qBAAqB,mBAIzB;QACE,GAAG,uBAAA;IACL,GACA,cAAc,uBACd;QACE,kBAAkB,4BAA4B,gBAAA;QAC9C,UAAU,4BAA4B,QAAA;QACtC,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,gBAAgB,cAAc;IAChC;IAGF,MAAM,cAAc,mBAClB,iBAAiB,CAAC,GAClB,cAAc,gBACd;QACE,kBAAkB,kBAAkB,gBAAA;QACpC,UAAU,kBAAkB,QAAA;QAC5B,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,gBAAgB,cAAc;IAChC;IAGF,MAAM,cAAc,mBAClB;QACE,GAAG,iBAAA;IACL,GACA,cAAc,gBACd;QACE,kBAAkB,sBAAsB,gBAAA;QACxC,UAAU,sBAAsB,QAAA;QAChC,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,gBAAgB,cAAc;IAChC;IAGF,MAAM,gBAAgB,mBAIpB;QACE,GAAG,mBAAA;IACL,GACA,cAAc,kBACd;QACE,kBAAkB,wBAAwB,gBAAA;QAC1C,UAAU,wBAAwB,QAAA;QAClC,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,gBAAgB,cAAc;IAChC;IAGF,IAAI,iBAAiB,KAAA,GAAW;QAC9B,OAAO;YACL,UAAU;YACV,cAAc,KAAA;YACd,YAAY,KAAA;YACZ,SAAS;YACT,oBAAoB;YACpB,aAAa;YACb,aAAa;YACb,eAAe;QACjB;IACF;IAEA,IAAI,iBAAiB,MAAM;QACzB,OAAO;YACL,UAAU;YACV,cAAc;YACd,YAAY;YACZ,SAAS;YACT,oBAAoB;YACpB,aAAa;YACb,aAAa;YACb,eAAe;QACjB;IACF;IAGA,IAAI,CAAC,MAAM,MAAA,IAAU,cAAc;QACjC,OAAO;YACL,UAAU;YACV;YACA,YAAY,KAAA;YACZ,SAAS;YACT,oBAAoB;YACpB,aAAa;YACb,aAAa;YACb,eAAe;QACjB;IACF;IAEA,OAAO;QACL,UAAU,MAAM,MAAA;QAChB;QAAA,oEAAA;QAEA,gBAAY,qTAAA,EAAiC,QAAS,IAAA,CAAK,uBAAA,EAAyB,aAAa,EAAE;QAAA,qCAAA;QACnG;QACA;QACA;QACA;QACA;IACF;AACF;;ACjeA,IAAMC,8BAA6B;IACjC,MAAM,KAAA;IACN,OAAO,KAAA;IACP,OAAO,KAAA;IACP,WAAW;IACX,YAAY;IACZ,SAAS;IACT,MAAM,KAAA;IACN,WAAW,KAAA;IACX,WAAW,KAAA;IACX,WAAW,KAAA;IACX,eAAe,KAAA;IACf,aAAa;IACb,iBAAiB;IACjB,YAAY,KAAA;IACZ,SAAS,KAAA;AACX;AAoLO,SAAS,oBAAyD,MAAA,EAA0C;IACjH,MAAM,EAAE,eAAA,EAAiB,eAAA,EAAiB,eAAA,CAAgB,CAAA,GAAI,UAAU,CAAC;IAEzE,gCAAgC,qBAAqB;IAErD,MAAM,4BAA4B,kBAAkB,iBAAiB;QACnE,aAAa;QACb,UAAU;QACV,kBAAkB;QAClB,UAAU;IACZ,CAAC;IAED,MAAM,4BAA4B,kBAAkB,iBAAiB;QACnE,aAAa;QACb,UAAU;QACV,QAAQ;QACR,kBAAkB;QAClB,UAAU;IACZ,CAAC;IAED,MAAM,4BAA4B,kBAAkB,iBAAiB;QACnE,aAAa;QACb,UAAU;QACV,QAAQ;QACR,kBAAkB;QAClB,UAAU;IACZ,CAAC;IAED,MAAM,QAAQ,wBAAwB;IACtC,MAAM,OAAO,eAAe;IAE5B,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkB,qBAAqB,CAAC;IAEhE,MAAM,wBACJ,OAAO,oBAAoB,cACvB,KAAA,IACA;QACE,aAAa,0BAA0B,WAAA;QACvC,UAAU,0BAA0B,QAAA;IACtC;IAEN,MAAM,wBACJ,OAAO,oBAAoB,cACvB,KAAA,IACA;QACE,aAAa,0BAA0B,WAAA;QACvC,UAAU,0BAA0B,QAAA;QACpC,QAAQ,0BAA0B,MAAA;IACpC;IAEN,MAAM,wBACJ,OAAO,oBAAoB,cACvB,KAAA,IACA;QACE,aAAa,0BAA0B,WAAA;QACvC,UAAU,0BAA0B,QAAA;QACpC,QAAQ,0BAA0B,MAAA;IACpC;IAEN,MAAM,gBAAgB,CAAC,CAAA,CAAE,MAAM,MAAA,IAAU,IAAA;IAEzC,MAAM,cAAc,mBAIlB,yBAAyB,CAAC,GAC1B,MAAM,4BACN;QACE,kBAAkB,0BAA0B,gBAAA;QAC5C,UAAU,0BAA0B,QAAA;QACpC,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,QAAQ,MAAM;IAChB;IAGF,MAAM,cAAc,mBAIlB;QACE,GAAG,qBAAA;IACL,GACA,MAAM,4BACN;QACE,kBAAkB,0BAA0B,gBAAA;QAC5C,UAAU,0BAA0B,QAAA;QACpC,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,QAAQ,MAAM;IAChB;IAGF,MAAM,cAAc,mBAIlB;QACE,GAAG,qBAAA;IACL,GACA,MAAM,4BACN;QACE,kBAAkB,0BAA0B,gBAAA;QAC5C,UAAU,0BAA0B,QAAA;QACpC,SAAS,CAAC,CAAC;IACb,GACA;QACE,MAAM;QACN,QAAQ,MAAM;IAChB;IAIF,IAAI,CAAC,eAAe;QAClB,OAAO;YACL,UAAU;YACV,oBAAoB,KAAA;YACpB,WAAW,KAAA;YACX,iBAAiBA;YACjB,iBAAiBA;YACjB,iBAAiBA;QACnB;IACF;IAEA,OAAO;QACL,UAAU;QACV,WAAW,MAAM,SAAA;QACjB,oBAAoB,MAAM,kBAAA;QAC1B,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;IACnB;AACF;;AC7XO,IAAM,sBAAsB,OAAO,WAAW,oBAAcC,OAAM,mBAAkBA,4WAAAA,CAAM,SAAA;;ACEjG,IAAM,WAAW,CAAA,UAAA,CAAA;AAkDV,IAAM,aAAyB,MAAM;IAC1C,gCAAgC,QAAQ;IAExC,MAAM,UAAU,kBAAkB;IAClC,MAAM,QAAQ,wBAAwB;IAEtC,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkB,QAAQ,CAAC;IAEnD,IAAI,YAAY,KAAA,GAAW;QACzB,OAAO;YAAE,UAAU;YAAO,YAAY,KAAA;YAAW,SAAS,KAAA;QAAU;IACtE;IAEA,IAAI,YAAY,MAAM;QACpB,OAAO;YAAE,UAAU;YAAM,YAAY;YAAO,SAAS;QAAK;IAC5D;IAEA,OAAO;QAAE,UAAU;QAAM,YAAY,MAAM,UAAA;QAAY;IAAQ;AACjE;;ACrEA,IAAMC,YAAW;AA4CV,IAAM,iBAAiB,MAA4B;IACxD,gCAAgCA,SAAQ;IAExC,MAAM,kBAAkB,wBAAwB;IAChD,MAAM,SAAS,iBAAiB;IAChC,MAAM,QAAQ,wBAAwB;IAEtC,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkBA,SAAQ,CAAC;IAEnD,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,UAAU;YAAO,UAAU,KAAA;YAAW,WAAW,KAAA;QAAU;IACtE;IAEA,OAAO;QACL,UAAU;QACV,UAAU,OAAO,QAAA;QACjB,WAAW,gBAAgB,SAAA;IAC7B;AACF;;AC9DA,IAAMC,YAAW;AA4HV,SAAS,UAAyB;IACvC,gCAAgCA,SAAQ;IAExC,MAAM,OAAO,eAAe;IAC5B,MAAM,QAAQ,wBAAwB;IAEtC,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkBA,SAAQ,CAAC;IAEnD,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO;YAAE,UAAU;YAAO,YAAY,KAAA;YAAW,MAAM,KAAA;QAAU;IACnE;IAEA,IAAI,SAAS,MAAM;QACjB,OAAO;YAAE,UAAU;YAAM,YAAY;YAAO,MAAM;QAAK;IACzD;IAEA,OAAO;QAAE,UAAU;QAAM,YAAY;QAAM;IAAK;AAClD;;AC3GO,IAAM,WAAW,MAAmB;IACzC,gCAAgC,UAAU;IAC1C,OAAO,wBAAwB;AACjC;;;ACnCA,IAAM,sBAAsB,CAAI,UAAa;IAC3C,MAAM,MAAMC,4WAAAA,CAAM,MAAA,CAAU,KAAK;IACjC,IAAI,KAAC,qMAAA,EAAU,OAAO,IAAI,OAAO,GAAG;QAClC,IAAI,OAAA,GAAU;IAChB;IACA,OAAOA,4WAAAA,CAAM,OAAA,CAAQ,IAAM,IAAI,OAAA,EAAS;QAAC,IAAI,OAAO;KAAC;AACvD;AAKO,IAAM,mBAAqC,CAAC,SAAS,oBAAoB;IAC9E,OAAOA,4WAAAA,CAAM,OAAA,CAAQ,SAAS,oBAAoB,eAAe,CAAC;AACpE;AAKO,IAAM,gBAAgB,qMAAA;;ACd7B,IAAM,sCAAsC;AAE5C,eAAe,cAAiB,MAAA,EAA6E;IAC3G,IAAI;QACF,MAAM,IAAI,MAAM;QAChB,IAAI,aAAa,UAAU;YACzB,OAAO,EAAE,IAAA,CAAK;QAChB;QACA,OAAO;IACT,EAAA,OAAS,GAAG;QAEV,QAAI,4SAAA,EAAwB,CAAC,KAAK,EAAE,MAAA,CAAO,IAAA,CAAK,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,SAAS,mCAAmC,GAAG;YAC3G,WAAO,wSAAA,CAAoB;QAC7B;QAGA,MAAM;IACR;AACF;AAoDA,SAAS,4BAA4B,MAAA,EAA2C;IAC9E,SAAS,qBACP,OAAA,EAC4F;QAC5F,OAAQ,OAAA,GAAU,SAA8B;YAC9C,IAAI,SAAS,MAAM,cAAc,QAAQ,GAAG,IAAI,CAAC;YAEjD,QAAI,ySAAA,EAAqB,MAAM,GAAG;gBAIhC,MAAM,gBAAY,0SAAA,CAAsB;gBAExC,MAAM,sBAAkB,iTAAA,EAA6B,OAAO,WAAA,CAAY,QAAA,EAAU,cAAc;gBAEhG,MAAM,QAAQ,kBAAkB,gBAAgB,EAAE,KAAA,GAAQ,KAAA;gBAE1D,MAAM,SAAS,MAAM;oBACnB,UAAU,MAAA,CACR,IAAI,sSAAA,CAAkB,yCAAyC;wBAC7D,MAAM;oBACR,CAAC;gBAEL;gBAEA,MAAM,WAAW,MAAM;oBACrB,UAAU,OAAA,CAAQ,IAAI;gBACxB;gBAEA,IAAI,OAAO,qBAAA,KAA0B,KAAA,GAAW;oBAK9C,OAAO,eAAA,GAAkB;wBACvB;wBACA,mBAAmB;wBACnB,4BAA4B;oBAC9B,CAAC;gBACH,OAAO;oBACL,OAAO,qBAAA,CAAsB;wBAC3B;wBACA;wBACA;oBACF,CAAC;gBACH;gBAKA,MAAM,UAAU,OAAA;gBAKhB,SAAS,MAAM,cAAc,QAAQ,GAAG,IAAI,CAAC;YAC/C;YAEA,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAmDO,IAAM,oBAAuC,CAAC,SAAS,YAAY;IACxE,MAAM,EAAE,6BAAA,EAA+B,SAAA,CAAU,CAAA,GAAI,SAAS;IAC9D,MAAM,iBAAaG,2WAAAA,EAAO,OAAO;IACjC,MAAM,iBAAaA,2WAAAA,EAAO,OAAO;IAEjC,WAAW,WACT,sSAAA,EAAkB,qBAAqB;QACrC,uBAAuB,QAAQ,SAAS,qBAAqB;IAC/D,CAAC;IAIH,oBAAoB,MAAM;QACxB,WAAW,OAAA,GAAU;QACrB,WAAW,OAAA,GAAU;IACvB,CAAC;IAED,WAAOC,gXAAAA,EACL,CAAA,GAAI,SAAS;QACX,MAAM,UAAU,4BAA4B;YAC1C,iBAAiB;YACjB;YACA,GAAG,WAAW,OAAA;QAChB,CAAC,EAAE,WAAW,OAAO;QACrB,OAAO,QAAQ,GAAG,IAAI;IACxB,GACA;QAAC;QAA+B,SAAS;KAAA;AAE7C;;ACvLO,SAAS,4BAAqG,EACnH,UAAAC,SAAAA,EACA,YAAA,EACA,UAAA,EACA,OAAA,EACF,EAA2C;IAKzC,OAAO,SAAS,gBACd,MAAA,EAC4E;QAC5E,MAAM,EAAE,KAAK,IAAA,EAAM,GAAG,iBAAiB,CAAA,GAAI,UAAW;YAAE,KAAK;QAAO;QAEpE,gCAAgCA,SAAQ;QAExC,MAAM,UAAU,WAAW,IAAI;QAE/B,MAAM,aAAa,kBAAkB,kBAAkB;YACrD,aAAa;YACb,UAAU;YACV,kBAAkB;YAClB,UAAU;YACV,qBAAqB,KAAA;QACvB,CAAiB;QAEjB,MAAM,QAAQ,wBAAwB;QACtC,MAAM,OAAO,eAAe;QAC5B,MAAM,EAAE,YAAA,CAAa,CAAA,GAAI,uBAAuB;QAEhD,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkBA,SAAQ,CAAC;QAEnD,MAAM,aACJ,OAAO,qBAAqB,cACxB,KAAA,IACC;YACC,aAAa,WAAW,WAAA;YACxB,UAAU,WAAW,QAAA;YACrB,GAAI,SAAS,iBAAiB;gBAAE,OAAO,cAAc;YAAG,IAAI,CAAC,CAAA;QAC/D;QAEN,MAAM,gBAAgB,CAAC,CAAA,CAAE,MAAM,MAAA,IAAA,CAAW,SAAS,kBAAkB,OAAO,IAAA,CAAA;QAE5E,MAAM,YAAY,CAAC,CAAC,cAAc;QAElC,MAAM,SAAS,mBACZ,cAAc,CAAC,GAChB,SACA;YACE,kBAAkB,WAAW,gBAAA;YAC7B,UAAU,WAAW,QAAA;YACrB,SAAS;YACT,qBAAqB,WAAW,mBAAA;QAClC,GACA;YACE,MAAM;YACN,QAAQ,MAAM;YACd,GAAI,SAAS,iBAAiB;gBAAE,OAAO,cAAc;YAAG,IAAI,CAAC,CAAA;QAC/D;QAGF,OAAO;IACT;AACF;;AChGO,IAAM,gBAAgB,4BAA4E;IACvG,UAAU;IACV,cAAc;IACd,YAAY,MAAM;QAChB,MAAM,QAAQ,wBAAwB;QACtC,OAAO,MAAM,OAAA,CAAQ,aAAA;IACvB;AACF,CAAC;;ACPM,IAAM,qBAAqB,4BAA+E;IAC/G,UAAU;IACV,cAAc;IACd,YAAY,MAAM;QAChB,MAAM,QAAQ,wBAAwB;QACtC,OAAO,MAAM,OAAA,CAAQ,kBAAA;IACvB;AACF,CAAC;;ACPM,IAAM,oBAAoB,4BAAoF;IACnH,UAAU;IACV,cAAc;IACd,YAAY,CAAA,aAAY;QACtB,MAAM,EAAE,YAAA,CAAa,CAAA,GAAI,uBAAuB;QAChD,MAAM,OAAO,eAAe;QAE5B,IAAI,aAAa,gBAAgB;YAC/B,OAAO,cAAc;QACvB;QACA,OAAO,MAAM;IACf;AACF,CAAC;;ACZM,IAAM,WAAW,4BAAkE;IACxF,UAAU;IACV,cAAc;IACd,YAAY,CAAA,SAAQ;QAClB,MAAM,QAAQ,wBAAwB;QACtC,OAAO,CAAC,EAAE,KAAA,EAAO,GAAG,KAAK,CAAA,KAAM;YAE7B,OAAO,MAAM,OAAA,CAAQ,QAAA,CAAS;gBAAE,GAAG,IAAA;gBAAM,KAAK;YAAK,CAAC;QACtD;IACF;IACA,SAAS;QACP,iBAAiB;IACnB;AACF,CAAC;;ACTD,IAAME,YAAW;AAmBV,IAAM,kBAAkB,CAAC,WAAmC;IACjE,gCAAgCA,SAAQ;IAExC,MAAM,QAAQ,wBAAwB;IACtC,MAAM,OAAO,eAAe;IAC5B,MAAM,EAAE,YAAA,CAAa,CAAA,GAAI,uBAAuB;IAEhD,MAAM,SAAA,EAAW,WAAO,sSAAA,EAAkBA,SAAQ,CAAC;IAEnD,MAAM,MAAMC,mPAAAA,EACV,MAAM,KACF;QACE,MAAM;QACN,QAAQ,KAAK,EAAA;QACb,MAAM;YAAE,OAAO,QAAQ,QAAQ,iBAAiB,cAAc,KAAK,KAAA;QAAU;IAC/E,IACA,MACJ,CAAC,EAAE,IAAA,CAAK,CAAA,GAAM,MAAM,OAAA,CAAQ,eAAA,CAAgB,IAAI,GAChD;QACE,kBAAkB,MAAQ;QAC1B,kBAAkB,QAAQ;IAC5B;IAGF,MAAM,iBAAaC,gXAAAA,EAAY,IAAM,IAAI,MAAA,CAAO,GAAG;QAAC,IAAI,MAAM;KAAC;IAE/D,OAAO;QACL,MAAM,IAAI,IAAA;QACV,OAAO,IAAI,KAAA;QACX,WAAW,IAAI,SAAA;QACf,YAAY,IAAI,YAAA;QAChB;IACF;AACF;;ACKO,IAAM,cAAc,CAAC,YAAuD;IACjF,MAAM,iBAAiB,mBAAmB;IAC1C,MAAM,EAAE,KAAK,eAAA,EAAiB,MAAA,EAAQ,UAAA,CAAW,CAAA,GAAI,WAAW;IAEhE,MAAM,QAAQ,SAAS;IACvB,MAAM,EAAE,YAAA,CAAa,CAAA,GAAI,gBAAgB;IACzC,MAAM,EAAE,QAAA,EAAU,IAAA,CAAK,CAAA,GAAI,QAAQ;IAEnC,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM,wFAAwF;IAC1G;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,qFAAqF;IACvG;IAEA,IAAI,oBAAoB,kBAAkB,CAAC,cAAc;QACvD,MAAM,IAAI,MAAM,+DAA+D;IACjF;IAEA,MAAM,cAAUE,4WAAAA,EACd,IAAM,MAAM,uBAAA,CAAwB;YAAE;YAAQ;YAAY,KAAK;QAAgB,CAAC,GAChF;QAAC,KAAK,EAAA;QAAI,cAAc;QAAI;QAAQ;QAAY,eAAe;KAAA;IAGjE,MAAM,wBAAoB,yXAAA,EACxB,CAAA,KAAM,QAAQ,SAAA,CAAU,EAAE,GAC1B,IAAM,QAAQ,QAAA,CAAS,GACvB,IAAM,QAAQ,QAAA,CAAS;IAGzB,MAAM,iBAAaA,4WAAAA,EAA4D,MAAM;QACnF,IAAI,CAAC,kBAAkB,QAAA,EAAU;YAC/B,OAAO;gBACL,IAAI;gBACJ,sBAAsB;gBACtB,mBAAmB;gBACnB,QAAQ;gBACR,QAAQ;gBACR,uBAAuB;gBACvB,YAAY;gBACZ,MAAM;gBACN,eAAe;gBACf,iBAAiB;YACnB;QACF;QACA,MAAM,EAAA,6DAAA;QAEJ,MAAA,EACA,OAAA,EACA,QAAA,EAAA,uDAAA;QAEA,GAAG,MACL,GAAI,kBAAkB,QAAA;QACtB,OAAO;IACT,GAAG;QAAC,kBAAkB,QAAQ;KAAC;IAE/B,MAAM,WAAW;QACf,GAAG,UAAA;QACH,UAAU,QAAQ,QAAA;QAClB,OAAO,QAAQ,KAAA;QACf,SAAS,QAAQ,OAAA;QACjB,OAAO,QAAQ,KAAA;QACf,UAAU,QAAQ,QAAA;QAClB,YAAY,kBAAkB,UAAA;QAC9B,cAAc,kBAAkB,YAAA;QAChC,OAAO,kBAAkB,KAAA;QACzB,QAAQ,kBAAkB,MAAA;QAC1B,aAAa,kBAAkB,WAAA;IACjC;IAEA,OAAO;QACL;IACF;AACF;;;;;;;AG5IO,IAAM,cAAc,CAAI,UAAgB;IAC7C,MAAM,UAAMQ,2WAAAA,EAAO,KAAK;IAExB,IAAA,8WAAA,EAAU,MAAM;QACd,IAAI,OAAA,GAAU;IAChB,GAAG;QAAC,KAAK;KAAC;IAEV,OAAO,IAAI,OAAA;AACb;AAEO,IAAM,iBAAiB,CAC5B,SACA,OACA,OACG;IACH,MAAM,YAAY,CAAC,CAAC;IACpB,MAAM,QAAQA,+WAAAA,EAAO,EAAE;IAIvB,IAAA,8WAAA,EAAU,MAAM;QACd,MAAM,OAAA,GAAU;IAClB,GAAG;QAAC,EAAE;KAAC;IAEP,IAAA,8WAAA,EAAU,MAAM;QACd,IAAI,CAAC,aAAa,CAAC,SAAS;YAC1B,OAAO,KAAO,CAAD;QACf;QAEA,MAAM,cAAc,CAAA,GAAI,SAAkB;YACxC,IAAI,MAAM,OAAA,EAAS;gBACjB,MAAM,OAAA,CAAQ,GAAG,IAAI;YACvB;QACF;QAEC,QAAgB,EAAA,CAAG,OAAO,WAAW;QAEtC,OAAO,MAAM;YACV,QAAgB,GAAA,CAAI,OAAO,WAAW;QACzC;IACF,GAAG;QAAC;QAAW;QAAO;QAAS,KAAK;KAAC;AACvC;;ADfA,IAAM,kBAAkBC,4WAAAA,CAAM,aAAA,CAA2C,IAAI;AAC7E,gBAAgB,WAAA,GAAc;AAE9B,IAAM,uBAAuB,CAAC,KAAkC,YAA0C;IACxG,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,MACR,CAAA,4EAAA,EAA+E,OAAO,CAAA,2BAAA,CAAA;IAE1F;IAEA,OAAO;AACT;AAqCA,IAAM,WAAiE,CAAC,EACtE,QAAQ,aAAA,EACR,OAAA,EACA,QAAA,EACF,KAA4B;IAC1B,MAAM,SAASA,4WAAAA,CAAM,OAAA,CAAQ,IAAM,gBAAgB,aAAa,GAAG;QAAC,aAAa;KAAC;IAGlF,MAAM,CAAC,KAAK,UAAU,CAAA,GAAIA,4WAAAA,CAAM,QAAA,CAA+B,IAAA,CAAO;YACpE,QAAQ,OAAO,GAAA,KAAQ,SAAS,OAAO,MAAA,GAAS;YAChD,UAAU,OAAO,GAAA,KAAQ,SAAS,OAAO,MAAA,CAAO,QAAA,CAAS,OAAO,IAAI;QACtE,CAAA,CAAE;IAEFA,4WAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,YAAY;QAEhB,MAAM,iBAAiB,CAAC,WAAmB;YACzC,WAAW,CAAAC,SAAO;gBAEhB,IAAIA,KAAI,MAAA,CAAQ,CAAA,OAAOA;gBACvB,OAAO;oBACL;oBACA,UAAU,OAAO,QAAA,CAAS,OAAO;gBACnC;YACF,CAAC;QACH;QAGA,IAAI,OAAO,GAAA,KAAQ,WAAW,CAAC,IAAI,MAAA,EAAQ;YACzC,OAAO,aAAA,CAAc,IAAA,CAAK,CAAA,WAAU;gBAClC,IAAI,UAAU,WAAW;oBAIvB,eAAe,MAAM;gBACvB;YACF,CAAC;QACH,OAAA,IAAW,OAAO,GAAA,KAAQ,UAAU,CAAC,IAAI,MAAA,EAAQ;YAE/C,eAAe,OAAO,MAAM;QAC9B;QAEA,OAAO,MAAM;YACX,YAAY;QACd;IACF,GAAG;QAAC;QAAQ;QAAK,OAAO;KAAC;IAGzB,MAAM,aAAa,YAAY,aAAa;IAC5CD,4WAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,eAAe,QAAQ,eAAe,eAAe;YACvD,QAAQ,IAAA,CAAK,4FAA4F;QAC3G;IACF,GAAG;QAAC;QAAY,aAAa;KAAC;IAG9B,MAAM,cAAc,YAAY,OAAO;IACvCA,4WAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,CAAC,IAAI,QAAA,EAAU;YACjB;QACF;QAEA,MAAM,UAAU,6BAA6B,SAAS,aAAa;YAAC;YAAgB,OAAO;SAAC;QAE5F,IAAI,SAAS;YACX,IAAI,QAAA,CAAS,MAAA,CAAO,OAAO;QAC7B;IACF,GAAG;QAAC;QAAS;QAAa,IAAI,QAAQ;KAAC;IAEvC,OAAO,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,gBAAgB,QAAA,EAAhB;QAAyB,OAAO;IAAA,GAAM,QAAS;AACzD;AAEA,IAAM,gCAAgC,CAAC,mBAAiD;IACtF,MAAM,MAAMA,4WAAAA,CAAM,UAAA,CAAW,eAAe;IAC5C,OAAO,qBAAqB,KAAK,cAAc;AACjD;AAEA,IAAM,cAAc,MAA6B;IAC/C,MAAM,EAAE,QAAA,CAAS,CAAA,GAAI,8BAA8B,qBAAqB;IACxE,OAAO;AACT;AAEA,IAAM,uBACJ;AAKF,IAAM,iBAAiB,CAAC,aAAsB,WAAW,oBAAA,KAAwC;IAC/F,IAAI,gBAAgB,QAAQ,SAAS,WAAW,GAAG;QACjD,OAAO;IACT;IAEA,MAAM,IAAI,MAAM,QAAQ;AAC1B;AAOA,IAAM,kBAAkB,CAAC,KAAc,WAAW,oBAAA,KAA2C;IAC3F,IAAI,UAAU,GAAG,GAAG;QAClB,OAAO;YACL,KAAK;YACL,eAAe,QAAQ,OAAA,CAAQ,GAAG,EAAE,IAAA,CAAK,CAAA,SAAU,eAAe,QAAQ,QAAQ,CAAC;QACrF;IACF;IAEA,MAAM,SAAS,eAAe,KAAK,QAAQ;IAE3C,IAAI,WAAW,MAAM;QACnB,OAAO;YAAE,KAAK;QAAQ;IACxB;IAEA,OAAO;QAAE,KAAK;QAAQ;IAAO;AAC/B;AAEA,IAAM,kBAAkB,CAAC,QAA2D;IAClF,OAAO,QAAQ,QAAQ,OAAO,QAAQ;AACxC;AAEA,IAAM,YAAY,CAAC,QAA8C;IAC/D,OAAO,gBAAgB,GAAG,KAAK,OAAO,IAAI,IAAA,KAAS;AACrD;AAKA,IAAM,WAAW,CAAC,QAAgC;IAChD,OACE,gBAAgB,GAAG,KACnB,OAAO,IAAI,QAAA,KAAa,cACxB,OAAO,IAAI,WAAA,KAAgB,cAC3B,OAAO,IAAI,mBAAA,KAAwB,cACnC,OAAO,IAAI,kBAAA,KAAuB;AAEtC;AAEA,IAAM,+BAA+B,CACnC,SACA,aACA,kBAC0B;IAC1B,IAAI,CAAC,gBAAgB,OAAO,GAAG;QAC7B,OAAO;IACT;IAEA,OAAO,OAAO,IAAA,CAAK,OAAO,EAAE,MAAA,CAAO,CAAC,YAAmC,QAAQ;QAC7E,MAAM,YAAY,CAAC,gBAAgB,WAAW,KAAK,CAAC,QAAQ,OAAA,CAAQ,GAAG,CAAA,EAAG,WAAA,CAAY,GAAG,CAAC;QAE1F,IAAI,cAAc,QAAA,CAAS,GAAG,GAAG;YAC/B,IAAI,WAAW;gBACb,QAAQ,IAAA,CAAK,CAAA,iCAAA,EAAoC,GAAG,CAAA,2BAAA,CAA6B;YACnF;YAEA,OAAO;QACT;QAEA,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,OAAO;YAAE,GAAI,cAAc,CAAC,CAAA;YAAI,CAAC,GAAG,CAAA,EAAG,OAAA,CAAQ,GAAG,CAAA;QAAE;IACtD,GAAG,IAAI;AACT;AAEA,IAAM,mBAAmB;AAEzB,IAAM,UAAU,CAAC,MAAe,UAA4B;IAC1D,IAAI,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,KAAK,GAAG;QACrD,OAAO,SAAS;IAClB;IAEA,MAAM,YAAY,MAAM,OAAA,CAAQ,IAAI;IACpC,MAAM,aAAa,MAAM,OAAA,CAAQ,KAAK;IAEtC,IAAI,cAAc,WAAY,CAAA,OAAO;IAErC,MAAM,kBAAkB,OAAO,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,IAAI,MAAM;IACjE,MAAM,mBAAmB,OAAO,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,KAAK,MAAM;IAEnE,IAAI,oBAAoB,iBAAkB,CAAA,OAAO;IAIjD,IAAI,CAAC,mBAAmB,CAAC,UAAW,CAAA,OAAO,SAAS;IAEpD,MAAM,WAAW,OAAO,IAAA,CAAK,IAAI;IACjC,MAAM,YAAY,OAAO,IAAA,CAAK,KAAK;IAEnC,IAAI,SAAS,MAAA,KAAW,UAAU,MAAA,CAAQ,CAAA,OAAO;IAEjD,MAAM,SAAqC,CAAC;IAC5C,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,KAAK,EAAG;QAC3C,MAAA,CAAO,QAAA,CAAS,CAAC,CAAC,CAAA,GAAI;IACxB;IACA,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,KAAK,EAAG;QAC5C,MAAA,CAAO,SAAA,CAAU,CAAC,CAAC,CAAA,GAAI;IACzB;IACA,MAAM,UAAU,OAAO,IAAA,CAAK,MAAM;IAClC,IAAI,QAAQ,MAAA,KAAW,SAAS,MAAA,EAAQ;QACtC,OAAO;IACT;IAEA,MAAM,IAAI;IACV,MAAM,IAAI;IACV,MAAM,OAAO,CAAC,QAAyB;QACrC,OAAO,QAAQ,CAAA,CAAE,GAAG,CAAA,EAAG,CAAA,CAAE,GAAG,CAAC;IAC/B;IAEA,OAAO,QAAQ,KAAA,CAAM,IAAI;AAC3B;AAEA,IAAM,YAAY,MAAqB;IACrC,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI,2CAA2C,mBAAmB;IACjF,OAAO;AACT;AAEA,IAAM,6CAA6C,CAAC,kBAAgD;IAClG,MAAM,kBAAkBA,4WAAAA,CAAM,UAAA,CAAW,eAAe;IAExD,OAAO,qBAAqB,iBAAiB,aAAa;AAC5D;AAwBA,IAAM,cAAc,CAAC,MAAgB,IAAI,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,IAAI,KAAA,CAAM,CAAC;AAE9E,IAAM,yBAAyB,CAAC,MAAyBE,cAAuD;IAC9G,MAAM,cAAc,GAAG,YAAY,IAAI,CAAC,CAAA,OAAA,CAAA;IAExC,MAAM,gBAAwD,CAAC,EAC7D,EAAA,EACA,SAAA,EACA,QAAA,EACA,UAAU,CAAC,CAAA,EACX,MAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,EACA,QAAA,EACA,OAAA,EACA,WAAA,EACA,aAAA,EACA,gBAAA,EACA,SAAA,EACA,QAAA,EACA,uBAAA,EACA,oBAAA,EACF,KAAM;QACJ,MAAM,MAAM,2CAA2C,CAAA,QAAA,EAAW,WAAW,CAAA,CAAA,CAAG;QAChF,MAAM,WAAW,cAAc,MAAM,IAAI,QAAA,GAAW;QACpD,MAAM,CAAC,SAAS,UAAU,CAAA,GAAIF,4WAAAA,CAAM,QAAA,CAA+B,IAAI;QACvE,MAAM,aAAaA,4WAAAA,CAAM,MAAA,CAA6B,IAAI;QAC1D,MAAM,UAAUA,4WAAAA,CAAM,MAAA,CAA8B,IAAI;QACxD,MAAM,CAAC,SAAS,QAAQ,CAAA,OAAIG,6WAAAA,EAAS,KAAK;QAK1C,eAAe,SAAS,QAAQ,MAAM;QACtC,eAAe,SAAS,SAAS,OAAO;QACxC,eAAe,SAAS,UAAU,QAAQ;QAC1C,eAAe,SAAS,SAAS,OAAO;QACxC,eAAe,SAAS,aAAa,WAAW;QAChD,eAAe,SAAS,eAAe,aAAa;QACpD,eAAe,SAAS,kBAAkB,gBAAgB;QAC1D,eAAe,SAAS,WAAW,SAAS;QAC5C,eAAe,SAAS,UAAU,QAAQ;QAC1C,eAAe,SAAS,yBAAyB,uBAAuB;QACxE,eAAe,SAAS,sBAAsB,oBAAoB;QAClE,eAAe,SAAS,UAAU,QAAQ;QAE1C,IAAI;QACJ,IAAI,SAAS;YAEX,gBAAgB,MAAM;gBACpB,SAAS,IAAI;gBACb,QAAQ,OAAO;YACjB;QACF;QAEA,eAAe,SAAS,SAAS,aAAa;QAE9CH,4WAAAA,CAAM,eAAA,CAAgB,MAAM;YAC1B,IAAI,WAAW,OAAA,KAAY,QAAQ,QAAQ,OAAA,KAAY,QAAQ,UAAU;gBACvE,IAAI,aAAmC;gBACvC,IAAI,UAAU;oBACZ,aAAa,SAAS,MAAA,CAAO,MAAa,OAAO;gBACnD;gBAGA,WAAW,OAAA,GAAU;gBAErB,WAAW,UAAU;gBAErB,IAAI,YAAY;oBACd,WAAW,KAAA,CAAM,QAAQ,OAAO;gBAClC;YACF;QACF,GAAG;YAAC;YAAU,OAAO;SAAC;QAEtB,MAAM,cAAc,YAAY,OAAO;QACvCA,4WAAAA,CAAM,SAAA,CAAU,MAAM;YACpB,IAAI,CAAC,WAAW,OAAA,EAAS;gBACvB;YACF;YAEA,MAAM,UAAU,6BAA6B,SAAS,aAAa;gBAAC,gBAAgB;aAAC;YAErF,IAAI,WAAW,YAAY,WAAW,OAAA,EAAS;gBAC7C,WAAW,OAAA,CAAQ,MAAA,CAAO,OAAO;YACnC;QACF,GAAG;YAAC;YAAS,WAAW;SAAC;QAEzBA,4WAAAA,CAAM,eAAA,CAAgB,MAAM;YAC1B,OAAO,MAAM;gBACX,IAAI,WAAW,OAAA,IAAW,OAAO,WAAW,OAAA,CAAQ,OAAA,KAAY,YAAY;oBAC1E,IAAI;wBACF,WAAW,OAAA,CAAQ,OAAA,CAAQ;wBAC3B,WAAW,OAAA,GAAU;oBACvB,EAAA,OAAQ,CAER;gBACF;YACF;QACF,GAAG,CAAC,CAAC;QAEL,OACE,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAAA,4WAAAA,CAAA,QAAA,EAAA,MACG,CAAC,WAAW,UACb,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,OAAA;YACC;YACA,OAAO;gBACL,QAAQ,UAAU,UAAU;gBAC5B,YAAY,UAAU,YAAY;YACpC;YACA;YACA,KAAK;QAAA;IAIb;IAGA,MAAM,gBAAwD,CAAA,UAAS;QACrE,2CAA2C,CAAA,QAAA,EAAW,WAAW,CAAA,CAAA,CAAG;QACpE,MAAM,EAAE,EAAA,EAAI,SAAA,CAAU,CAAA,GAAI;QAC1B,OACE,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,OAAA;YACC;YACA;QAAA;IAGN;IAEA,MAAM,UAAUE,YAAW,gBAAgB;IAC3C,QAAQ,WAAA,GAAc;IACrB,QAAgB,aAAA,GAAgB;IAEjC,OAAO;AACT;AAEA,IAAM,WAAW,OAAO,SAAW;AACnC,IAAM,iBAIF,uBAAuB,WAAW,QAAQ;;ADrb9C,IAAM,CAAC,mBAAmB,oBAAoB,CAAA,GAAI,qBAExC,mBAAmB;AAE7B,IAAM,qBAAqB,CAAC,EAAE,QAAA,CAAS,CAAA,KAAyB;IAC9D,MAAM,QAAQ,SAAS;IACvB,MAAM,EAAE,MAAM,eAAA,CAAgB,CAAA,GAAI,mPAAA,EAChC,oBACA,YAAY;QACV,MAAM,aAAc,MAAM,MAAM,uBAAA,CAAwB;QACxD,OAAO;YAAE;QAAW;IACtB,GACA;QACE,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;IACpB;IAGF,OACE,aAAA,GAAAE,4WAAAA,CAAA,aAAA,CAAC,kBAAkB,QAAA,EAAlB;QACC,OAAO;YACL,OAAO,mBAAmB;QAC5B;IAAA,GAEC;AAGP;AAEA,IAAM,yBAAyB,MAAM;IACnC,MAAM,QAAQ,SAAS;IAEvB,OAAO,MAAM,uBAAA;AACf;AAEA,IAAM,wBAAwB,CAAC,cAA4B,MAAA,KAAW;IACpE,MAAM,EAAE,YAAA,CAAa,CAAA,GAAI,gBAAgB;IACzC,MAAM,EAAE,IAAA,CAAK,CAAA,GAAI,QAAQ;IACzB,MAAM,WAAW,gBAAgB,iBAAiB,eAAe;IACjE,MAAM,kBAAkB,qBAAqB;IAE7C,MAAM,EAAE,MAAM,wBAAA,EAA0B,SAAS,uBAAA,CAAwB,CAAA,OAAI,kOAAA,EAC3E;QACE,KAAK;QACL,YAAY,UAAU;IACxB,GACA,MAAM;QACJ,OAAO,UAAU,wBAAwB;YACvC,SAAS;QACX,CAAC;IACH;IAGF,MAAM,cAAc,uBAAuB;QAE3CC,8WAAAA,EAAU,MAAM;QACd,IAAI,CAAC,UAAU,GAAI,CAAA;QACnB,wBAAwB,EAAE,KAAA,CAAM,KAEhC,CAFsC,AAErC;IACH,GAAG;QAAC,UAAU,EAAE;KAAC;IAEjB,MAAM,oBAAoB,0BAA0B;IACpD,MAAM,uBAAuB,0BAA0B;IACvD,MAAM,qBAAqB,0BAA0B;IACrD,MAAM,uBAAuB,aAAa,iBAAiB,QAAQ;IAEnE,MAAM,EAAE,MAAM,MAAA,CAAO,CAAA,OAAI,+OAAA,EACvB,mBAAmB,qBAAqB,uBACpC;QAAE,KAAK;QAAc;QAAmB;IAAqB,IAC7D,MACJ,CAAC,EAAE,sBAAAC,qBAAAA,EAAsB,mBAAAC,kBAAAA,CAAkB,CAAA,KAAM;QAC/C,OAAO,iBAAiB,WAAWD,uBAAsB;YACvD,eAAeC;QACjB,CAAC;IACH,GACA;QACE,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB,MAAQ;IAC5B;IAGF,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAiCA,IAAM,CAAC,uBAAuB,wBAAwB,CAAA,GAAI,qBAMxD,uBAAuB;AAEzB,IAAM,CAAC,oBAAoB,qBAAqB,CAAA,GAAI,qBAGjD,oBAAoB;AAEvB,IAAM,sBAAsB,CAAC,EAAE,QAAA,CAAS,CAAA,KAAyB;IAC/D,MAAM,SAAS,UAAU;IACzB,MAAM,WAAW,YAAY;IAE7B,OAAO,aAAA,GAAAH,4WAAAA,CAAA,aAAA,CAAC,mBAAmB,QAAA,EAAnB;QAA4B,OAAO;YAAE,OAAO;gBAAE;gBAAQ;YAAS;QAAE;IAAA,GAAI,QAAS;AACxF;AAEA,IAAM,mBAAmB,CAAC,EAAE,QAAA,CAAS,CAAA,KAAyB;IAC5D,OAAO,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,mBAAmB,QAAA,EAAnB;QAA4B,OAAO;YAAE,OAAO,CAAC;QAAS;IAAA,GAAI,QAAS;AAC7E;AAEA,IAAM,gBAAgB,CAAC,EAAE,QAAA,EAAU,GAAG,MAAM,CAAA,KAAsD;IAChG,MAAM,QAAQ,sBAAsB,MAAM,GAAG;IAC7C,MAAM,CAAC,uBAAuB,wBAAwB,CAAA,OAAII,6WAAAA,EAAS,KAAK;IACxE,OACE,aAAA,GAAAJ,4WAAAA,CAAA,aAAA,CAAC,sBAAsB,QAAA,EAAtB;QACC,OAAO;YACL,OAAO;gBACL,GAAG,KAAA;gBACH,GAAG,KAAA;gBACH;gBACA;YACF;QACF;IAAA,GAEC;AAGP;AAEA,IAAM,yBAAyB,CAAC,EAAE,QAAA,EAAU,GAAG,MAAM,CAAA,KAAsD;IACzG,OACE,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,oBAAA,MACC,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,eAAA;QAAe,GAAG,KAAA;IAAA,GACjB,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,4BAAA,MAA4B,QAAS,CACxC,CACF;AAEJ;AAEA,IAAM,6BAA6B,CAAC,UAA6B;IAC/D,MAAM,EAAE,MAAA,EAAQ,oBAAA,EAAsB,gBAAA,CAAiB,CAAA,GAAI,yBAAyB;IAEpF,IAAI,UAAU,sBAAsB;QAClC,OACE,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,UAAA;YAEC,KAAK;YACL;YACA,SAAS;gBACP,QAAQ;gBACR,cAAc;gBACd,YAAY;oBACV,WAAW;gBACb;YACF;QAAA,GAEA,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,qBAAA,MAAqB,MAAM,QAAS;IAG3C;IAEA,OAAO,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,kBAAA,MAAkB,MAAM,QAAS;AAC3C;AAEA,IAAMK,kBAAiB,CAAC,EAAE,QAAA,CAAS,CAAA,KAAgC;IACjE,MAAM,EACJ,wBAAA,EACA,kBAAA,EACA,QAAA,EACA,MAAA,EACA,oBAAA,EACA,kBAAA,EACA,KAAK,IAAA,EACP,GAAI,yBAAyB;IAC7B,MAAM,cAAc,uBAAuB;IAE3C,MAAM,eAAWC,4WAAAA,EAAQ,MAAM;QAC7B,IAAI,CAAC,YAAY,CAAC,SAAS,MAAA,IAAU,CAAC,SAAS,IAAA,EAAM;YACnD,OAAO,KAAA;QACT;QAEA,OAAO;YACL,yBAAyB;gBACvB,oBAAoB,sBAAsB;gBAC1C,eACE,SAAS,iBACL,aAAa,cAAc,0BAA0B,KACrD,aAAa,cAAc,kBAAkB;gBACnD,gBAAgB;oBACd,QAAQ,SAAS,MAAA,CAAO,WAAA,EAAa,UAAU,SAAS,MAAA,CAAO,UAAA,CAAW,MAAA;oBAC1E,OAAO,SAAS,IAAA,CAAK,IAAA;oBACrB,8BAA8B,SAAS,UAAA,KAAe,WAAW,SAAS;gBAC5E;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAoB;QAAM,WAAW;KAAC;IAEpD,MAAM,cAAUA,4WAAAA,EAAQ,MAAM;QAC5B,OAAO;YACL,QAAQ;gBACN,MAAM;gBACN,kBAAkB;YACpB;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAU,kBAAkB;KAAC;IAEjC,MAAM,cAAUC,gXAAAA,EAAY,MAAM;QAChC,yBAAyB,IAAI;IAC/B,GAAG;QAAC,wBAAwB;KAAC;IAE7B,IAAI,CAAC,UAAU,CAAC,sBAAsB;QACpC,OAAO,aAAA,GAAAP,4WAAAA,CAAA,aAAA,CAAAA,4WAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;IACrB;IAEA,OACE,aAAA,GAAAA,4WAAAA,CAAA,aAAA,CAAC,gBAAA;QACC;QACA;QACA;IAAA;AAGN;AAEA,IAAM,wBAAwB,MAAM;IAClC,MAAM,IAAI,MACR;AAEJ;AA4BA,IAAM,oBAAoB,MAA+B;IACvD,MAAM,EAAE,qBAAA,EAAuB,uBAAA,CAAwB,CAAA,GAAI,yBAAyB;IACpF,MAAM,EAAE,MAAA,EAAQ,QAAA,CAAS,CAAA,GAAI,sBAAsB;IACnD,MAAM,EAAE,oBAAA,CAAqB,CAAA,GAAI,yBAAyB;IAE1D,MAAM,aAASO,gXAAAA,EAAY,YAAY;QACrC,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB,OAAO,sBAAsB;QAC/B;QAEA,MAAM,EAAE,WAAA,EAAa,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,YAAA,CAAa;YACvD;YACA,eAAe;gBACb,YAAY,OAAO,QAAA,CAAS,IAAA;YAC9B;YACA,UAAU;QACZ,CAAC;QACD,IAAI,OAAO;YACT,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;wBACL,MAAM,MAAM,IAAA;wBACZ,SAAS,MAAM,OAAA;wBACf,MAAM,MAAM,IAAA;oBACd;gBACF;YACF;QACF;QACA,OAAO;YACL,MAAM;gBAAE,SAAS;gBAAU,cAAc,YAAY,cAAA;YAAyB;YAC9E,OAAO;QACT;IACF,GAAG;QAAC;QAAQ,QAAQ;KAAC;IAErB,MAAM,YAAQA,gXAAAA,EAAY,YAAY;QACpC,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB,OAAO,sBAAsB;QAC/B;QAEA,MAAM,wBAAwB;IAChC,GAAG;QAAC;QAAQ;QAAU,uBAAuB;KAAC;IAE9C,MAAM,kBAAkB,QAAQ,UAAU,oBAAoB;IAE9D,IAAI,CAAC,iBAAiB;QACpB,OAAO;YACL,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU,KAAA;YACV,iBAAiB;QACnB;IACF;IACA,OAAO;QACL;QACA;QACA,aAAa;QACb,UAAU;YACR,MAAM;QACR;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3032, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/runtimeEnvironment.ts"], "sourcesContent": ["export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAO,IAAM,2BAA2B,MAAe;IACrD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAIT,OAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;IAC9C,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;IACpD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3065, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/deprecated.ts"], "sourcesContent": ["import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAqBA,IAAM,oBAAoB,aAAA,GAAA,IAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;IACjF,MAAM,kBAAc,sSAAA,CAAkB,UAAK,4SAAA,CAAwB;IACnE,MAAM,YAAY,OAAO;IACzB,IAAI,kBAAkB,GAAA,CAAI,SAAS,KAAK,aAAa;QACnD;IACF;IACA,kBAAkB,GAAA,CAAI,SAAS;IAE/B,QAAQ,IAAA,CACN,CAAA,8BAAA,EAAiC,MAAM,CAAA;AAAA,EAAmE,OAAO,EAAA;AAErH;AAyBO,IAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,KAAA,KAAgB;IAC9G,MAAM,SAAS,WAAW,MAAM,IAAI,SAAA;IAEpC,IAAI,QAAQ,MAAA,CAAO,QAAQ,CAAA;IAC3B,OAAO,cAAA,CAAe,QAAQ,UAAU;QACtC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG,IAAI,IAAI,CAAA,CAAA,EAAI,QAAQ,EAAE;YACvD,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH;AAYO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;IACT,IAAI,QAAQ,GAAA,CAAI,QAAQ,CAAA;IACxB,OAAO,cAAA,CAAe,KAAK,UAAU;QACnC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG;YACjC,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH", "debugId": null}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/allSettled.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/logErrorInDevMode.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/fastDeepMerge.ts", "file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/uuid.ts"], "sourcesContent": ["/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n", "/**\n * Generates a RFC 4122 v4 UUID using the best available source of randomness.\n *\n * Order of preference:\n * - crypto.randomUUID (when available)\n * - crypto.getRandomValues with manual v4 formatting\n * - Math.random-based fallback (not cryptographically secure; last resort)\n */\nexport function generateUuid(): string {\n  const cryptoApi = (globalThis as unknown as { crypto?: Crypto }).crypto;\n\n  if (cryptoApi && typeof (cryptoApi as any).randomUUID === 'function') {\n    return (cryptoApi as any).randomUUID();\n  }\n\n  if (cryptoApi && typeof cryptoApi.getRandomValues === 'function') {\n    const bytes = new Uint8Array(16);\n    cryptoApi.getRandomValues(bytes);\n\n    // Per RFC 4122 §4.4\n    bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4\n    bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant 10\n\n    const hex: string[] = [];\n    for (let i = 0; i < bytes.length; i++) {\n      hex.push((bytes[i] + 0x100).toString(16).substring(1));\n    }\n\n    return (\n      hex[0] +\n      hex[1] +\n      hex[2] +\n      hex[3] +\n      '-' +\n      hex[4] +\n      hex[5] +\n      '-' +\n      hex[6] +\n      hex[7] +\n      '-' +\n      hex[8] +\n      hex[9] +\n      '-' +\n      hex[10] +\n      hex[11] +\n      hex[12] +\n      hex[13] +\n      hex[14] +\n      hex[15]\n    );\n  }\n\n  // Last-resort fallback for very old environments (not cryptographically secure)\n  // Format: 8-4-4-4-12, with version=4 and variant=8|9|a|b\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = Math.floor(Math.random() * 16);\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAIO,SAAS,WACd,QAAA,EACsF;IACtF,MAAM,WAAW,MAAM,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,CAAA,IACxC,EAAE,IAAA,CACA,CAAA,QAAA,CAAU;gBAAE,QAAQ;gBAAa;YAAM,CAAA,GACvC,CAAA,SAAA,CAAW;gBAAE,QAAQ;gBAAY;YAAO,CAAA;IAG5C,OAAO,QAAQ,GAAA,CAAI,QAAQ;AAC7B;;ACZO,IAAM,oBAAoB,CAAC,YAAoB;IACpD,QAAI,6SAAA,CAAyB,IAAG;QAC9B,QAAQ,KAAA,CAAM,CAAA,OAAA,EAAU,OAAO,EAAE;IACnC;AACF;;ACDO,IAAM,0BAA0B,CACrC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,wBAAwB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAClD,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,GAAG;YAC5D,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,qBAAqB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAC/C,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;YACzF,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF;;ACnCO,SAAS,eAAuB;IACrC,MAAM,YAAa,WAA8C,MAAA;IAEjE,IAAI,aAAa,OAAQ,UAAkB,UAAA,KAAe,YAAY;QACpE,OAAQ,UAAkB,UAAA,CAAW;IACvC;IAEA,IAAI,aAAa,OAAO,UAAU,eAAA,KAAoB,YAAY;QAChE,MAAM,QAAQ,IAAI,WAAW,EAAE;QAC/B,UAAU,eAAA,CAAgB,KAAK;QAG/B,KAAA,CAAM,CAAC,CAAA,GAAK,KAAA,CAAM,CAAC,CAAA,GAAI,KAAQ;QAC/B,KAAA,CAAM,CAAC,CAAA,GAAK,KAAA,CAAM,CAAC,CAAA,GAAI,KAAQ;QAE/B,MAAM,MAAgB,CAAC,CAAA;QACvB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;YACrC,IAAI,IAAA,CAAA,CAAM,KAAA,CAAM,CAAC,CAAA,GAAI,GAAA,EAAO,QAAA,CAAS,EAAE,EAAE,SAAA,CAAU,CAAC,CAAC;QACvD;QAEA,OACE,GAAA,CAAI,CAAC,CAAA,GACL,GAAA,CAAI,CAAC,CAAA,GACL,GAAA,CAAI,CAAC,CAAA,GACL,GAAA,CAAI,CAAC,CAAA,GACL,MACA,GAAA,CAAI,CAAC,CAAA,GACL,GAAA,CAAI,CAAC,CAAA,GACL,MACA,GAAA,CAAI,CAAC,CAAA,GACL,GAAA,CAAI,CAAC,CAAA,GACL,MACA,GAAA,CAAI,CAAC,CAAA,GACL,GAAA,CAAI,CAAC,CAAA,GACL,MACA,GAAA,CAAI,EAAE,CAAA,GACN,GAAA,CAAI,EAAE,CAAA,GACN,GAAA,CAAI,EAAE,CAAA,GACN,GAAA,CAAI,EAAE,CAAA,GACN,GAAA,CAAI,EAAE,CAAA,GACN,GAAA,CAAI,EAAE,CAAA;IAEV;IAIA,OAAO,uCAAuC,OAAA,CAAQ,SAAS,CAAA,MAAK;QAClE,MAAM,IAAI,KAAK,KAAA,CAAM,KAAK,MAAA,CAAO,IAAI,EAAE;QACvC,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,IAAO;QACtC,OAAO,EAAE,QAAA,CAAS,EAAE;IACtB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 3222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/instance.ts"], "sourcesContent": ["/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGO,SAAS,UAAU,WAAA,EAA8B;IACtD,OACE,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,iBAAiB,KACtC,YAAY,QAAA,CAAS,oBAAoB;AAE7C", "debugId": null}}, {"offset": {"line": 3236, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/utils/handleValueOrFn.ts"], "sourcesContent": ["type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;;AAGO,SAAS,gBAAmB,KAAA,EAAyB,GAAA,EAAU,YAAA,EAAiC;IACrG,IAAI,OAAO,UAAU,YAAY;QAC/B,OAAQ,MAAwB,GAAG;IACrC;IAEA,IAAI,OAAO,UAAU,aAAa;QAChC,OAAO;IACT;IAEA,IAAI,OAAO,iBAAiB,aAAa;QACvC,OAAO;IACT;IAEA,OAAO,KAAA;AACT", "debugId": null}}, {"offset": {"line": 3259, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/object.ts"], "sourcesContent": ["export const without = <T extends object, P extends keyof T>(obj: T, ...props: P[]): Omit<T, P> => {\n  const copy = { ...obj };\n  for (const prop of props) {\n    delete copy[prop];\n  }\n  return copy;\n};\n\nexport const removeUndefined = <T extends object>(obj: T): Partial<T> => {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined && value !== null) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n};\n\nexport const applyFunctionToObj = <T extends Record<string, any>, R>(\n  obj: T,\n  fn: (val: any, key: string) => R,\n): Record<string, R> => {\n  const result = {} as Record<string, R>;\n  for (const key in obj) {\n    result[key] = fn(obj[key], key);\n  }\n  return result;\n};\n\nexport const filterProps = <T extends Record<string, any>>(obj: T, filter: (a: any) => boolean): T => {\n  const result = {} as T;\n  for (const key in obj) {\n    if (obj[key] && filter(obj[key])) {\n      result[key] = obj[key];\n    }\n  }\n  return result;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAO,IAAM,UAAU,CAAsC,KAAA,GAAW,UAA2B;IACjG,MAAM,OAAO;QAAE,GAAG,GAAA;IAAI;IACtB,KAAA,MAAW,QAAQ,MAAO;QACxB,OAAO,IAAA,CAAK,IAAI,CAAA;IAClB;IACA,OAAO;AACT;AAEO,IAAM,kBAAkB,CAAmB,QAAuB;IACvE,OAAO,OAAO,OAAA,CAAQ,GAAG,EAAE,MAAA,CAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAA,KAAM;QACvD,IAAI,UAAU,KAAA,KAAa,UAAU,MAAM;YACzC,GAAA,CAAI,GAAc,CAAA,GAAI;QACxB;QACA,OAAO;IACT,GAAG,CAAC,CAAe;AACrB;AAEO,IAAM,qBAAqB,CAChC,KACA,OACsB;IACtB,MAAM,SAAS,CAAC;IAChB,IAAA,MAAW,OAAO,IAAK;QACrB,MAAA,CAAO,GAAG,CAAA,GAAI,GAAG,GAAA,CAAI,GAAG,CAAA,EAAG,GAAG;IAChC;IACA,OAAO;AACT;AAEO,IAAM,cAAc,CAAgC,KAAQ,WAAmC;IACpG,MAAM,SAAS,CAAC;IAChB,IAAA,MAAW,OAAO,IAAK;QACrB,IAAI,GAAA,CAAI,GAAG,CAAA,IAAK,OAAO,GAAA,CAAI,GAAG,CAAC,GAAG;YAChC,MAAA,CAAO,GAAG,CAAA,GAAI,GAAA,CAAI,GAAG,CAAA;QACvB;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3330, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/versionSelector.ts"], "sourcesContent": ["/**\n * This version selector is a bit complicated, so here is the flow:\n * 1. Use the clerkJSVersion prop on the provider\n * 2. Use the exact `@clerk/clerk-js` version if it is a `@snapshot` prerelease\n * 3. Use the prerelease tag of `@clerk/clerk-js` or the packageVersion provided\n * 4. Fallback to the major version of `@clerk/clerk-js` or the packageVersion provided\n * @param clerkJSVersion - The optional clerkJSVersion prop on the provider\n * @param packageVersion - The version of `@clerk/clerk-js` that will be used if an explicit version is not provided\n * @returns The npm tag, version or major version to use\n */\nexport const versionSelector = (clerkJSVersion: string | undefined, packageVersion = JS_PACKAGE_VERSION) => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n\n  const prereleaseTag = getPrereleaseTag(packageVersion);\n  if (prereleaseTag) {\n    if (prereleaseTag === 'snapshot') {\n      return JS_PACKAGE_VERSION;\n    }\n\n    return prereleaseTag;\n  }\n\n  return getMajorVersion(packageVersion);\n};\n\nconst getPrereleaseTag = (packageVersion: string) =>\n  packageVersion\n    .trim()\n    .replace(/^v/, '')\n    .match(/-(.+?)(\\.|$)/)?.[1];\n\nexport const getMajorVersion = (packageVersion: string) => packageVersion.trim().replace(/^v/, '').split('.')[0];\n"], "names": [], "mappings": ";;;;;;;AAUO,IAAM,kBAAkB,CAAC,gBAAoC,iBAAiB,QAAA,KAAuB;IAC1G,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,MAAM,gBAAgB,iBAAiB,cAAc;IACrD,IAAI,eAAe;QACjB,IAAI,kBAAkB,YAAY;YAChC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,OAAO,gBAAgB,cAAc;AACvC;AAEA,IAAM,mBAAmB,CAAC,iBACxB,eACG,IAAA,CAAK,EACL,OAAA,CAAQ,MAAM,EAAE,EAChB,KAAA,CAAM,cAAc,GAAA,CAAI,CAAC,CAAA;AAEvB,IAAM,kBAAkB,CAAC,iBAA2B,eAAe,IAAA,CAAK,EAAE,OAAA,CAAQ,MAAM,EAAE,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA", "debugId": null}}, {"offset": {"line": 3369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/proxy.ts"], "sourcesContent": ["export function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAO,SAAS,gBAAgB,GAAA,EAAyB;IACvD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,OAAO,cAAc,GAAG,KAAK,mBAAmB,GAAG;AACrD;AAEO,SAAS,cAAc,GAAA,EAAyB;IACrD,OAAO,iBAAiB,IAAA,CAAK,OAAO,EAAE;AACxC;AAEO,SAAS,mBAAmB,GAAA,EAAa;IAC9C,OAAO,IAAI,UAAA,CAAW,GAAG;AAC3B;AAEO,SAAS,sBAAsB,GAAA,EAAiC;IACrE,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,OAAO,mBAAmB,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,QAAA,CAAS,MAAM,EAAE,QAAA,CAAS,IAAI;AACrF", "debugId": null}}, {"offset": {"line": 3404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/url.ts"], "sourcesContent": ["import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n"], "names": ["url"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,kBAAkB,cAAc,EAAA,EAAqB;IACnE,IAAI,YAAY,UAAA,CAAW,GAAG,GAAG;QAC/B,cAAc,YAAY,KAAA,CAAM,CAAC;IACnC;IACA,OAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,EAAA,EAAY;IAC5C,OAAA,CAAQ,OAAO,EAAA,EAAI,OAAA,CAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,GAAA,EAAyB;IACtD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI;IACJ,IAAI,IAAI,KAAA,CAAM,iBAAiB,GAAG;QAChC,QAAQ;IACV,OAAA,IAAW,IAAI,KAAA,CAAM,kBAAkB,GAAG;QACxC,OAAO;IACT,OAAO;QACL,QAAQ;IACV;IAEA,MAAM,WAAW,IAAI,OAAA,CAAQ,OAAO,EAAE;IACtC,OAAO,CAAA,MAAA,EAAS,QAAQ,EAAA;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,YAAqB;IACpF,IAAI,CAAC,eAAW,8RAAA,EAAU,WAAW,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,OAAO,QAAQ,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK;AAClC;AAOO,IAAM,eAAe,CAAC,aAAqB,EAAE,cAAA,CAAe,CAAA,KAAmC;IACpG,MAAM,sBAAsB,YAAY,OAAA,CAAQ,iBAAiB,EAAE;IACnE,MAAM,QAAQ,4BAA4B,aAAa,cAAc;IACrE,OAAO,CAAA,QAAA,EAAW,mBAAmB,CAAA,qBAAA,EAAwB,kBAAkB,KAAK,CAAA,sBAAA,CAAA;AACtF;AAMO,SAAS,+BAA+B,IAAA,EAAuB;IACpE,OAAO,iTAAA,CAA6B,IAAA,CAAK,CAAA,oBAAmB;QAC1D,OAAO,KAAK,UAAA,CAAW,WAAW,KAAK,KAAK,QAAA,CAAS,eAAe;IACtE,CAAC;AACH;AAQO,SAAS,gCAAgC,IAAA,EAAuB;IACrE,OAAO,kTAAA,CAA8B,IAAA,CAAK,CAAA,qBAAoB;QAC5D,OAAO,KAAK,QAAA,CAAS,gBAAgB,KAAK,CAAC,KAAK,QAAA,CAAS,WAAW,gBAAgB;IACtF,CAAC;AACH;AAIA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,EAAA,EAAI,uBAAA,EAA4C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG;IAC3B;IACA,OAAO,kBAAkB,IAAA,CAAK,KAAK;AACrC;AAEO,SAAS,kBAAkB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG,IAAI,QAAQ,QAAQ;IAC/C;IACA,IAAI,iBAAiB,OAAO,IAAI,GAAG;QACjC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;QACpC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;IACF;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAO,KAAK,MAAA,CAAO,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9D;AAEO,SAAS,qBAAqB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IAC1F,IAAI,CAAC,yBAAyB;QAC5B,OAAA,CAAQ,iBAAiB,KAAK,IAAI,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI,KAAA,KAAU;IACnE;IACA,IAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;QAClC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;IACtC;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAA,CAAQ,GAAG,KAAA,CAAM,GAAG,CAAA,CAAE,KAAK,GAAA,IAAA,CAAQ,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,EAAA,EAAa;IACnD,OAAO,MAAM,UAAA,CAAW,GAAG;AAC7B;AAEO,SAAS,oBAAoB,QAAQ,EAAA,EAAY;IACtD,OAAA,CAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAA,CAAM,CAAC,IAAI,KAAA,KAAU;AAC9D;AAEO,SAAS,iBAAiB,QAAQ,EAAA,EAAY;IACnD,OAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAEO,SAAS,mBAAmB,QAAQ,EAAA,EAAY;IACrD,OAAO,MACJ,KAAA,CAAM,KAAK,EACX,GAAA,CAAI,CAAA,UAAW,QAAQ,OAAA,CAAQ,WAAW,GAAG,CAAC,EAC9C,IAAA,CAAK,KAAK;AACf;AAEO,SAAS,cAAc,GAAA,EAAa;IACzC,OAAO,OAAO,QAAQ;AACxB;AAEA,IAAM,wBAAwB;AAEvB,SAAS,QAAQ,IAAA,EAAA,GAAiB,KAAA,EAAyB;IAChE,IAAI,MAAM,QAAQ;IAElB,KAAA,MAAW,WAAW,MAAM,MAAA,CAAO,CAAAA,OAAO,cAAcA,IAAG,CAAC,EAAG;QAC7D,IAAI,KAAK;YAEP,MAAM,WAAW,QAAQ,OAAA,CAAQ,uBAAuB,EAAE;YAC1D,MAAM,kBAAkB,GAAG,IAAI;QACjC,OAAO;YACL,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAMA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,MAAgB,mBAAmB,IAAA,CAAK,GAAG", "debugId": null}}, {"offset": {"line": 3573, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/retry.ts"], "sourcesContent": ["type Milliseconds = number;\n\ntype RetryOptions = Partial<{\n  /**\n   * The initial delay before the first retry.\n   * @default 125\n   */\n  initialDelay: Milliseconds;\n  /**\n   * The maximum delay between retries.\n   * The delay between retries will never exceed this value.\n   * If set to 0, the delay will increase indefinitely.\n   * @default 0\n   */\n  maxDelayBetweenRetries: Milliseconds;\n  /**\n   * The multiplier for the exponential backoff.\n   * @default 2\n   */\n  factor: number;\n  /**\n   * A function to determine if the operation should be retried.\n   * The callback accepts the error that was thrown and the number of iterations.\n   * The iterations variable references the number of retries AFTER attempt\n   * that caused the error and starts at 1 (as in, this is the 1st, 2nd, nth retry).\n   * @default (error, iterations) => iterations < 5\n   */\n  shouldRetry: (error: unknown, iterations: number) => boolean;\n  /**\n   * Controls whether the helper should retry the operation immediately once before applying exponential backoff.\n   * The delay for the immediate retry is 100ms.\n   * @default false\n   */\n  retryImmediately: boolean;\n  /**\n   * If true, the intervals will be multiplied by a factor in the range of [1,2].\n   * @default true\n   */\n  jitter: boolean;\n}>;\n\nconst defaultOptions: Required<RetryOptions> = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_: unknown, iteration: number) => iteration < 5,\n  retryImmediately: false,\n  jitter: true,\n};\n\nconst RETRY_IMMEDIATELY_DELAY = 100;\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst applyJitter = (delay: Milliseconds, jitter: boolean) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\n\nconst createExponentialDelayAsyncFn = (\n  opts: Required<Pick<RetryOptions, 'initialDelay' | 'maxDelayBetweenRetries' | 'factor' | 'jitter'>>,\n) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\n/**\n * Retries a callback until it succeeds or the shouldRetry function returns false.\n * See {@link RetryOptions} for the available options.\n */\nexport const retry = async <T>(callback: () => T | Promise<T>, options: RetryOptions = {}): Promise<T> => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter,\n  });\n\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;AAyCA,IAAM,iBAAyC;IAC7C,cAAc;IACd,wBAAwB;IACxB,QAAQ;IACR,aAAa,CAAC,GAAY,YAAsB,YAAY;IAC5D,kBAAkB;IAClB,QAAQ;AACV;AAEA,IAAM,0BAA0B;AAEhC,IAAM,QAAQ,OAAO,KAAqB,IAAI,QAAQ,CAAA,IAAK,WAAW,GAAG,EAAE,CAAC;AAE5E,IAAM,cAAc,CAAC,OAAqB,WAAoB;IAC5D,OAAO,SAAS,QAAA,CAAS,IAAI,KAAK,MAAA,CAAO,CAAA,IAAK;AAChD;AAEA,IAAM,gCAAgC,CACpC,SACG;IACH,IAAI,cAAc;IAElB,MAAM,qBAAqB,MAAM;QAC/B,MAAM,WAAW,KAAK,YAAA;QACtB,MAAM,OAAO,KAAK,MAAA;QAClB,IAAI,QAAQ,WAAW,KAAK,GAAA,CAAI,MAAM,WAAW;QACjD,QAAQ,YAAY,OAAO,KAAK,MAAM;QACtC,OAAO,KAAK,GAAA,CAAI,KAAK,sBAAA,IAA0B,OAAO,KAAK;IAC7D;IAEA,OAAO,YAA2B;QAChC,MAAM,MAAM,mBAAmB,CAAC;QAChC;IACF;AACF;AAMO,IAAM,QAAQ,OAAU,UAAgC,UAAwB,CAAC,CAAA,KAAkB;IACxG,IAAI,aAAa;IACjB,MAAM,EAAE,WAAA,EAAa,YAAA,EAAc,sBAAA,EAAwB,MAAA,EAAQ,gBAAA,EAAkB,MAAA,CAAO,CAAA,GAAI;QAC9F,GAAG,cAAA;QACH,GAAG,OAAA;IACL;IAEA,MAAM,QAAQ,8BAA8B;QAC1C;QACA;QACA;QACA;IACF,CAAC;IAED,MAAO,KAAM;QACX,IAAI;YACF,OAAO,MAAM,SAAS;QACxB,EAAA,OAAS,GAAG;YACV;YACA,IAAI,CAAC,YAAY,GAAG,UAAU,GAAG;gBAC/B,MAAM;YACR;YACA,IAAI,oBAAoB,eAAe,GAAG;gBACxC,MAAM,MAAM,YAAY,yBAAyB,MAAM,CAAC;YAC1D,OAAO;gBACL,MAAM,MAAM;YACd;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3639, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/loadScript.ts"], "sourcesContent": ["import { retry } from './retry';\n\nconst NO_DOCUMENT_ERROR = 'loadScript cannot be called when document does not exist';\nconst NO_SRC_ERROR = 'loadScript cannot be called without a src';\n\ntype LoadScriptOptions = {\n  async?: boolean;\n  defer?: boolean;\n  crossOrigin?: 'anonymous' | 'use-credentials';\n  nonce?: string;\n  beforeLoad?: (script: HTMLScriptElement) => void;\n};\n\nexport async function loadScript(src = '', opts: LoadScriptOptions): Promise<HTMLScriptElement> {\n  const { async, defer, beforeLoad, crossOrigin, nonce } = opts || {};\n\n  const load = () => {\n    return new Promise<HTMLScriptElement>((resolve, reject) => {\n      if (!src) {\n        reject(new Error(NO_SRC_ERROR));\n      }\n\n      if (!document || !document.body) {\n        reject(NO_DOCUMENT_ERROR);\n      }\n\n      const script = document.createElement('script');\n\n      if (crossOrigin) script.setAttribute('crossorigin', crossOrigin);\n      script.async = async || false;\n      script.defer = defer || false;\n\n      script.addEventListener('load', () => {\n        script.remove();\n        resolve(script);\n      });\n\n      script.addEventListener('error', () => {\n        script.remove();\n        reject();\n      });\n\n      script.src = src;\n      script.nonce = nonce;\n      beforeLoad?.(script);\n      document.body.appendChild(script);\n    });\n  };\n\n  return retry(load, { shouldRetry: (_, iterations) => iterations <= 5 });\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AAUrB,eAAsB,WAAW,MAAM,EAAA,EAAI,IAAA,EAAqD;IAC9F,MAAM,EAAE,KAAA,EAAO,KAAA,EAAO,UAAA,EAAY,WAAA,EAAa,KAAA,CAAM,CAAA,GAAI,QAAQ,CAAC;IAElE,MAAM,OAAO,MAAM;QACjB,OAAO,IAAI,QAA2B,CAAC,SAAS,WAAW;YACzD,IAAI,CAAC,KAAK;gBACR,OAAO,IAAI,MAAM,YAAY,CAAC;YAChC;YAEA,IAAI,CAAC,YAAY,CAAC,SAAS,IAAA,EAAM;gBAC/B,OAAO,iBAAiB;YAC1B;YAEA,MAAM,SAAS,SAAS,aAAA,CAAc,QAAQ;YAE9C,IAAI,YAAa,CAAA,OAAO,YAAA,CAAa,eAAe,WAAW;YAC/D,OAAO,KAAA,GAAQ,SAAS;YACxB,OAAO,KAAA,GAAQ,SAAS;YAExB,OAAO,gBAAA,CAAiB,QAAQ,MAAM;gBACpC,OAAO,MAAA,CAAO;gBACd,QAAQ,MAAM;YAChB,CAAC;YAED,OAAO,gBAAA,CAAiB,SAAS,MAAM;gBACrC,OAAO,MAAA,CAAO;gBACd,OAAO;YACT,CAAC;YAED,OAAO,GAAA,GAAM;YACb,OAAO,KAAA,GAAQ;YACf,aAAa,MAAM;YACnB,SAAS,IAAA,CAAK,WAAA,CAAY,MAAM;QAClC,CAAC;IACH;IAEA,WAAO,0RAAA,EAAM,MAAM;QAAE,aAAa,CAAC,GAAG,aAAe,cAAc;IAAE,CAAC;AACxE", "debugId": null}}, {"offset": {"line": 3686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/loadClerkJsScript.ts"], "sourcesContent": ["import type { ClerkOptions, SDKMetadata, Without } from '@clerk/types';\n\nimport { buildErrorThrower } from './error';\nimport { createDevOrStagingUrlCache, parsePublishableKey } from './keys';\nimport { loadScript } from './loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy';\nimport { addClerkPrefix } from './url';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\nconst { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/shared' });\n\n/**\n * Sets the package name for error messages during ClerkJS script loading.\n *\n * @param packageName - The name of the package to use in error messages (e.g., '@clerk/clerk-react').\n * @example\n * ```typescript\n * setClerkJsLoadingErrorPackageName('@clerk/clerk-react');\n * ```\n */\nexport function setClerkJsLoadingErrorPackageName(packageName: string) {\n  errorThrower.setPackageName({ packageName });\n}\n\ntype LoadClerkJsScriptOptions = Without<ClerkOptions, 'isSatellite'> & {\n  publishableKey: string;\n  clerkJSUrl?: string;\n  clerkJSVariant?: 'headless' | '';\n  clerkJSVersion?: string;\n  sdkMetadata?: SDKMetadata;\n  proxyUrl?: string;\n  domain?: string;\n  nonce?: string;\n  /**\n   * Timeout in milliseconds to wait for clerk-js to load before considering it failed.\n   *\n   * @default 15000 (15 seconds)\n   */\n  scriptLoadTimeout?: number;\n};\n\n/**\n * Validates that window.Clerk exists and is properly initialized.\n * This ensures we don't have false positives where the script loads but Clerk is malformed.\n *\n * @returns `true` if window.Clerk exists and has the expected structure with a load method.\n */\nfunction isClerkProperlyLoaded(): boolean {\n  if (typeof window === 'undefined' || !(window as any).Clerk) {\n    return false;\n  }\n\n  // Basic validation that window.Clerk has the expected structure\n  const clerk = (window as any).Clerk;\n  return typeof clerk === 'object' && typeof clerk.load === 'function';\n}\n\n/**\n * Waits for Clerk to be properly loaded with a timeout mechanism.\n * Uses polling to check if Clerk becomes available within the specified timeout.\n *\n * @param timeoutMs - Maximum time to wait in milliseconds.\n * @returns Promise that resolves with null if Clerk loads successfully, or rejects with an error if timeout is reached.\n */\nfunction waitForClerkWithTimeout(timeoutMs: number): Promise<HTMLScriptElement | null> {\n  return new Promise((resolve, reject) => {\n    let resolved = false;\n\n    const cleanup = (timeoutId: ReturnType<typeof setTimeout>, pollInterval: ReturnType<typeof setInterval>) => {\n      clearTimeout(timeoutId);\n      clearInterval(pollInterval);\n    };\n\n    const checkAndResolve = () => {\n      if (resolved) return;\n\n      if (isClerkProperlyLoaded()) {\n        resolved = true;\n        cleanup(timeoutId, pollInterval);\n        resolve(null);\n      }\n    };\n\n    const handleTimeout = () => {\n      if (resolved) return;\n\n      resolved = true;\n      cleanup(timeoutId, pollInterval);\n\n      if (!isClerkProperlyLoaded()) {\n        reject(new Error(FAILED_TO_LOAD_ERROR));\n      } else {\n        resolve(null);\n      }\n    };\n\n    const timeoutId = setTimeout(handleTimeout, timeoutMs);\n\n    checkAndResolve();\n\n    const pollInterval = setInterval(() => {\n      if (resolved) {\n        clearInterval(pollInterval);\n        return;\n      }\n      checkAndResolve();\n    }, 100);\n  });\n}\n\n/**\n * Hotloads the Clerk JS script with robust failure detection.\n *\n * Uses a timeout-based approach to ensure absolute certainty about load success/failure.\n * If the script fails to load within the timeout period, or loads but doesn't create\n * a proper Clerk instance, the promise rejects with an error.\n *\n * @param opts - The options used to build the Clerk JS script URL and load the script.\n *               Must include a `publishableKey` if no existing script is found.\n * @returns Promise that resolves with null if Clerk loads successfully, or rejects with an error.\n *\n * @example\n * ```typescript\n * try {\n *   await loadClerkJsScript({ publishableKey: 'pk_test_...' });\n *   console.log('Clerk loaded successfully');\n * } catch (error) {\n *   console.error('Failed to load Clerk:', error.message);\n * }\n * ```\n */\nconst loadClerkJsScript = async (opts?: LoadClerkJsScriptOptions): Promise<HTMLScriptElement | null> => {\n  const timeout = opts?.scriptLoadTimeout ?? 15000;\n\n  if (isClerkProperlyLoaded()) {\n    return null;\n  }\n\n  const existingScript = document.querySelector<HTMLScriptElement>('script[data-clerk-js-script]');\n\n  if (existingScript) {\n    return waitForClerkWithTimeout(timeout);\n  }\n\n  if (!opts?.publishableKey) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n\n  const loadPromise = waitForClerkWithTimeout(timeout);\n\n  loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    nonce: opts.nonce,\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n\n  return loadPromise;\n};\n\n/**\n * Generates a Clerk JS script URL based on the provided options.\n *\n * @param opts - The options to use when building the Clerk JS script URL.\n * @returns The complete URL to the Clerk JS script.\n *\n * @example\n * ```typescript\n * const url = clerkJsScriptUrl({ publishableKey: 'pk_test_...' });\n * // Returns: \"https://example.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js\"\n * ```\n */\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\n/**\n * Builds an object of Clerk JS script attributes based on the provided options.\n *\n * @param options - The options containing the values for script attributes.\n * @returns An object containing data attributes to be applied to the script element.\n */\nconst buildClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => {\n  const obj: Record<string, string> = {};\n\n  if (options.publishableKey) {\n    obj['data-clerk-publishable-key'] = options.publishableKey;\n  }\n\n  if (options.proxyUrl) {\n    obj['data-clerk-proxy-url'] = options.proxyUrl;\n  }\n\n  if (options.domain) {\n    obj['data-clerk-domain'] = options.domain;\n  }\n\n  if (options.nonce) {\n    obj.nonce = options.nonce;\n  }\n\n  return obj;\n};\n\n/**\n * Returns a function that applies Clerk JS script attributes to a script element.\n *\n * @param options - The options containing the values for script attributes.\n * @returns A function that accepts a script element and applies the attributes to it.\n */\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const attributes = buildClerkJsScriptAttributes(options);\n  for (const attribute in attributes) {\n    script.setAttribute(attribute, attributes[attribute]);\n  }\n};\n\nexport { loadClerkJsScript, buildClerkJsScriptAttributes, clerkJsScriptUrl };\nexport type { LoadClerkJsScriptOptions };\n"], "names": ["timeoutId", "pollInterval"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,uBAAuB;AAE7B,IAAM,EAAE,iBAAA,CAAkB,CAAA,OAAI,+SAAA,CAA2B;AAEzD,IAAM,mBAAe,sSAAA,EAAkB;IAAE,aAAa;AAAgB,CAAC;AAWhE,SAAS,kCAAkC,WAAA,EAAqB;IACrE,aAAa,cAAA,CAAe;QAAE;IAAY,CAAC;AAC7C;AAyBA,SAAS,wBAAiC;IACxC,IAAI,OAAO,WAAW,eAAe,CAAE,EAAsB,KAAP;QACpD,OAAO;IACT;;;IAGA,MAAM,QAAS,OAAe;AAEhC;AASA,SAAS,wBAAwB,SAAA,EAAsD;IACrF,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;QACtC,IAAI,WAAW;QAEf,MAAM,UAAU,CAACA,YAA0CC,kBAAiD;YAC1G,aAAaD,UAAS;YACtB,cAAcC,aAAY;QAC5B;QAEA,MAAM,kBAAkB,MAAM;YAC5B,IAAI,SAAU,CAAA;YAEd,IAAI,sBAAsB,GAAG;gBAC3B,WAAW;gBACX,QAAQ,WAAW,YAAY;gBAC/B,QAAQ,IAAI;YACd;QACF;QAEA,MAAM,gBAAgB,MAAM;YAC1B,IAAI,SAAU,CAAA;YAEd,WAAW;YACX,QAAQ,WAAW,YAAY;YAE/B,IAAI,CAAC,sBAAsB,GAAG;gBAC5B,OAAO,IAAI,MAAM,oBAAoB,CAAC;YACxC,OAAO;gBACL,QAAQ,IAAI;YACd;QACF;QAEA,MAAM,YAAY,WAAW,eAAe,SAAS;QAErD,gBAAgB;QAEhB,MAAM,eAAe,YAAY,MAAM;YACrC,IAAI,UAAU;gBACZ,cAAc,YAAY;gBAC1B;YACF;YACA,gBAAgB;QAClB,GAAG,GAAG;IACR,CAAC;AACH;AAuBA,IAAM,oBAAoB,OAAO,SAAuE;IACtG,MAAM,UAAU,MAAM,qBAAqB;IAE3C,IAAI,sBAAsB,GAAG;QAC3B,OAAO;IACT;IAEA,MAAM,iBAAiB,SAAS,aAAA,CAAiC,8BAA8B;IAE/F,IAAI,gBAAgB;QAClB,OAAO,wBAAwB,OAAO;IACxC;IAEA,IAAI,CAAC,MAAM,gBAAgB;QACzB,aAAa,+BAAA,CAAgC;QAC7C,OAAO;IACT;IAEA,MAAM,cAAc,wBAAwB,OAAO;IAEnD,IAAA,+RAAA,EAAW,iBAAiB,IAAI,GAAG;QACjC,OAAO;QACP,aAAa;QACb,OAAO,KAAK,KAAA;QACZ,YAAY,6BAA6B,IAAI;IAC/C,CAAC,EAAE,KAAA,CAAM,MAAM;QACb,MAAM,IAAI,MAAM,oBAAoB;IACtC,CAAC;IAED,OAAO;AACT;AAcA,IAAM,mBAAmB,CAAC,SAAmC;IAC3D,MAAM,EAAE,UAAA,EAAY,cAAA,EAAgB,cAAA,EAAgB,QAAA,EAAU,MAAA,EAAQ,cAAA,CAAe,CAAA,GAAI;IAEzF,IAAI,YAAY;QACd,OAAO;IACT;IAEA,IAAI,aAAa;IACjB,IAAI,CAAC,CAAC,gBAAY,oSAAA,EAAgB,QAAQ,GAAG;QAC3C,iBAAa,0SAAA,EAAsB,QAAQ,EAAE,OAAA,CAAQ,iBAAiB,EAAE;IAC1E,OAAA,IAAW,UAAU,CAAC,sBAAkB,wSAAA,EAAoB,cAAc,GAAG,eAAe,EAAE,GAAG;QAC/F,iBAAa,mSAAA,EAAe,MAAM;IACpC,OAAO;QACL,iBAAa,wSAAA,EAAoB,cAAc,GAAG,eAAe;IACnE;IAEA,MAAM,UAAU,iBAAiB,GAAG,eAAe,OAAA,CAAQ,QAAQ,EAAE,CAAC,CAAA,CAAA,CAAA,GAAM;IAC5E,MAAM,cAAU,oSAAA,EAAgB,cAAc;IAC9C,OAAO,CAAA,QAAA,EAAW,UAAU,CAAA,qBAAA,EAAwB,OAAO,CAAA,YAAA,EAAe,OAAO,CAAA,UAAA,CAAA;AACnF;AAQA,IAAM,+BAA+B,CAAC,YAAsC;IAC1E,MAAM,MAA8B,CAAC;IAErC,IAAI,QAAQ,cAAA,EAAgB;QAC1B,GAAA,CAAI,4BAA4B,CAAA,GAAI,QAAQ,cAAA;IAC9C;IAEA,IAAI,QAAQ,QAAA,EAAU;QACpB,GAAA,CAAI,sBAAsB,CAAA,GAAI,QAAQ,QAAA;IACxC;IAEA,IAAI,QAAQ,MAAA,EAAQ;QAClB,GAAA,CAAI,mBAAmB,CAAA,GAAI,QAAQ,MAAA;IACrC;IAEA,IAAI,QAAQ,KAAA,EAAO;QACjB,IAAI,KAAA,GAAQ,QAAQ,KAAA;IACtB;IAEA,OAAO;AACT;AAQA,IAAM,+BAA+B,CAAC,UAAsC,CAAC,WAA8B;QACzG,MAAM,aAAa,6BAA6B,OAAO;QACvD,IAAA,MAAW,aAAa,WAAY;YAClC,OAAO,YAAA,CAAa,WAAW,UAAA,CAAW,SAAS,CAAC;QACtD;IACF", "debugId": null}}, {"offset": {"line": 3832, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3865, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3882, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/deriveState.ts"], "sourcesContent": ["import type {\n  InitialState,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  OrganizationResource,\n  Resources,\n  SignedInSessionResource,\n  UserResource,\n} from '@clerk/types';\n\n/**\n * Derives authentication state based on the current rendering context (SSR or client-side).\n */\nexport const deriveState = (clerkOperational: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkOperational && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const sessionStatus = initialState.sessionStatus;\n  const sessionClaims = initialState.sessionClaims;\n  const session = initialState.session as SignedInSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as OrganizationCustomRoleKey;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n  const factorVerificationAge = initialState.factorVerificationAge;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    factorVerificationAge,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const sessionStatus = state.session?.status;\n  const sessionClaims: JwtPayload | null | undefined = state.session\n    ? state.session.lastActiveToken?.jwt?.claims\n    : null;\n  const factorVerificationAge: [number, number] | null = state.session ? state.session.factorVerificationAge : null;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    factorVerificationAge,\n  };\n};\n"], "names": [], "mappings": ";;;;;AAcO,IAAM,cAAc,CAAC,kBAA2B,OAAkB,iBAA2C;IAClH,IAAI,CAAC,oBAAoB,cAAc;QACrC,OAAO,0BAA0B,YAAY;IAC/C;IACA,OAAO,0BAA0B,KAAK;AACxC;AAEA,IAAM,4BAA4B,CAAC,iBAA+B;IAChE,MAAM,SAAS,aAAa,MAAA;IAC5B,MAAM,OAAO,aAAa,IAAA;IAC1B,MAAM,YAAY,aAAa,SAAA;IAC/B,MAAM,gBAAgB,aAAa,aAAA;IACnC,MAAM,gBAAgB,aAAa,aAAA;IACnC,MAAM,UAAU,aAAa,OAAA;IAC7B,MAAM,eAAe,aAAa,YAAA;IAClC,MAAM,QAAQ,aAAa,KAAA;IAC3B,MAAM,UAAU,aAAa,OAAA;IAC7B,MAAM,iBAAiB,aAAa,cAAA;IACpC,MAAM,UAAU,aAAa,OAAA;IAC7B,MAAM,QAAQ,aAAa,KAAA;IAC3B,MAAM,wBAAwB,aAAa,qBAAA;IAE3C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,IAAM,4BAA4B,CAAC,UAAqB;IACtD,MAAM,SAAoC,MAAM,IAAA,GAAO,MAAM,IAAA,CAAK,EAAA,GAAK,MAAM,IAAA;IAC7E,MAAM,OAAO,MAAM,IAAA;IACnB,MAAM,YAAuC,MAAM,OAAA,GAAU,MAAM,OAAA,CAAQ,EAAA,GAAK,MAAM,OAAA;IACtF,MAAM,UAAU,MAAM,OAAA;IACtB,MAAM,gBAAgB,MAAM,OAAA,EAAS;IACrC,MAAM,gBAA+C,MAAM,OAAA,GACvD,MAAM,OAAA,CAAQ,eAAA,EAAiB,KAAK,SACpC;IACJ,MAAM,wBAAiD,MAAM,OAAA,GAAU,MAAM,OAAA,CAAQ,qBAAA,GAAwB;IAC7G,MAAM,QAAQ,SAAS;IACvB,MAAM,eAAe,MAAM,YAAA;IAC3B,MAAM,QAAmC,MAAM,YAAA,GAAe,MAAM,YAAA,CAAa,EAAA,GAAK,MAAM,YAAA;IAC5F,MAAM,UAAU,cAAc;IAC9B,MAAM,aAAa,eACf,MAAM,yBAAyB,KAAK,CAAA,KAAM,GAAG,YAAA,CAAa,EAAA,KAAO,KAAK,IACtE;IACJ,MAAM,iBAAiB,aAAa,WAAW,WAAA,GAAc;IAC7D,MAAM,UAAU,aAAa,WAAW,IAAA,GAAO;IAE/C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3960, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3971, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/browser.ts"], "sourcesContent": ["/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAIO,SAAS,YAAqB;IACnC,OAAO,OAAO,SAAW;AAC3B;AAEA,IAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AACA,IAAM,gBAAgB,IAAI,OAAO,UAAU,IAAA,CAAK,GAAG,GAAG,GAAG;AAOlD,SAAS,iBAAiB,SAAA,EAA4B;IAC3D,OAAO,CAAC,YAAY,QAAQ,cAAc,IAAA,CAAK,SAAS;AAC1D;AAMO,SAAS,iBAA0B;IACxC,MAAM,YAAY,UAAU,IAAI,QAAQ,kBAAY;IACpD,IAAI,CAAC,mCAAW;QACd,OAAO;IACT;;;AAEF;AAMO,SAAS,kBAA2B;IACzC,MAAM,YAAY,UAAU,IAAI,QAAQ,kBAAY;IACpD,IAAI,CAAC,mCAAW;QACd,OAAO;IACT;;;IAEA,MAAM,oBAAoB,WAAW;IAKrC,MAAM,iCAAiC,WAAW,YAAY,QAAQ,KAAK,WAAW,YAAY,aAAa;AAEjH;AAMO,SAAS,uBAAgC;IAC9C,OAAO,gBAAgB,KAAK,eAAe;AAC7C", "debugId": null}}, {"offset": {"line": 4048, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/eventBus.ts"], "sourcesContent": ["/**\n * Type definition for event handler functions\n */\ntype EventHandler<Events extends Record<string, unknown>, Key extends keyof Events> = (payload: Events[Key]) => void;\n\n/**\n * @interface\n * Strongly-typed event bus interface that enables type-safe publish/subscribe patterns\n */\ntype EventBus<Events extends Record<string, unknown>> = {\n  /**\n   * Subscribe to an event\n   *\n   * @param event - The event name to subscribe to\n   * @param handler - The callback function to execute when the event is dispatched\n   * @param opts - Optional configuration\n   * @param opts.notify - If true and the event was previously dispatched, handler will be called immediately with the latest payload\n   * @returns void\n   */\n  on: <Event extends keyof Events>(\n    event: Event,\n    handler: EventHandler<Events, Event>,\n    opts?: { notify?: boolean },\n  ) => void;\n\n  /**\n   * Subscribe to an event with pre-dispatch priority\n   * Pre-dispatch handlers are called before regular event handlers when an event is dispatched\n   *\n   * @param event - The event name to subscribe to\n   * @param handler - The callback function to execute when the event is dispatched\n   * @returns void\n   */\n  prioritizedOn: <Event extends keyof Events>(event: Event, handler: EventHandler<Events, Event>) => void;\n\n  /**\n   * Publish an event with a payload\n   * Triggers all registered handlers for the event\n   *\n   * @param event - The event name to publish\n   * @param payload - The data to pass to event handlers\n   * @returns void\n   */\n  emit: <Event extends keyof Events>(event: Event, payload: Events[Event]) => void;\n\n  /**\n   * Unsubscribe from an event\n   *\n   * @param event - The event name to unsubscribe from\n   * @param handler - Optional specific handler to remove. If omitted, all handlers for the event are removed\n   * @returns void\n   */\n  off: <Event extends keyof Events>(event: Event, handler?: EventHandler<Events, Event>) => void;\n\n  /**\n   * Unsubscribe from a pre-dispatch event\n   *\n   * @param event - The event name to unsubscribe from\n   * @param handler - Optional specific handler to remove. If omitted, all pre-dispatch handlers for the event are removed\n   * @returns void\n   */\n  prioritizedOff: <Event extends keyof Events>(event: Event, handler?: EventHandler<Events, Event>) => void;\n\n  /**\n   * Internal utilities for the event bus\n   */\n  internal: {\n    /**\n     * Retrieve all listeners for a specific event\n     *\n     * @param event - The event name to get listeners for\n     * @returns Array of handler functions\n     */\n    retrieveListeners: <Event extends keyof Events>(event: Event) => Array<(...args: any[]) => void>;\n  };\n};\n\n/**\n * @internal\n */\ntype InternalOn = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  latestPayloadMap: Map<keyof Events, any>,\n  event: Event,\n  handler: EventHandler<Events, Event>,\n  opts?: { notify?: boolean },\n) => void;\n\n/**\n * @internal\n */\ntype InternalOff = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  event: Event,\n  handler?: EventHandler<Events, Event>,\n) => void;\n\n/**\n * @internal\n */\ntype InternalDispatch = <Events extends Record<string, unknown>, Event extends keyof Events>(\n  eventToHandlersMap: Map<keyof Events, Array<(...args: any[]) => void>>,\n  event: Event,\n  payload: Events[Event],\n) => void;\n\n/**\n * @internal\n */\nconst _on: InternalOn = (eventToHandlersMap, latestPayloadMap, event, handler, opts) => {\n  const { notify } = opts || {};\n  let handlers = eventToHandlersMap.get(event);\n\n  if (!handlers) {\n    handlers = [];\n    eventToHandlersMap.set(event, handlers);\n  }\n\n  handlers.push(handler);\n\n  if (notify && latestPayloadMap.has(event)) {\n    handler(latestPayloadMap.get(event));\n  }\n};\n\n/**\n * @internal\n */\nconst _dispatch: InternalDispatch = (eventToHandlersMap, event, payload) =>\n  (eventToHandlersMap.get(event) || []).map(h => h(payload));\n\n/**\n * @internal\n */\nconst _off: InternalOff = (eventToHandlersMap, event, handler) => {\n  const handlers = eventToHandlersMap.get(event);\n  if (handlers) {\n    if (handler) {\n      handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n    } else {\n      eventToHandlersMap.set(event, []);\n    }\n  }\n};\n\n/**\n * A ES6/2015 compatible 300 byte event bus\n *\n * Creates a strongly-typed event bus that enables publish/subscribe communication between components.\n *\n * @template Events - A record type that maps event names to their payload types\n * @returns An EventBus instance with the following methods:\n * - `on`: Subscribe to an event\n * - `onPreDispatch`: Subscribe to an event, triggered before regular subscribers\n * - `emit`: Publish an event with payload\n * - `off`: Unsubscribe from an event\n * - `offPreDispatch`: Unsubscribe from a pre-dispatch event\n *\n * @example\n * // Define event types\n * const eventBus = createEventBus<{\n *   'user-login': { userId: string; timestamp: number };\n *   'data-updated': { records: any[] };\n *   'error': Error;\n * }>();\n *\n * // Subscribe to events\n * eventBus.on('user-login', ({ userId, timestamp }) => {\n *   console.log(`User ${userId} logged in at ${timestamp}`);\n * });\n *\n * // Subscribe with immediate notification if event was already dispatched\n * eventBus.on('user-login', (payload) => {\n *   // This will be called immediately if 'user-login' was previously dispatched\n * }, { notify: true });\n *\n * // Publish an event\n * eventBus.emit('user-login', { userId: 'abc123', timestamp: Date.now() });\n *\n * // Unsubscribe from event\n * const handler = (payload) => console.log(payload);\n * eventBus.on('error', handler);\n * // Later...\n * eventBus.off('error', handler);\n *\n * // Unsubscribe all handlers for an event\n * eventBus.off('data-updated');\n */\nexport const createEventBus = <Events extends Record<string, unknown>>(): EventBus<Events> => {\n  const eventToHandlersMap = new Map<keyof Events, Array<(...args: any[]) => void>>();\n  const latestPayloadMap = new Map<keyof Events, any>();\n  const eventToPredispatchHandlersMap = new Map<keyof Events, Array<(...args: any[]) => void>>();\n\n  const emit: EventBus<Events>['emit'] = (event, payload) => {\n    latestPayloadMap.set(event, payload);\n    _dispatch(eventToPredispatchHandlersMap, event, payload);\n    _dispatch(eventToHandlersMap, event, payload);\n  };\n\n  return {\n    // Subscribe to an event\n    on: (...args) => _on(eventToHandlersMap, latestPayloadMap, ...args),\n    // Subscribe to an event with priority\n    // Registered handlers with `prioritizedOn` will be called before handlers registered with `on`\n    prioritizedOn: (...args) => _on(eventToPredispatchHandlersMap, latestPayloadMap, ...args),\n    // Dispatch an event\n    emit,\n    // Unsubscribe from an event\n    off: (...args) => _off(eventToHandlersMap, ...args),\n    // Unsubscribe from an event with priority\n    // Unsubscribes handlers only registered with `prioritizedOn`\n    prioritizedOff: (...args) => _off(eventToPredispatchHandlersMap, ...args),\n\n    // Internal utilities\n    internal: {\n      retrieveListeners: event => eventToHandlersMap.get(event) || [],\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AA6GA,IAAM,MAAkB,CAAC,oBAAoB,kBAAkB,OAAO,SAAS,SAAS;IACtF,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI,QAAQ,CAAC;IAC5B,IAAI,WAAW,mBAAmB,GAAA,CAAI,KAAK;IAE3C,IAAI,CAAC,UAAU;QACb,WAAW,CAAC,CAAA;QACZ,mBAAmB,GAAA,CAAI,OAAO,QAAQ;IACxC;IAEA,SAAS,IAAA,CAAK,OAAO;IAErB,IAAI,UAAU,iBAAiB,GAAA,CAAI,KAAK,GAAG;QACzC,QAAQ,iBAAiB,GAAA,CAAI,KAAK,CAAC;IACrC;AACF;AAKA,IAAM,YAA8B,CAAC,oBAAoB,OAAO,UAAA,CAC7D,mBAAmB,GAAA,CAAI,KAAK,KAAK,CAAC,CAAA,EAAG,GAAA,CAAI,CAAA,IAAK,EAAE,OAAO,CAAC;AAK3D,IAAM,OAAoB,CAAC,oBAAoB,OAAO,YAAY;IAChE,MAAM,WAAW,mBAAmB,GAAA,CAAI,KAAK;IAC7C,IAAI,UAAU;QACZ,IAAI,SAAS;YACX,SAAS,MAAA,CAAO,SAAS,OAAA,CAAQ,OAAO,MAAM,GAAG,CAAC;QACpD,OAAO;YACL,mBAAmB,GAAA,CAAI,OAAO,CAAC,CAAC;QAClC;IACF;AACF;AA6CO,IAAM,iBAAiB,MAAgE;IAC5F,MAAM,qBAAqB,aAAA,GAAA,IAAI,IAAmD;IAClF,MAAM,mBAAmB,aAAA,GAAA,IAAI,IAAuB;IACpD,MAAM,gCAAgC,aAAA,GAAA,IAAI,IAAmD;IAE7F,MAAM,OAAiC,CAAC,OAAO,YAAY;QACzD,iBAAiB,GAAA,CAAI,OAAO,OAAO;QACnC,UAAU,+BAA+B,OAAO,OAAO;QACvD,UAAU,oBAAoB,OAAO,OAAO;IAC9C;IAEA,OAAO;QAAA,wBAAA;QAEL,IAAI,CAAA,GAAI,OAAS,IAAI,oBAAoB,kBAAkB,GAAG,IAAI;QAAA,sCAAA;QAAA,+FAAA;QAGlE,eAAe,CAAA,GAAI,OAAS,IAAI,+BAA+B,kBAAkB,GAAG,IAAI;QAAA,oBAAA;QAExF;QAAA,4BAAA;QAEA,KAAK,CAAA,GAAI,OAAS,KAAK,oBAAoB,GAAG,IAAI;QAAA,0CAAA;QAAA,6DAAA;QAGlD,gBAAgB,CAAA,GAAI,OAAS,KAAK,+BAA+B,GAAG,IAAI;QAAA,qBAAA;QAGxE,UAAU;YACR,mBAAmB,CAAA,QAAS,mBAAmB,GAAA,CAAI,KAAK,KAAK,CAAC,CAAA;QAChE;IACF;AACF", "debugId": null}}, {"offset": {"line": 4121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/clerkEventBus.ts"], "sourcesContent": ["import type { ClerkEventPayload } from '@clerk/types';\n\nimport { createEventBus } from './eventBus';\n\nexport const clerkEvents = {\n  Status: 'status',\n} satisfies Record<string, keyof ClerkEventPayload>;\n\nexport const createClerkEventBus = () => {\n  return createEventBus<ClerkEventPayload>();\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIO,IAAM,cAAc;IACzB,QAAQ;AACV;AAEO,IAAM,sBAAsB,MAAM;IACvC,WAAO,mSAAA,CAAkC;AAC3C", "debugId": null}}, {"offset": {"line": 4144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/logger.ts"], "sourcesContent": ["const loggedMessages: Set<string> = new Set();\n\nexport const logger = {\n  /**\n   * A custom logger that ensures messages are logged only once.\n   * Reduces noise and duplicated messages when logs are in a hot codepath.\n   */\n  warnOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    loggedMessages.add(msg);\n    console.warn(msg);\n  },\n  logOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    console.log(msg);\n    loggedMessages.add(msg);\n  },\n};\n"], "names": [], "mappings": ";;;;;AAAA,IAAM,iBAA8B,aAAA,GAAA,IAAI,IAAI;AAErC,IAAM,SAAS;IAAA;;;GAAA,GAKpB,UAAU,CAAC,QAAgB;QACzB,IAAI,eAAe,GAAA,CAAI,GAAG,GAAG;YAC3B;QACF;QAEA,eAAe,GAAA,CAAI,GAAG;QACtB,QAAQ,IAAA,CAAK,GAAG;IAClB;IACA,SAAS,CAAC,QAAgB;QACxB,IAAI,eAAe,GAAA,CAAI,GAAG,GAAG;YAC3B;QACF;QAEA,QAAQ,GAAA,CAAI,GAAG;QACf,eAAe,GAAA,CAAI,GAAG;IACxB;AACF", "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/node_modules/.pnpm/%40clerk%2Bshared%403.22.0_react-dom%4019.1.0_react%4019.1.0/node_modules/%40clerk/shared/src/apiUrlFromPublishableKey.ts"], "sourcesContent": ["import {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES,\n} from './constants';\nimport { parsePublishableKey } from './keys';\n\n/**\n * Get the correct API url based on the publishable key.\n *\n * @param publishableKey - The publishable key to parse.\n * @returns One of Clerk's API URLs.\n */\nexport const apiUrlFromPublishableKey = (publishableKey: string) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n\n  if (frontendApi?.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n\n  if (LOCAL_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n"], "names": [], "mappings": ";;;;;;;;;AAgBO,IAAM,2BAA2B,CAAC,mBAA2B;IAClE,MAAM,kBAAc,wSAAA,EAAoB,cAAc,GAAG;IAEzD,IAAI,aAAa,WAAW,QAAQ,KAAK,iTAAA,CAA6B,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACnH,OAAO,iSAAA;IACT;IAEA,IAAI,uSAAA,CAAmB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACpE,OAAO,kSAAA;IACT;IACA,IAAI,ySAAA,CAAqB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACtE,OAAO,oSAAA;IACT;IACA,OAAO,iSAAA;AACT", "debugId": null}}, {"offset": {"line": 4214, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4233, "column": 0}, "map": {"version": 3, "sources": [], "sourcesContent": [], "names": [], "mappings": "", "debugId": null}}]}