{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/components/footer.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,oaAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/components/footer.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,oaAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/components/header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,oaAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/components/header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,oaAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/lib/env.ts"], "sourcesContent": ["// canonical urls:\nexport const baseUrl =\n  process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';\n\n// env stuff:\nexport const env = process.env.NEXT_PUBLIC_ENV || 'development';\nexport const development = env === 'development';\nexport const staging = env === 'staging';\nexport const production = env === 'production';\n\nexport const canonical = (path: string) => `${baseUrl}${path}`;\nexport const prefixTitle = (title: string) => {\n  return production ? title : `[${env}] ${title}`;\n};\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;;;;;;;;;;;;AACX,MAAM,UACX,6DAAoC;AAG/B,MAAM,MAAM,mDAA+B;AAC3C,MAAM,cAAc,QAAQ;AAC5B,MAAM,UAAU,QAAQ;AACxB,MAAM,aAAa,QAAQ;AAE3B,MAAM,YAAY,CAAC,OAAiB,GAAG,UAAU,MAAM;AACvD,MAAM,cAAc,CAAC;IAC1B,OAAO,sCAAa,0BAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO;AACjD", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/kidaro/app/%28guest%29/layout.tsx"], "sourcesContent": ["import Footer from '@/components/footer';\nimport Header from '@/components/header';\nimport {prefixTitle} from '@/lib/env';\nimport {canonical} from '@/lib/env';\nimport type {Metadata} from 'next';\n\nconst title = prefixTitle('Kidaro');\nconst description =\n  'Your Child’s Favorite Way to Learn — Smarter, Faster, and More Fun with AI.';\n\nexport const metadata: Metadata = {\n  title,\n  description,\n  alternates: {\n    canonical: canonical('/'),\n    languages: {\n      'en-US': '/en-US',\n    },\n  },\n  openGraph: {\n    title,\n    description,\n    type: 'website',\n    url: canonical(''),\n    images: canonical('/img/kidaro-og.jpg'),\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title,\n    description,\n    creator: '@kidaro',\n    images: [canonical('/img/kidaro-og.jpg')], // Must be an absolute URL\n  },\n};\n\nexport default async function GuestLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <div className=\"container flex-1 flex flex-col bg-white max-w-[768px] mx-auto overflow-hidden\">\n      <Header />\n      <main>{children}</main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;;AAIA,MAAM,QAAQ,IAAA,yHAAW,EAAC;AAC1B,MAAM,cACJ;AAEK,MAAM,WAAqB;IAChC;IACA;IACA,YAAY;QACV,WAAW,IAAA,uHAAS,EAAC;QACrB,WAAW;YACT,SAAS;QACX;IACF;IACA,WAAW;QACT;QACA;QACA,MAAM;QACN,KAAK,IAAA,uHAAS,EAAC;QACf,QAAQ,IAAA,uHAAS,EAAC;IACpB;IACA,SAAS;QACP,MAAM;QACN;QACA;QACA,SAAS;QACT,QAAQ;YAAC,IAAA,uHAAS,EAAC;SAAsB;IAC3C;AACF;AAEe,eAAe,YAAY,EACxC,QAAQ,EAGR;IACA,qBACE,0YAAC;QAAI,WAAU;;0BACb,0YAAC,gIAAM;;;;;0BACP,0YAAC;0BAAM;;;;;;0BACP,0YAAC,gIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}