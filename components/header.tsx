'use client';
import Logo from '@/assets/svg/kidaro-logo-full-color.svg';
import {Sling as Hamburger} from 'hamburger-react';
import {useEffect, useState} from 'react';
import Link from 'next/link';
import {cn} from '@/lib/utils';
// import {
//   Drawer,
//   // DrawerClose,
//   DrawerContent,
//   DrawerFooter,
//   // DrawerHeader,
//   DrawerTitle,
//   DrawerTrigger,
// } from './ui/drawer';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import {SignedIn, SignOutButton, useUser} from '@clerk/nextjs';

import Spinner from '@/assets/svg/spinner.svg';
import {InfoIcon} from 'lucide-react';
import {usePathname} from 'next/navigation';

type NavItem = {
  title: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
};

export default function Header({className = ''}: {className?: string}) {
  const {user} = useUser();
  // eslint-disable-next-line
  const pathname = usePathname();

  const nav = [
    {title: 'Recipes', icon: Spinner, href: '/recipes'},
    {title: 'Bookmarks', icon: Spinner, href: '/bookmarks'},
    {title: 'Challenges', icon: Spinner, href: '/challenges'},
    {title: 'Rewards', icon: Spinner, href: '/rewards'},
    {title: 'My Info', icon: InfoIcon, href: '/onboarding'},
    ...(user
      ? [
          {
            title: 'Account',
            icon: () => {
              return (
                /* eslint-disable-next-line @next/next/no-img-element */
                <img
                  src={user.imageUrl}
                  alt="avatar"
                  className="w-5 h-5 rounded-full object-cover"
                />
              );
            },

            href: '/account',
          },
        ]
      : []),
  ].map((i) => i as NavItem);

  const [isOpen, setOpen] = useState(false);

  // close on scroll
  useEffect(() => {
    const handleScroll = () => isOpen && setOpen(false);
    window.addEventListener('scroll', handleScroll, {passive: true});
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isOpen]);

  return (
    <header className={cn(className)}>
      <div className="container mx-auto flex items-center justify-between md:justify-between px-4 relative z-50 transition-all ease-out duration-500">
        <Link href="/recipes" className="hover:scale-[103%] duration-300">
          <Logo className={cn('w-[140px] h-auto')} />
        </Link>

        <div className="flex items-center gap-2">
          <Popover open={isOpen} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Hamburger
                toggled={isOpen}
                direction="left"
                distance="sm"
                size={24}
                rounded
              />
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div
                className={cn(
                  'nav font-arimo font-bold relative flex flex-col text-lg mb-10'
                )}
              >
                {nav.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex gap-2 px-4 py-1 items-center justify-start',
                      'hover:underline hover:bg-stone-200 cursor-pointer!'
                    )}
                    onClick={() => setOpen(false)}
                  >
                    {item.icon && <item.icon className={cn('w-5 h-5')} />}
                    {item.title}
                  </Link>
                ))}
                <SignedIn>
                  <SignOutButton>
                    <a
                      href="#logout"
                      className={cn(
                        'flex gap-2 px-4 py-1 items-center justify-start',
                        'hover:underline hover:bg-stone-200 cursor-pointer!'
                      )}
                      onClick={() => setOpen(false)}
                      style={{cursor: 'pointer'}}
                    >
                      <Spinner className="w-5 h-5 translate-x-[2px]" />
                      Logout
                    </a>
                  </SignOutButton>
                </SignedIn>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}
