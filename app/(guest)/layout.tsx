import Footer from '@/components/footer';
import Header from '@/components/header';
import {canonical} from '@/lib/env';
import type {Metadata} from 'next';

const title = 'JuiceMadam App';
const description =
  'Juicemadam recipes, juice challenges, discounts and promotions.';

export const metadata: Metadata = {
  title,
  description,
  alternates: {
    ...canonical('/'),
    languages: {
      'en-US': '/en-US',
    },
  },
  openGraph: {
    title,
    description,
    type: 'website',
    url: 'https://recipes.juicemadam.com',
    images: 'https://recipes.juicemadam.com/img/juicemadam-og.jpg',
  },
  twitter: {
    card: 'summary_large_image',
    title,
    description,
    creator: '@slapcitygame',
    images: ['https://recipes.juicemadam.com/img/juicemadam-og.jpg'], // Must be an absolute URL
  },
};

export default async function GuestLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="container flex-1 flex flex-col bg-white max-w-[768px] mx-auto overflow-hidden">
      <Header />
      <main>{children}</main>
      <Footer />
    </div>
  );
}
