@use 'sass:color';

// SCSS Variables for custom styles
$primary-color: #3b82f6;
$secondary-color: #64748b;
$success-color: #10b981;
$error-color: #ef4444;
$warning-color: #f59e0b;

// Mixins for common patterns
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin button-base {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;

  &:hover {
    transform: translateY(-1px);
  }
}

// Custom component styles
.test {
  background: $error-color;
  height: 100px;
  width: 100px;
  @include flex-center;
  border-radius: 0.5rem;

  &:hover {
    background: color.adjust($error-color, $lightness: -10%);
    transform: scale(1.05);
  }
}
