{"version": 3, "sources": ["../../../../src/app-router/client/useAwaitableReplace.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.replace` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitableReplace = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.replaceState.bind(window.history) : undefined,\n    routerNav: router.replace.bind(router),\n    name: 'replace',\n  });\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,wBAA0B;AAE1B,+BAAkC;AAO3B,MAAM,sBAAsB,MAAM;AACvC,QAAM,aAAS,6BAAU;AAEzB,aAAO,4CAAkB;AAAA,IACvB,WAAW,OAAO,WAAW,cAAc,OAAO,QAAQ,aAAa,KAAK,OAAO,OAAO,IAAI;AAAA,IAC9F,WAAW,OAAO,QAAQ,KAAK,MAAM;AAAA,IACrC,MAAM;AAAA,EACR,CAAC;AACH;", "names": []}