{"version": 3, "sources": ["../../../../src/app-router/server/ClerkProvider.tsx"], "sourcesContent": ["import type { InitialState, Without } from '@clerk/types';\nimport { headers } from 'next/headers';\nimport type { ReactNode } from 'react';\nimport React from 'react';\n\nimport { PromisifiedAuthProvider } from '../../client-boundary/PromisifiedAuthProvider';\nimport { getDynamicAuthData } from '../../server/buildClerkProps';\nimport type { NextClerkProviderProps } from '../../types';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { isNext13 } from '../../utils/sdk-versions';\nimport { ClientClerkProvider } from '../client/ClerkProvider';\nimport { getKeylessStatus, KeylessProvider } from './keyless-provider';\nimport { buildRequestLike, getScriptNonceFromHeader } from './utils';\n\nconst getDynamicClerkState = React.cache(async function getDynamicClerkState() {\n  const request = await buildRequestLike();\n  const data = getDynamicAuthData(request);\n\n  return data;\n});\n\nconst getNonceHeaders = React.cache(async function getNonceHeaders() {\n  const headersList = await headers();\n  const nonce = headersList.get('X-Nonce');\n  return nonce\n    ? nonce\n    : // Fallback to extracting from CSP header\n      getScriptNonceFromHeader(headersList.get('Content-Security-Policy') || '') || '';\n});\n\nexport async function ClerkProvider(\n  props: Without<NextClerkProviderProps, '__unstable_invokeMiddlewareOnAuthStateChange'>,\n) {\n  const { children, dynamic, ...rest } = props;\n\n  async function generateStatePromise() {\n    if (!dynamic) {\n      return Promise.resolve(null);\n    }\n    if (isNext13) {\n      /**\n       * For some reason, Next 13 requires that functions which call `headers()` are awaited where they are invoked.\n       * Without the await here, Next will throw a DynamicServerError during build.\n       */\n      return Promise.resolve(await getDynamicClerkState());\n    }\n    return getDynamicClerkState();\n  }\n\n  async function generateNonce() {\n    if (!dynamic) {\n      return Promise.resolve('');\n    }\n    if (isNext13) {\n      /**\n       * For some reason, Next 13 requires that functions which call `headers()` are awaited where they are invoked.\n       * Without the await here, Next will throw a DynamicServerError during build.\n       */\n      return Promise.resolve(await getNonceHeaders());\n    }\n    return getNonceHeaders();\n  }\n\n  const propsWithEnvs = mergeNextClerkPropsWithEnv({\n    ...rest,\n  });\n\n  const { shouldRunAsKeyless, runningWithClaimedKeys } = await getKeylessStatus(propsWithEnvs);\n\n  let output: ReactNode;\n\n  try {\n    const detectKeylessEnvDrift = await import('../../server/keyless-telemetry.js').then(\n      mod => mod.detectKeylessEnvDrift,\n    );\n    await detectKeylessEnvDrift();\n  } catch {\n    // ignore\n  }\n\n  if (shouldRunAsKeyless) {\n    output = (\n      <KeylessProvider\n        rest={propsWithEnvs}\n        generateNonce={generateNonce}\n        generateStatePromise={generateStatePromise}\n        runningWithClaimedKeys={runningWithClaimedKeys}\n      >\n        {children}\n      </KeylessProvider>\n    );\n  } else {\n    output = (\n      <ClientClerkProvider\n        {...propsWithEnvs}\n        nonce={await generateNonce()}\n        initialState={await generateStatePromise()}\n      >\n        {children}\n      </ClientClerkProvider>\n    );\n  }\n\n  if (dynamic) {\n    return (\n      // TODO: fix types so AuthObject is compatible with InitialState\n      <PromisifiedAuthProvider authPromise={generateStatePromise() as unknown as Promise<InitialState>}>\n        {output}\n      </PromisifiedAuthProvider>\n    );\n  }\n  return output;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAwB;AAExB,mBAAkB;AAElB,qCAAwC;AACxC,6BAAmC;AAEnC,wCAA2C;AAC3C,0BAAyB;AACzB,2BAAoC;AACpC,8BAAkD;AAClD,mBAA2D;AAE3D,MAAM,uBAAuB,aAAAA,QAAM,MAAM,eAAeC,wBAAuB;AAC7E,QAAM,UAAU,UAAM,+BAAiB;AACvC,QAAM,WAAO,2CAAmB,OAAO;AAEvC,SAAO;AACT,CAAC;AAED,MAAM,kBAAkB,aAAAD,QAAM,MAAM,eAAeE,mBAAkB;AACnE,QAAM,cAAc,UAAM,wBAAQ;AAClC,QAAM,QAAQ,YAAY,IAAI,SAAS;AACvC,SAAO,QACH;AAAA;AAAA,QAEA,uCAAyB,YAAY,IAAI,yBAAyB,KAAK,EAAE,KAAK;AAAA;AACpF,CAAC;AAED,eAAsB,cACpB,OACA;AACA,QAAM,EAAE,UAAU,SAAS,GAAG,KAAK,IAAI;AAEvC,iBAAe,uBAAuB;AACpC,QAAI,CAAC,SAAS;AACZ,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,QAAI,8BAAU;AAKZ,aAAO,QAAQ,QAAQ,MAAM,qBAAqB,CAAC;AAAA,IACrD;AACA,WAAO,qBAAqB;AAAA,EAC9B;AAEA,iBAAe,gBAAgB;AAC7B,QAAI,CAAC,SAAS;AACZ,aAAO,QAAQ,QAAQ,EAAE;AAAA,IAC3B;AACA,QAAI,8BAAU;AAKZ,aAAO,QAAQ,QAAQ,MAAM,gBAAgB,CAAC;AAAA,IAChD;AACA,WAAO,gBAAgB;AAAA,EACzB;AAEA,QAAM,oBAAgB,8DAA2B;AAAA,IAC/C,GAAG;AAAA,EACL,CAAC;AAED,QAAM,EAAE,oBAAoB,uBAAuB,IAAI,UAAM,0CAAiB,aAAa;AAE3F,MAAI;AAEJ,MAAI;AACF,UAAM,wBAAwB,MAAM,OAAO,mCAAmC,EAAE;AAAA,MAC9E,SAAO,IAAI;AAAA,IACb;AACA,UAAM,sBAAsB;AAAA,EAC9B,QAAQ;AAAA,EAER;AAEA,MAAI,oBAAoB;AACtB,aACE,6BAAAF,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA;AAAA,MAEC;AAAA,IACH;AAAA,EAEJ,OAAO;AACL,aACE,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,OAAO,MAAM,cAAc;AAAA,QAC3B,cAAc,MAAM,qBAAqB;AAAA;AAAA,MAExC;AAAA,IACH;AAAA,EAEJ;AAEA,MAAI,SAAS;AACX;AAAA;AAAA,MAEE,6BAAAA,QAAA,cAAC,0DAAwB,aAAa,qBAAqB,KACxD,MACH;AAAA;AAAA,EAEJ;AACA,SAAO;AACT;", "names": ["React", "getDynamicClerkState", "getNonceHeaders"]}