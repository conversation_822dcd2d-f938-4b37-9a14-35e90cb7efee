{"version": 3, "sources": ["../../../src/utils/response.ts"], "sourcesContent": ["import { constants as nextConstants } from '../constants';\n\nexport const isRedirect = (res: Response) => {\n  return res.headers.get(nextConstants.Headers.NextRedirect);\n};\n\nexport const setHeader = <T extends Response>(res: T, name: string, val: string): T => {\n  res.headers.set(name, val);\n  return res;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAA2C;AAEpC,MAAM,aAAa,CAAC,QAAkB;AAC3C,SAAO,IAAI,QAAQ,IAAI,iBAAAA,UAAc,QAAQ,YAAY;AAC3D;AAEO,MAAM,YAAY,CAAqB,KAAQ,MAAc,QAAmB;AACrF,MAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,SAAO;AACT;", "names": ["nextConstants"]}