{"version": 3, "sources": ["../../src/constants.ts"], "sourcesContent": ["const Headers = {\n  NextRewrite: 'x-middleware-rewrite',\n  NextResume: 'x-middleware-next',\n  NextRedirect: 'Location',\n  // Used by next to identify internal navigation for app router\n  NextUrl: 'next-url',\n  NextAction: 'next-action',\n  // Used by next to identify internal navigation for pages router\n  NextjsData: 'x-nextjs-data',\n} as const;\n\nexport const constants = {\n  Headers,\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAM,UAAU;AAAA,EACd,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA,EACT,YAAY;AAAA;AAAA,EAEZ,YAAY;AACd;AAEO,MAAM,YAAY;AAAA,EACvB;AACF;", "names": []}