{"version": 3, "sources": ["../../../../src/server/data/getAuthDataFromRequest.ts"], "sourcesContent": ["import type { AuthObject, MachineAuthObject } from '@clerk/backend';\nimport type {\n  AuthenticateRequestOptions,\n  MachineTokenType,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n} from '@clerk/backend/internal';\nimport {\n  AuthStatus,\n  constants,\n  getAuthObjectForAcceptedToken,\n  getAuthObjectFromJwt,\n  invalidTokenAuthObject,\n  isMachineTokenByPrefix,\n  isTokenTypeAccepted,\n  signedOutAuthObject,\n  TokenType,\n} from '@clerk/backend/internal';\nimport { decodeJwt } from '@clerk/backend/jwt';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport type { LoggerNoCommit } from '../../utils/debugLogger';\nimport { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from '../constants';\nimport { getAuthKeyFromRequest, getHeader } from '../headers-utils';\nimport type { RequestLike } from '../types';\nimport { assertTokenSignature, decryptClerkRequestData } from '../utils';\n\nexport type GetAuthDataFromRequestOptions = {\n  secretKey?: string;\n  logger?: LoggerNoCommit;\n  acceptsToken?: AuthenticateRequestOptions['acceptsToken'];\n} & PendingSessionOptions;\n\n/**\n * Extracts auth headers from the request\n */\nconst getAuthHeaders = (req: RequestLike) => {\n  return {\n    authStatus: getAuthKeyFromRequest(req, 'AuthStatus'),\n    authToken: getAuthKeyFromRequest(req, 'AuthToken'),\n    authMessage: getAuthKeyFromRequest(req, 'AuthMessage'),\n    authReason: getAuthKeyFromRequest(req, 'AuthReason'),\n    authSignature: getAuthKeyFromRequest(req, 'AuthSignature'),\n  };\n};\n\n/**\n * Creates auth options object with fallbacks from encrypted request data\n */\nconst createAuthOptions = (req: RequestLike, opts: GetAuthDataFromRequestOptions, treatPendingAsSignedOut = true) => {\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  return {\n    secretKey: opts?.secretKey || decryptedRequestData.secretKey || SECRET_KEY,\n    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    apiVersion: API_VERSION,\n    authStatus: getAuthKeyFromRequest(req, 'AuthStatus'),\n    authMessage: getAuthKeyFromRequest(req, 'AuthMessage'),\n    authReason: getAuthKeyFromRequest(req, 'AuthReason'),\n    treatPendingAsSignedOut,\n  };\n};\n\n/**\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n */\nexport const getSessionAuthDataFromRequest = (\n  req: RequestLike,\n  { treatPendingAsSignedOut = true, ...opts }: GetAuthDataFromRequestOptions = {},\n): SignedInAuthObject | SignedOutAuthObject => {\n  const { authStatus, authMessage, authReason, authToken, authSignature } = getAuthHeaders(req);\n\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const options = createAuthOptions(req, opts, treatPendingAsSignedOut);\n\n  // Only accept session tokens in this function.\n  // Machine tokens are not supported and will result in a signed-out state.\n  if (!isTokenTypeAccepted(TokenType.SessionToken, opts.acceptsToken || TokenType.SessionToken)) {\n    return signedOutAuthObject(options);\n  }\n\n  let authObject;\n  if (!authStatus || authStatus !== AuthStatus.SignedIn) {\n    authObject = signedOutAuthObject(options);\n  } else {\n    assertTokenSignature(authToken as string, options.secretKey, authSignature);\n\n    const jwt = decodeJwt(authToken as string);\n\n    opts.logger?.debug('jwt', jwt.raw);\n\n    return getAuthObjectFromJwt(jwt, options);\n  }\n\n  return authObject;\n};\n\n/**\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n *\n * This function handles both session tokens and machine tokens:\n * - Session tokens: Decoded from JWT and validated\n * - Machine tokens: Retrieved from encrypted request data (x-clerk-request-data header)\n */\nexport const getAuthDataFromRequest = (req: RequestLike, opts: GetAuthDataFromRequestOptions = {}): AuthObject => {\n  const { authStatus, authMessage, authReason } = getAuthHeaders(req);\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  const bearerToken = getHeader(req, constants.Headers.Authorization)?.replace('Bearer ', '');\n  const acceptsToken = opts.acceptsToken || TokenType.SessionToken;\n\n  const options = createAuthOptions(req, opts);\n\n  // Handle machine tokens first (from encrypted request data)\n  // Machine tokens are passed via x-clerk-request-data header from middleware\n  const machineAuthObject = handleMachineToken(\n    bearerToken,\n    decryptedRequestData.machineAuthObject,\n    acceptsToken,\n    options,\n  );\n  if (machineAuthObject) {\n    return machineAuthObject;\n  }\n\n  // If a random token is present and acceptsToken is an array that does NOT include session_token,\n  // return invalid token auth object.\n  if (bearerToken && Array.isArray(acceptsToken) && !acceptsToken.includes(TokenType.SessionToken)) {\n    return invalidTokenAuthObject();\n  }\n\n  // Fallback to session logic for all other cases\n  return getSessionAuthDataFromRequest(req, opts);\n};\n\nconst handleMachineToken = (\n  bearerToken: string | undefined,\n  rawAuthObject: AuthObject | undefined,\n  acceptsToken: NonNullable<AuthenticateRequestOptions['acceptsToken']>,\n  options: Record<string, any>,\n): MachineAuthObject<MachineTokenType> | null => {\n  const hasMachineToken = bearerToken && isMachineTokenByPrefix(bearerToken);\n\n  const acceptsOnlySessionToken =\n    acceptsToken === TokenType.SessionToken ||\n    (Array.isArray(acceptsToken) && acceptsToken.length === 1 && acceptsToken[0] === TokenType.SessionToken);\n\n  if (hasMachineToken && rawAuthObject && !acceptsOnlySessionToken) {\n    const authObject = getAuthObjectForAcceptedToken({\n      authObject: {\n        ...rawAuthObject,\n        debug: () => options,\n      },\n      acceptsToken,\n    });\n    return {\n      ...authObject,\n      getToken: () => (authObject.isAuthenticated ? Promise.resolve(bearerToken) : Promise.resolve(null)),\n      has: () => false,\n    } as MachineAuthObject<MachineTokenType>;\n  }\n\n  return null;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAUO;AACP,iBAA0B;AAI1B,uBAAkE;AAClE,2BAAiD;AAEjD,mBAA8D;AAW9D,MAAM,iBAAiB,CAAC,QAAqB;AAC3C,SAAO;AAAA,IACL,gBAAY,4CAAsB,KAAK,YAAY;AAAA,IACnD,eAAW,4CAAsB,KAAK,WAAW;AAAA,IACjD,iBAAa,4CAAsB,KAAK,aAAa;AAAA,IACrD,gBAAY,4CAAsB,KAAK,YAAY;AAAA,IACnD,mBAAe,4CAAsB,KAAK,eAAe;AAAA,EAC3D;AACF;AAKA,MAAM,oBAAoB,CAAC,KAAkB,MAAqC,0BAA0B,SAAS;AACnH,QAAM,2BAAuB,gCAAU,KAAK,0BAAU,QAAQ,gBAAgB;AAC9E,QAAM,2BAAuB,sCAAwB,oBAAoB;AAEzE,SAAO;AAAA,IACL,YAAW,6BAAM,cAAa,qBAAqB,aAAa;AAAA,IAChE,gBAAgB,qBAAqB,kBAAkB;AAAA,IACvD,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,gBAAY,4CAAsB,KAAK,YAAY;AAAA,IACnD,iBAAa,4CAAsB,KAAK,aAAa;AAAA,IACrD,gBAAY,4CAAsB,KAAK,YAAY;AAAA,IACnD;AAAA,EACF;AACF;AAMO,MAAM,gCAAgC,CAC3C,KACA,EAAE,0BAA0B,MAAM,GAAG,KAAK,IAAmC,CAAC,MACjC;AAxE/C;AAyEE,QAAM,EAAE,YAAY,aAAa,YAAY,WAAW,cAAc,IAAI,eAAe,GAAG;AAE5F,aAAK,WAAL,mBAAa,MAAM,WAAW,EAAE,YAAY,aAAa,WAAW;AAEpE,QAAM,UAAU,kBAAkB,KAAK,MAAM,uBAAuB;AAIpE,MAAI,KAAC,qCAAoB,0BAAU,cAAc,KAAK,gBAAgB,0BAAU,YAAY,GAAG;AAC7F,eAAO,qCAAoB,OAAO;AAAA,EACpC;AAEA,MAAI;AACJ,MAAI,CAAC,cAAc,eAAe,2BAAW,UAAU;AACrD,qBAAa,qCAAoB,OAAO;AAAA,EAC1C,OAAO;AACL,2CAAqB,WAAqB,QAAQ,WAAW,aAAa;AAE1E,UAAM,UAAM,sBAAU,SAAmB;AAEzC,eAAK,WAAL,mBAAa,MAAM,OAAO,IAAI;AAE9B,eAAO,sCAAqB,KAAK,OAAO;AAAA,EAC1C;AAEA,SAAO;AACT;AAUO,MAAM,yBAAyB,CAAC,KAAkB,OAAsC,CAAC,MAAkB;AA7GlH;AA8GE,QAAM,EAAE,YAAY,aAAa,WAAW,IAAI,eAAe,GAAG;AAClE,aAAK,WAAL,mBAAa,MAAM,WAAW,EAAE,YAAY,aAAa,WAAW;AAEpE,QAAM,2BAAuB,gCAAU,KAAK,0BAAU,QAAQ,gBAAgB;AAC9E,QAAM,2BAAuB,sCAAwB,oBAAoB;AAEzE,QAAM,eAAc,yCAAU,KAAK,0BAAU,QAAQ,aAAa,MAA9C,mBAAiD,QAAQ,WAAW;AACxF,QAAM,eAAe,KAAK,gBAAgB,0BAAU;AAEpD,QAAM,UAAU,kBAAkB,KAAK,IAAI;AAI3C,QAAM,oBAAoB;AAAA,IACxB;AAAA,IACA,qBAAqB;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AACA,MAAI,mBAAmB;AACrB,WAAO;AAAA,EACT;AAIA,MAAI,eAAe,MAAM,QAAQ,YAAY,KAAK,CAAC,aAAa,SAAS,0BAAU,YAAY,GAAG;AAChG,eAAO,wCAAuB;AAAA,EAChC;AAGA,SAAO,8BAA8B,KAAK,IAAI;AAChD;AAEA,MAAM,qBAAqB,CACzB,aACA,eACA,cACA,YAC+C;AAC/C,QAAM,kBAAkB,mBAAe,wCAAuB,WAAW;AAEzE,QAAM,0BACJ,iBAAiB,0BAAU,gBAC1B,MAAM,QAAQ,YAAY,KAAK,aAAa,WAAW,KAAK,aAAa,CAAC,MAAM,0BAAU;AAE7F,MAAI,mBAAmB,iBAAiB,CAAC,yBAAyB;AAChE,UAAM,iBAAa,+CAA8B;AAAA,MAC/C,YAAY;AAAA,QACV,GAAG;AAAA,QACH,OAAO,MAAM;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,UAAU,MAAO,WAAW,kBAAkB,QAAQ,QAAQ,WAAW,IAAI,QAAQ,QAAQ,IAAI;AAAA,MACjG,KAAK,MAAM;AAAA,IACb;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}