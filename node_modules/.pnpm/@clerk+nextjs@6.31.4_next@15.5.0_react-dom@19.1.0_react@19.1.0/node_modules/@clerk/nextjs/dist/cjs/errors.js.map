{"version": 3, "sources": ["../../src/errors.ts"], "sourcesContent": ["export {\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isReverificationCancelledError,\n  isMetamaskError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n} from './client-boundary/hooks';\n\nexport { isClerkAPIResponseError } from '@clerk/clerk-react/errors';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQO;AAEP,oBAAwC;", "names": []}