{"version": 3, "sources": ["../../../src/server/types.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http';\nimport type { NextApiRequest } from 'next';\nimport type { NextApiRequestCookies } from 'next/dist/server/api-utils';\nimport type { NextMiddleware, NextRequest } from 'next/server';\n\n// Request contained in GetServerSidePropsContext, has cookies but not query\ntype GsspRequest = IncomingMessage & { cookies: NextApiRequestCookies };\n\nexport type RequestLike = NextRequest | NextApiRequest | GsspRequest;\n\nexport type NextMiddlewareRequestParam = Parameters<NextMiddleware>['0'];\nexport type NextMiddlewareEvtParam = Parameters<NextMiddleware>['1'];\nexport type NextMiddlewareReturn = ReturnType<NextMiddleware>;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}