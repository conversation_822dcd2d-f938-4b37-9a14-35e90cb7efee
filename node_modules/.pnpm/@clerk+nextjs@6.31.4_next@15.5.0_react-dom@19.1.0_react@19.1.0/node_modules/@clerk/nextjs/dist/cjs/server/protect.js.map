{"version": 3, "sources": ["../../../src/server/protect.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport type {\n  AuthenticatedMachineObject,\n  AuthenticateRequestOptions,\n  InferAuthObjectFromToken,\n  InferAuthObjectFromTokenArray,\n  RedirectFun,\n  SignedInAuthObject,\n} from '@clerk/backend/internal';\nimport { constants, isTokenTypeAccepted, TokenType } from '@clerk/backend/internal';\nimport type {\n  CheckAuthorizationFromSessionClaims,\n  CheckAuthorizationParamsFromSessionClaims,\n  CheckAuthorizationParamsWithCustomPermissions,\n  CheckAuthorizationWithCustomPermissions,\n  OrganizationCustomPermissionKey,\n} from '@clerk/types';\n\nimport { constants as nextConstants } from '../constants';\nimport { isNextFetcher } from './nextFetcher';\n\ntype AuthProtectOptions = {\n  /**\n   * The token type to check.\n   */\n  token?: AuthenticateRequestOptions['acceptsToken'];\n  /**\n   * The URL to redirect the user to if they are not authorized.\n   */\n  unauthorizedUrl?: string;\n  /**\n   * The URL to redirect the user to if they are not authenticated.\n   */\n  unauthenticatedUrl?: string;\n};\n\n/**\n * Throws a Nextjs notFound error if user is not authenticated or authorized.\n */\nexport interface AuthProtect {\n  /**\n   * @example\n   * auth.protect({ permission: 'org:admin:example1' });\n   * auth.protect({ role: 'admin' });\n   */\n  <P extends OrganizationCustomPermissionKey>(\n    params?: CheckAuthorizationParamsFromSessionClaims<P>,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  /**\n   * @example\n   * auth.protect(has => has({ permission: 'org:admin:example1' }));\n   */\n  (\n    params?: (has: CheckAuthorizationFromSessionClaims) => boolean,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  /**\n   * @example\n   * auth.protect({ token: 'session_token' });\n   */\n  <T extends TokenType>(\n    options?: AuthProtectOptions & { token: T },\n  ): Promise<InferAuthObjectFromToken<T, SignedInAuthObject, AuthenticatedMachineObject>>;\n\n  /**\n   * @example\n   * auth.protect({ token: ['session_token', 'm2m_token'] });\n   */\n  <T extends TokenType[]>(\n    options?: AuthProtectOptions & { token: T },\n  ): Promise<InferAuthObjectFromTokenArray<T, SignedInAuthObject, AuthenticatedMachineObject>>;\n\n  /**\n   * @example\n   * auth.protect({ token: 'any' });\n   */\n  (options?: AuthProtectOptions & { token: 'any' }): Promise<SignedInAuthObject | AuthenticatedMachineObject>;\n\n  /**\n   * @example\n   * auth.protect();\n   */\n  (options?: AuthProtectOptions): Promise<SignedInAuthObject>;\n}\n\nexport function createProtect(opts: {\n  request: Request;\n  authObject: AuthObject;\n  /**\n   * middleware and pages throw a notFound error if signed out\n   * but the middleware needs to throw an error it can catch\n   * use this callback to customise the behavior\n   */\n  notFound: () => never;\n  /**\n   * see {@link notFound} above\n   */\n  redirect: (url: string) => void;\n  /**\n   * For m2m requests, throws a 401 response\n   */\n  unauthorized: () => void;\n  /**\n   * protect() in middleware redirects to signInUrl if signed out\n   * protect() in pages throws a notFound error if signed out\n   * use this callback to customise the behavior\n   */\n  redirectToSignIn: RedirectFun<unknown>;\n}): AuthProtect {\n  const { redirectToSignIn, authObject, redirect, notFound, request, unauthorized } = opts;\n\n  return (async (...args: any[]) => {\n    const paramsOrFunction = getAuthorizationParams(args[0]);\n    const unauthenticatedUrl = (args[0]?.unauthenticatedUrl || args[1]?.unauthenticatedUrl) as string | undefined;\n    const unauthorizedUrl = (args[0]?.unauthorizedUrl || args[1]?.unauthorizedUrl) as string | undefined;\n    const requestedToken = args[0]?.token || args[1]?.token || TokenType.SessionToken;\n\n    const handleUnauthenticated = () => {\n      if (unauthenticatedUrl) {\n        return redirect(unauthenticatedUrl);\n      }\n      if (isPageRequest(request)) {\n        // TODO: Handle runtime values. What happens if runtime values are set in middleware and in ClerkProvider as well?\n        return redirectToSignIn();\n      }\n      return notFound();\n    };\n\n    const handleUnauthorized = () => {\n      // For machine tokens, return a 401 response\n      if (authObject.tokenType !== TokenType.SessionToken) {\n        return unauthorized();\n      }\n\n      if (unauthorizedUrl) {\n        return redirect(unauthorizedUrl);\n      }\n      return notFound();\n    };\n\n    if (!isTokenTypeAccepted(authObject.tokenType, requestedToken)) {\n      return handleUnauthorized();\n    }\n\n    if (authObject.tokenType !== TokenType.SessionToken) {\n      // For machine tokens, we only check if they're authenticated\n      // They don't have session status or organization permissions\n      if (!authObject.isAuthenticated) {\n        return handleUnauthorized();\n      }\n      return authObject;\n    }\n\n    /**\n     * Redirects the user back to the tasks URL if their session status is pending\n     */\n    if (authObject.sessionStatus === 'pending') {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is not authenticated\n     */\n    if (!authObject.userId) {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is authenticated\n     */\n    if (!paramsOrFunction) {\n      return authObject;\n    }\n\n    /**\n     * if a function is passed and returns false then throw not found\n     */\n    if (typeof paramsOrFunction === 'function') {\n      if (paramsOrFunction(authObject.has)) {\n        return authObject;\n      }\n      return handleUnauthorized();\n    }\n\n    /**\n     * Checking if user is authorized when permission or role is passed\n     */\n    if (authObject.has(paramsOrFunction)) {\n      return authObject;\n    }\n\n    return handleUnauthorized();\n  }) as AuthProtect;\n}\n\nconst getAuthorizationParams = (arg: any) => {\n  if (!arg) {\n    return undefined;\n  }\n\n  // Skip authorization check if the arg contains any of these options\n  if (arg.unauthenticatedUrl || arg.unauthorizedUrl || arg.token) {\n    return undefined;\n  }\n\n  // Skip if it's just a token-only object\n  if (Object.keys(arg).length === 1 && 'token' in arg) {\n    return undefined;\n  }\n\n  // Return the authorization params/function\n  return arg as\n    | CheckAuthorizationParamsWithCustomPermissions\n    | ((has: CheckAuthorizationWithCustomPermissions) => boolean);\n};\n\nconst isServerActionRequest = (req: Request) => {\n  return (\n    !!req.headers.get(nextConstants.Headers.NextUrl) &&\n    (req.headers.get(constants.Headers.Accept)?.includes('text/x-component') ||\n      req.headers.get(constants.Headers.ContentType)?.includes('multipart/form-data') ||\n      !!req.headers.get(nextConstants.Headers.NextAction))\n  );\n};\n\nconst isPageRequest = (req: Request): boolean => {\n  return (\n    req.headers.get(constants.Headers.SecFetchDest) === 'document' ||\n    req.headers.get(constants.Headers.SecFetchDest) === 'iframe' ||\n    req.headers.get(constants.Headers.Accept)?.includes('text/html') ||\n    isAppRouterInternalNavigation(req) ||\n    isPagesRouterInternalNavigation(req)\n  );\n};\n\nconst isAppRouterInternalNavigation = (req: Request) =>\n  (!!req.headers.get(nextConstants.Headers.NextUrl) && !isServerActionRequest(req)) || isPagePathAvailable();\n\nconst isPagePathAvailable = () => {\n  const __fetch = globalThis.fetch;\n\n  if (!isNextFetcher(__fetch)) {\n    return false;\n  }\n\n  const { page, pagePath } = __fetch.__nextGetStaticStore().getStore() || {};\n\n  return Boolean(\n    // available on next@14\n    pagePath ||\n      // available on next@15\n      page,\n  );\n};\n\nconst isPagesRouterInternalNavigation = (req: Request) => !!req.headers.get(nextConstants.Headers.NextjsData);\n\n// /**\n//  * In case we want to handle router handlers and server actions differently in the future\n//  */\n// const isApiRouteRequest = (req: Request) => {\n//   return !isPageRequest(req) && !isServerActionRequest(req);\n// };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,sBAA0D;AAS1D,uBAA2C;AAC3C,yBAA8B;AAqEvB,SAAS,cAAc,MAuBd;AACd,QAAM,EAAE,kBAAkB,YAAY,UAAU,UAAU,SAAS,aAAa,IAAI;AAEpF,SAAQ,UAAU,SAAgB;AAlHpC;AAmHI,UAAM,mBAAmB,uBAAuB,KAAK,CAAC,CAAC;AACvD,UAAM,uBAAsB,UAAK,CAAC,MAAN,mBAAS,yBAAsB,UAAK,CAAC,MAAN,mBAAS;AACpE,UAAM,oBAAmB,UAAK,CAAC,MAAN,mBAAS,sBAAmB,UAAK,CAAC,MAAN,mBAAS;AAC9D,UAAM,mBAAiB,UAAK,CAAC,MAAN,mBAAS,YAAS,UAAK,CAAC,MAAN,mBAAS,UAAS,0BAAU;AAErE,UAAM,wBAAwB,MAAM;AAClC,UAAI,oBAAoB;AACtB,eAAO,SAAS,kBAAkB;AAAA,MACpC;AACA,UAAI,cAAc,OAAO,GAAG;AAE1B,eAAO,iBAAiB;AAAA,MAC1B;AACA,aAAO,SAAS;AAAA,IAClB;AAEA,UAAM,qBAAqB,MAAM;AAE/B,UAAI,WAAW,cAAc,0BAAU,cAAc;AACnD,eAAO,aAAa;AAAA,MACtB;AAEA,UAAI,iBAAiB;AACnB,eAAO,SAAS,eAAe;AAAA,MACjC;AACA,aAAO,SAAS;AAAA,IAClB;AAEA,QAAI,KAAC,qCAAoB,WAAW,WAAW,cAAc,GAAG;AAC9D,aAAO,mBAAmB;AAAA,IAC5B;AAEA,QAAI,WAAW,cAAc,0BAAU,cAAc;AAGnD,UAAI,CAAC,WAAW,iBAAiB;AAC/B,eAAO,mBAAmB;AAAA,MAC5B;AACA,aAAO;AAAA,IACT;AAKA,QAAI,WAAW,kBAAkB,WAAW;AAC1C,aAAO,sBAAsB;AAAA,IAC/B;AAKA,QAAI,CAAC,WAAW,QAAQ;AACtB,aAAO,sBAAsB;AAAA,IAC/B;AAKA,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AAKA,QAAI,OAAO,qBAAqB,YAAY;AAC1C,UAAI,iBAAiB,WAAW,GAAG,GAAG;AACpC,eAAO;AAAA,MACT;AACA,aAAO,mBAAmB;AAAA,IAC5B;AAKA,QAAI,WAAW,IAAI,gBAAgB,GAAG;AACpC,aAAO;AAAA,IACT;AAEA,WAAO,mBAAmB;AAAA,EAC5B;AACF;AAEA,MAAM,yBAAyB,CAAC,QAAa;AAC3C,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAGA,MAAI,IAAI,sBAAsB,IAAI,mBAAmB,IAAI,OAAO;AAC9D,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,KAAK,GAAG,EAAE,WAAW,KAAK,WAAW,KAAK;AACnD,WAAO;AAAA,EACT;AAGA,SAAO;AAGT;AAEA,MAAM,wBAAwB,CAAC,QAAiB;AA3NhD;AA4NE,SACE,CAAC,CAAC,IAAI,QAAQ,IAAI,iBAAAA,UAAc,QAAQ,OAAO,QAC9C,SAAI,QAAQ,IAAI,0BAAU,QAAQ,MAAM,MAAxC,mBAA2C,SAAS,0BACnD,SAAI,QAAQ,IAAI,0BAAU,QAAQ,WAAW,MAA7C,mBAAgD,SAAS,2BACzD,CAAC,CAAC,IAAI,QAAQ,IAAI,iBAAAA,UAAc,QAAQ,UAAU;AAExD;AAEA,MAAM,gBAAgB,CAAC,QAA0B;AApOjD;AAqOE,SACE,IAAI,QAAQ,IAAI,0BAAU,QAAQ,YAAY,MAAM,cACpD,IAAI,QAAQ,IAAI,0BAAU,QAAQ,YAAY,MAAM,cACpD,SAAI,QAAQ,IAAI,0BAAU,QAAQ,MAAM,MAAxC,mBAA2C,SAAS,iBACpD,8BAA8B,GAAG,KACjC,gCAAgC,GAAG;AAEvC;AAEA,MAAM,gCAAgC,CAAC,QACpC,CAAC,CAAC,IAAI,QAAQ,IAAI,iBAAAA,UAAc,QAAQ,OAAO,KAAK,CAAC,sBAAsB,GAAG,KAAM,oBAAoB;AAE3G,MAAM,sBAAsB,MAAM;AAChC,QAAM,UAAU,WAAW;AAE3B,MAAI,KAAC,kCAAc,OAAO,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,MAAM,SAAS,IAAI,QAAQ,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAEzE,SAAO;AAAA;AAAA,IAEL;AAAA,IAEE;AAAA,EACJ;AACF;AAEA,MAAM,kCAAkC,CAAC,QAAiB,CAAC,CAAC,IAAI,QAAQ,IAAI,iBAAAA,UAAc,QAAQ,UAAU;", "names": ["nextConstants"]}