{"version": 3, "sources": ["../../../src/server/createClerkClient.ts"], "sourcesContent": ["import { createClerkClient } from '@clerk/backend';\n\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  MACHINE_SECRET_KEY,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED,\n} from './constants';\n\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${PACKAGE_NAME}@${PACKAGE_VERSION}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  machineSecretKey: MACHINE_SECRET_KEY,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG,\n  },\n};\n\nexport const createClerkClientWithOptions: typeof createClerkClient = options =>\n  createClerkClient({ ...clerkClientDefaultOptions, ...options });\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAkC;AAElC,uBAYO;AAEP,MAAM,4BAA4B;AAAA,EAChC,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW,GAAG,eAAY,IAAI,QAAe;AAAA,EAC7C,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,WAAW;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AACF;AAEO,MAAM,+BAAyD,iBACpE,kCAAkB,EAAE,GAAG,2BAA2B,GAAG,QAAQ,CAAC;", "names": []}