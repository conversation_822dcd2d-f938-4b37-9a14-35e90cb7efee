{"version": 3, "sources": ["../../../src/server/keyless-log-cache.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\nimport { isDevelopmentEnvironment } from '@clerk/shared/utils';\n// 10 minutes in milliseconds\nconst THROTTLE_DURATION_MS = 10 * 60 * 1000;\n\nfunction createClerkDevCache() {\n  if (!isDevelopmentEnvironment()) {\n    return;\n  }\n\n  if (!global.__clerk_internal_keyless_logger) {\n    global.__clerk_internal_keyless_logger = {\n      __cache: new Map<string, { expiresAt: number; data?: unknown }>(),\n\n      log: function ({ cacheKey, msg }) {\n        if (this.__cache.has(cacheKey) && Date.now() < (this.__cache.get(cacheKey)?.expiresAt || 0)) {\n          return;\n        }\n\n        console.log(msg);\n\n        this.__cache.set(cacheKey, {\n          expiresAt: Date.now() + THROTTLE_DURATION_MS,\n        });\n      },\n      run: async function (\n        callback,\n        { cacheKey, onSuccessStale = THROTTLE_DURATION_MS, onErrorStale = THROTTLE_DURATION_MS },\n      ) {\n        if (this.__cache.has(cacheKey) && Date.now() < (this.__cache.get(cacheKey)?.expiresAt || 0)) {\n          return this.__cache.get(cacheKey)?.data;\n        }\n\n        try {\n          const result = await callback();\n\n          this.__cache.set(cacheKey, {\n            expiresAt: Date.now() + onSuccessStale,\n            data: result,\n          });\n          return result;\n        } catch (e) {\n          this.__cache.set(cacheKey, {\n            expiresAt: Date.now() + onErrorStale,\n          });\n\n          throw e;\n        }\n      },\n    };\n  }\n\n  return globalThis.__clerk_internal_keyless_logger;\n}\n\nexport const createKeylessModeMessage = (keys: AccountlessApplication) => {\n  return `\\n\\x1b[35m\\n[Clerk]:\\x1b[0m You are running in keyless mode.\\nYou can \\x1b[35mclaim your keys\\x1b[0m by visiting ${keys.claimUrl}\\n`;\n};\n\nexport const createConfirmationMessage = () => {\n  return `\\n\\x1b[35m\\n[Clerk]:\\x1b[0m Your application is running with your claimed keys.\\nYou can safely remove the \\x1b[35m.clerk/\\x1b[0m from your project.\\n`;\n};\n\nexport const clerkDevelopmentCache = createClerkDevCache();\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAAyC;AAEzC,MAAM,uBAAuB,KAAK,KAAK;AAEvC,SAAS,sBAAsB;AAC7B,MAAI,KAAC,uCAAyB,GAAG;AAC/B;AAAA,EACF;AAEA,MAAI,CAAC,OAAO,iCAAiC;AAC3C,WAAO,kCAAkC;AAAA,MACvC,SAAS,oBAAI,IAAmD;AAAA,MAEhE,KAAK,SAAU,EAAE,UAAU,IAAI,GAAG;AAdxC;AAeQ,YAAI,KAAK,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,OAAK,UAAK,QAAQ,IAAI,QAAQ,MAAzB,mBAA4B,cAAa,IAAI;AAC3F;AAAA,QACF;AAEA,gBAAQ,IAAI,GAAG;AAEf,aAAK,QAAQ,IAAI,UAAU;AAAA,UACzB,WAAW,KAAK,IAAI,IAAI;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MACA,KAAK,eACH,UACA,EAAE,UAAU,iBAAiB,sBAAsB,eAAe,qBAAqB,GACvF;AA5BR;AA6BQ,YAAI,KAAK,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,OAAK,UAAK,QAAQ,IAAI,QAAQ,MAAzB,mBAA4B,cAAa,IAAI;AAC3F,kBAAO,UAAK,QAAQ,IAAI,QAAQ,MAAzB,mBAA4B;AAAA,QACrC;AAEA,YAAI;AACF,gBAAM,SAAS,MAAM,SAAS;AAE9B,eAAK,QAAQ,IAAI,UAAU;AAAA,YACzB,WAAW,KAAK,IAAI,IAAI;AAAA,YACxB,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT,SAAS,GAAG;AACV,eAAK,QAAQ,IAAI,UAAU;AAAA,YACzB,WAAW,KAAK,IAAI,IAAI;AAAA,UAC1B,CAAC;AAED,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW;AACpB;AAEO,MAAM,2BAA2B,CAAC,SAAiC;AACxE,SAAO;AAAA;AAAA;AAAA,qDAAoH,KAAK,QAAQ;AAAA;AAC1I;AAEO,MAAM,4BAA4B,MAAM;AAC7C,SAAO;AAAA;AAAA;AAAA;AAAA;AACT;AAEO,MAAM,wBAAwB,oBAAoB;", "names": []}