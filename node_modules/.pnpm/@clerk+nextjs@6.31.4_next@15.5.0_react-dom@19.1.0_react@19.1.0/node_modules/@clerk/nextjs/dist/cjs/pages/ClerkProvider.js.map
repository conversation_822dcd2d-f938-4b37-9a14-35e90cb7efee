{"version": 3, "sources": ["../../../src/pages/ClerkProvider.tsx"], "sourcesContent": ["import { Clerk<PERSON>rovider as ReactClerkProvider } from '@clerk/clerk-react';\n// Override Clerk React error thrower to show that errors come from @clerk/nextjs\nimport { setClerkJsLoadingErrorPackageName, setErrorThrowerOptions } from '@clerk/clerk-react/internal';\nimport { useRouter } from 'next/router';\nimport React from 'react';\n\nimport { useSafeLayoutEffect } from '../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider } from '../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../types';\nimport { ClerkJSScript } from '../utils/clerk-js-script';\nimport { invalidateNextRouterCache } from '../utils/invalidateNextRouterCache';\nimport { mergeNextClerkPropsWithEnv } from '../utils/mergeNextClerkPropsWithEnv';\nimport { removeBasePath } from '../utils/removeBasePath';\nimport { RouterTelemetry } from '../utils/router-telemetry';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n\nexport function ClerkProvider({ children, ...props }: NextClerkProviderProps): JSX.Element {\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true } = props;\n  const { push, replace } = useRouter();\n  ReactClerkProvider.displayName = 'ReactClerkProvider';\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = invalidateNextRouterCache;\n  }, []);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onAfterSetActive = () => {\n      // Re-run the middleware every time there auth state changes.\n      // This enables complete control from a centralised place (NextJS middleware),\n      // as we will invoke it every time the client-side auth state changes, eg: signing-out, switching orgs, etc.\\\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        void push(window.location.href);\n      }\n    };\n  }, []);\n\n  const navigate = (to: string) => push(removeBasePath(to));\n  const replaceNavigate = (to: string) => replace(removeBasePath(to));\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    routerPush: navigate,\n    routerReplace: replaceNavigate,\n  });\n  // ClerkProvider automatically injects __clerk_ssr_state\n  // getAuth returns a user-facing authServerSideProps that hides __clerk_ssr_state\n  // @ts-expect-error initialState is hidden from the types as it's a private prop\n  const initialState = props.authServerSideProps?.__clerk_ssr_state || props.__clerk_ssr_state;\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider\n        {...mergedProps}\n        initialState={initialState}\n      >\n        <RouterTelemetry />\n        <ClerkJSScript router='pages' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAoD;AAEpD,sBAA0E;AAC1E,oBAA0B;AAC1B,mBAAkB;AAElB,iCAAoC;AACpC,gCAAyC;AAEzC,6BAA8B;AAC9B,uCAA0C;AAC1C,wCAA2C;AAC3C,4BAA+B;AAC/B,8BAAgC;AAAA,IAEhC,wCAAuB,EAAE,aAAa,gBAAa,CAAC;AAAA,IACpD,mDAAkC,eAAY;AAEvC,SAAS,cAAc,EAAE,UAAU,GAAG,MAAM,GAAwC;AAlB3F;AAmBE,QAAM,EAAE,+CAA+C,KAAK,IAAI;AAChE,QAAM,EAAE,MAAM,QAAQ,QAAI,yBAAU;AACpC,qBAAAA,cAAmB,cAAc;AAEjC,sDAAoB,MAAM;AACxB,WAAO,gCAAgC;AAAA,EACzC,GAAG,CAAC,CAAC;AAEL,sDAAoB,MAAM;AACxB,WAAO,+BAA+B,MAAM;AAI1C,UAAI,8CAA8C;AAChD,aAAK,KAAK,OAAO,SAAS,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,WAAW,CAAC,OAAe,SAAK,sCAAe,EAAE,CAAC;AACxD,QAAM,kBAAkB,CAAC,OAAe,YAAQ,sCAAe,EAAE,CAAC;AAClE,QAAM,kBAAc,8DAA2B;AAAA,IAC7C,GAAG;AAAA,IACH,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB,CAAC;AAID,QAAM,iBAAe,WAAM,wBAAN,mBAA2B,sBAAqB,MAAM;AAE3E,SACE,6BAAAC,QAAA,cAAC,sDAAyB,SAAS,eACjC,6BAAAA,QAAA;AAAA,IAAC,mBAAAD;AAAA,IAAA;AAAA,MACE,GAAG;AAAA,MACJ;AAAA;AAAA,IAEA,6BAAAC,QAAA,cAAC,6CAAgB;AAAA,IACjB,6BAAAA,QAAA,cAAC,wCAAc,QAAO,SAAQ;AAAA,IAC7B;AAAA,EACH,CACF;AAEJ;", "names": ["ReactClerkProvider", "React"]}