"use strict";
"use client";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var ClerkProvider_exports = {};
__export(ClerkProvider_exports, {
  ClientClerkProvider: () => ClientClerkProvider
});
module.exports = __toCommonJS(ClerkProvider_exports);
var import_clerk_react = require("@clerk/clerk-react");
var import_browser = require("@clerk/shared/browser");
var import_logger = require("@clerk/shared/logger");
var import_dynamic = __toESM(require("next/dynamic"));
var import_navigation = require("next/navigation");
var import_package = __toESM(require("next/package.json"));
var import_react = __toESM(require("react"));
var import_useSafeLayoutEffect = require("../../client-boundary/hooks/useSafeLayoutEffect");
var import_NextOptionsContext = require("../../client-boundary/NextOptionsContext");
var import_clerk_js_script = require("../../utils/clerk-js-script");
var import_feature_flags = require("../../utils/feature-flags");
var import_mergeNextClerkPropsWithEnv = require("../../utils/mergeNextClerkPropsWithEnv");
var import_router_telemetry = require("../../utils/router-telemetry");
var import_sdk_versions = require("../../utils/sdk-versions");
var import_keyless_actions = require("../keyless-actions");
var import_server_actions = require("../server-actions");
var import_useAwaitablePush = require("./useAwaitablePush");
var import_useAwaitableReplace = require("./useAwaitableReplace");
const LazyCreateKeylessApplication = (0, import_dynamic.default)(
  () => import("./keyless-creator-reader.js").then((m) => m.KeylessCreatorOrReader)
);
const NextClientClerkProvider = (props) => {
  if (import_sdk_versions.isNextWithUnstableServerActions) {
    const deprecationWarning = `Clerk:
Your current Next.js version (${import_package.default.version}) will be deprecated in the next major release of "@clerk/nextjs". Please upgrade to next@14.1.0 or later.`;
    if ((0, import_browser.inBrowser)()) {
      import_logger.logger.warnOnce(deprecationWarning);
    } else {
      import_logger.logger.logOnce(`
\x1B[43m----------
${deprecationWarning}
----------\x1B[0m
`);
    }
  }
  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;
  const router = (0, import_navigation.useRouter)();
  const push = (0, import_useAwaitablePush.useAwaitablePush)();
  const replace = (0, import_useAwaitableReplace.useAwaitableReplace)();
  const [isPending, startTransition] = (0, import_react.useTransition)();
  (0, import_useSafeLayoutEffect.useSafeLayoutEffect)(() => {
    if (import_feature_flags.canUseKeyless) {
      void (0, import_keyless_actions.detectKeylessEnvDriftAction)();
    }
  }, []);
  const isNested = Boolean((0, import_NextOptionsContext.useClerkNextOptions)());
  if (isNested) {
    return props.children;
  }
  (0, import_react.useEffect)(() => {
    var _a;
    if (!isPending) {
      (_a = window.__clerk_internal_invalidateCachePromise) == null ? void 0 : _a.call(window);
    }
  }, [isPending]);
  (0, import_useSafeLayoutEffect.useSafeLayoutEffect)(() => {
    window.__unstable__onBeforeSetActive = (intent) => {
      return new Promise((resolve) => {
        var _a;
        window.__clerk_internal_invalidateCachePromise = resolve;
        const nextVersion = ((_a = window == null ? void 0 : window.next) == null ? void 0 : _a.version) || "";
        if (nextVersion.startsWith("13")) {
          startTransition(() => {
            router.refresh();
          });
        } else if (nextVersion.startsWith("15") && intent === "sign-out") {
          resolve();
        } else {
          void (0, import_server_actions.invalidateCacheAction)().then(() => resolve());
        }
      });
    };
    window.__unstable__onAfterSetActive = () => {
      if (__unstable_invokeMiddlewareOnAuthStateChange) {
        return router.refresh();
      }
    };
  }, []);
  const mergedProps = (0, import_mergeNextClerkPropsWithEnv.mergeNextClerkPropsWithEnv)({
    ...props,
    // @ts-expect-error Error because of the stricter types of internal `push`
    routerPush: push,
    // @ts-expect-error Error because of the stricter types of internal `replace`
    routerReplace: replace
  });
  return /* @__PURE__ */ import_react.default.createElement(import_NextOptionsContext.ClerkNextOptionsProvider, { options: mergedProps }, /* @__PURE__ */ import_react.default.createElement(import_clerk_react.ClerkProvider, { ...mergedProps }, /* @__PURE__ */ import_react.default.createElement(import_router_telemetry.RouterTelemetry, null), /* @__PURE__ */ import_react.default.createElement(import_clerk_js_script.ClerkJSScript, { router: "app" }), children));
};
const ClientClerkProvider = (props) => {
  const { children, disableKeyless = false, ...rest } = props;
  const safePublishableKey = (0, import_mergeNextClerkPropsWithEnv.mergeNextClerkPropsWithEnv)(rest).publishableKey;
  if (safePublishableKey || !import_feature_flags.canUseKeyless || disableKeyless) {
    return /* @__PURE__ */ import_react.default.createElement(NextClientClerkProvider, { ...rest }, children);
  }
  return /* @__PURE__ */ import_react.default.createElement(LazyCreateKeylessApplication, null, /* @__PURE__ */ import_react.default.createElement(NextClientClerkProvider, { ...rest }, children));
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ClientClerkProvider
});
//# sourceMappingURL=ClerkProvider.js.map