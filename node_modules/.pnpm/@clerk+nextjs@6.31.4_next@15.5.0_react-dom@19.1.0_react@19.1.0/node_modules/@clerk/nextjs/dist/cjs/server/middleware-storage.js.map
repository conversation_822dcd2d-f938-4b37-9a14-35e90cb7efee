{"version": 3, "sources": ["../../../src/server/middleware-storage.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks';\n\nimport type { AuthenticateRequestOptions } from '@clerk/backend/internal';\n\nexport const clerkMiddlewareRequestDataStore = new Map<'requestData', AuthenticateRequestOptions>();\nexport const clerkMiddlewareRequestDataStorage = new AsyncLocalStorage<typeof clerkMiddlewareRequestDataStore>();\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAAkC;AAI3B,MAAM,kCAAkC,oBAAI,IAA+C;AAC3F,MAAM,oCAAoC,IAAI,0CAA0D;", "names": []}