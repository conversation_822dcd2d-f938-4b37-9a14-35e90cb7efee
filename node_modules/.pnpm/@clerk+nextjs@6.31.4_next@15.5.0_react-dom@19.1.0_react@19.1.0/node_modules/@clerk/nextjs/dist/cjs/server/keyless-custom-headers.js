"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var keyless_custom_headers_exports = {};
__export(keyless_custom_headers_exports, {
  collectKeylessMetadata: () => collectKeylessMetadata,
  formatMetadataHeaders: () => formatMetadataHeaders
});
module.exports = __toCommonJS(keyless_custom_headers_exports);
var import_headers = require("next/headers");
async function collectKeylessMetadata() {
  var _a;
  const headerStore = await (0, import_headers.headers)();
  return {
    nodeVersion: process.version,
    nextVersion: getNextVersion(),
    npmConfigUserAgent: process.env.npm_config_user_agent,
    // eslint-disable-line
    userAgent: (_a = headerStore.get("User-Agent")) != null ? _a : void 0
  };
}
function getNextVersion() {
  var _a;
  try {
    return (_a = process.title) != null ? _a : "unknown-process-title";
  } catch {
    return void 0;
  }
}
function formatMetadataHeaders(metadata) {
  const headers2 = new Headers();
  if (metadata.nodeVersion) {
    headers2.set("Clerk-Node-Version", metadata.nodeVersion);
  }
  if (metadata.nextVersion) {
    headers2.set("Clerk-Next-Version", metadata.nextVersion);
  }
  if (metadata.npmConfigUserAgent) {
    headers2.set("Clerk-NPM-Config-User-Agent", metadata.npmConfigUserAgent);
  }
  if (metadata.userAgent) {
    headers2.set("Clerk-Client-User-Agent", metadata.userAgent);
  }
  return headers2;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  collectKeylessMetadata,
  formatMetadataHeaders
});
//# sourceMappingURL=keyless-custom-headers.js.map