{"version": 3, "sources": ["../../../../src/app-router/server/auth.ts"], "sourcesContent": ["import type { AuthObject, InvalidTokenAuthObject, MachineAuthObject, SessionAuthObject } from '@clerk/backend';\nimport type {\n  AuthenticateRequestOptions,\n  InferAuthObjectFromToken,\n  InferAuthObjectFromTokenArray,\n  RedirectFun,\n  SessionTokenType,\n} from '@clerk/backend/internal';\nimport { constants, createClerkRequest, createRedirect, TokenType } from '@clerk/backend/internal';\nimport type { PendingSessionOptions } from '@clerk/types';\nimport { notFound, redirect } from 'next/navigation';\n\nimport { PUBLISHABLE_KEY, SIGN_IN_URL, SIGN_UP_URL } from '../../server/constants';\nimport { createAsyncGetAuth } from '../../server/createGetAuth';\nimport { authAuthHeaderMissing } from '../../server/errors';\nimport { getAuthKeyFromRequest, getHeader } from '../../server/headers-utils';\nimport { unauthorized } from '../../server/nextErrors';\nimport type { AuthProtect } from '../../server/protect';\nimport { createProtect } from '../../server/protect';\nimport { decryptClerkRequestData } from '../../server/utils';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { buildRequestLike } from './utils';\n\n/**\n * `Auth` object of the currently active user and the `redirectToSignIn()` method.\n */\ntype SessionAuthWithRedirect<TRedirect> = SessionAuthObject & {\n  /**\n   * The `auth()` helper returns the `redirectToSignIn()` method, which you can use to redirect the user to the sign-in page.\n   *\n   * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign in.\n   *\n   * > [!NOTE]\n   * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).\n   */\n  redirectToSignIn: RedirectFun<TRedirect>;\n\n  /**\n   * The `auth()` helper returns the `redirectToSignUp()` method, which you can use to redirect the user to the sign-up page.\n   *\n   * @param [returnBackUrl] {string | URL} - The URL to redirect the user back to after they sign up.\n   *\n   * > [!NOTE]\n   * > `auth()` on the server-side can only access redirect URLs defined via [environment variables](https://clerk.com/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) or [`clerkMiddleware` dynamic keys](https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys).\n   */\n  redirectToSignUp: RedirectFun<TRedirect>;\n};\n\nexport type AuthOptions = PendingSessionOptions & { acceptsToken?: AuthenticateRequestOptions['acceptsToken'] };\n\nexport interface AuthFn<TRedirect = ReturnType<typeof redirect>> {\n  /**\n   * @example\n   * const authObject = await auth({ acceptsToken: ['session_token', 'api_key'] })\n   */\n  <T extends TokenType[]>(\n    options: AuthOptions & { acceptsToken: T },\n  ): Promise<\n    | InferAuthObjectFromTokenArray<\n        T,\n        SessionAuthWithRedirect<TRedirect>,\n        MachineAuthObject<Exclude<T[number], SessionTokenType>>\n      >\n    | InvalidTokenAuthObject\n  >;\n\n  /**\n   * @example\n   * const authObject = await auth({ acceptsToken: 'session_token' })\n   */\n  <T extends TokenType>(\n    options: AuthOptions & { acceptsToken: T },\n  ): Promise<\n    InferAuthObjectFromToken<T, SessionAuthWithRedirect<TRedirect>, MachineAuthObject<Exclude<T, SessionTokenType>>>\n  >;\n\n  /**\n   * @example\n   * const authObject = await auth({ acceptsToken: 'any' })\n   */\n  (options: AuthOptions & { acceptsToken: 'any' }): Promise<AuthObject>;\n\n  /**\n   * @example\n   * const authObject = await auth()\n   */\n  (options?: PendingSessionOptions): Promise<SessionAuthWithRedirect<TRedirect>>;\n\n  /**\n   * `auth` includes a single property, the `protect()` method, which you can use in two ways:\n   * - to check if a user is authenticated (signed in)\n   * - to check if a user is authorized (has the correct roles or permissions) to access something, such as a component or a route handler\n   *\n   * The following table describes how auth.protect() behaves based on user authentication or authorization status:\n   *\n   * | Authenticated | Authorized | `auth.protect()` will |\n   * | - | - | - |\n   * | Yes | Yes | Return the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object. |\n   * | Yes | No | Return a `404` error. |\n   * | No | No | Redirect the user to the sign-in page\\*. |\n   *\n   * > [!IMPORTANT]\n   * > \\*For non-document requests, such as API requests, `auth.protect()` returns a `404` error to users who aren't authenticated.\n   *\n   * `auth.protect()` can be used to check if a user is authenticated or authorized to access certain parts of your application or even entire routes. See detailed examples in the [dedicated guide](https://clerk.com/docs/organizations/verify-user-permissions).\n   */\n  protect: AuthProtect;\n}\n\n/**\n * The `auth()` helper returns the [`Auth`](https://clerk.com/docs/references/backend/types/auth-object) object of the currently active user, as well as the [`redirectToSignIn()`](https://clerk.com/docs/references/nextjs/auth#redirect-to-sign-in) method.\n *\n * - Only available for App Router.\n * - Only works on the server-side, such as in Server Components, Route Handlers, and Server Actions.\n * - Requires [`clerkMiddleware()`](https://clerk.com/docs/references/nextjs/clerk-middleware) to be configured.\n */\nexport const auth: AuthFn = (async (options?: AuthOptions) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const request = await buildRequestLike();\n\n  const stepsBasedOnSrcDirectory = async () => {\n    if (isNextWithUnstableServerActions) {\n      return [];\n    }\n\n    try {\n      const isSrcAppDir = await import('../../server/fs/middleware-location.js').then(m => m.hasSrcAppDir());\n      return [`Your Middleware exists at ./${isSrcAppDir ? 'src/' : ''}middleware.(ts|js)`];\n    } catch {\n      return [];\n    }\n  };\n  const authObject = await createAsyncGetAuth({\n    debugLoggerName: 'auth()',\n    noAuthStatusMessage: authAuthHeaderMissing('auth', await stepsBasedOnSrcDirectory()),\n  })(request, {\n    treatPendingAsSignedOut: options?.treatPendingAsSignedOut,\n    acceptsToken: options?.acceptsToken ?? TokenType.SessionToken,\n  });\n\n  const clerkUrl = getAuthKeyFromRequest(request, 'ClerkUrl');\n\n  const createRedirectForRequest = (...args: Parameters<RedirectFun<never>>) => {\n    const { returnBackUrl } = args[0] || {};\n    const clerkRequest = createClerkRequest(request);\n    const devBrowserToken =\n      clerkRequest.clerkUrl.searchParams.get(constants.QueryParameters.DevBrowser) ||\n      clerkRequest.cookies.get(constants.Cookies.DevBrowser);\n\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n    return [\n      createRedirect({\n        redirectAdapter: redirect,\n        devBrowserToken: devBrowserToken,\n        baseUrl: clerkRequest.clerkUrl.toString(),\n        publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n        signInUrl: decryptedRequestData.signInUrl || SIGN_IN_URL,\n        signUpUrl: decryptedRequestData.signUpUrl || SIGN_UP_URL,\n        sessionStatus: authObject.tokenType === TokenType.SessionToken ? authObject.sessionStatus : null,\n      }),\n      returnBackUrl === null ? '' : returnBackUrl || clerkUrl?.toString(),\n    ] as const;\n  };\n\n  const redirectToSignIn: RedirectFun<never> = (opts = {}) => {\n    const [r, returnBackUrl] = createRedirectForRequest(opts);\n    return r.redirectToSignIn({\n      returnBackUrl,\n    });\n  };\n\n  const redirectToSignUp: RedirectFun<never> = (opts = {}) => {\n    const [r, returnBackUrl] = createRedirectForRequest(opts);\n    return r.redirectToSignUp({\n      returnBackUrl,\n    });\n  };\n\n  if (authObject.tokenType === TokenType.SessionToken) {\n    return Object.assign(authObject, { redirectToSignIn, redirectToSignUp });\n  }\n\n  return authObject;\n}) as AuthFn;\n\nauth.protect = async (...args: any[]) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const request = await buildRequestLike();\n  const requestedToken = args?.[0]?.token || args?.[1]?.token || TokenType.SessionToken;\n  const authObject = await auth({ acceptsToken: requestedToken });\n\n  const protect = createProtect({\n    request,\n    authObject,\n    redirectToSignIn: authObject.redirectToSignIn,\n    notFound,\n    redirect,\n    unauthorized,\n  });\n\n  return protect(...args);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,sBAAyE;AAEzE,wBAAmC;AAEnC,uBAA0D;AAC1D,2BAAmC;AACnC,oBAAsC;AACtC,2BAAiD;AACjD,wBAA6B;AAE7B,qBAA8B;AAC9B,mBAAwC;AACxC,0BAAgD;AAChD,IAAAA,gBAAiC;AA+F1B,MAAM,OAAgB,OAAO,YAA0B;AApH9D;AAsHE,UAAQ,aAAa;AAErB,QAAM,UAAU,UAAM,gCAAiB;AAEvC,QAAM,2BAA2B,YAAY;AAC3C,QAAI,qDAAiC;AACnC,aAAO,CAAC;AAAA,IACV;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,OAAO,wCAAwC,EAAE,KAAK,OAAK,EAAE,aAAa,CAAC;AACrG,aAAO,CAAC,+BAA+B,cAAc,SAAS,EAAE,oBAAoB;AAAA,IACtF,QAAQ;AACN,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACA,QAAM,aAAa,UAAM,yCAAmB;AAAA,IAC1C,iBAAiB;AAAA,IACjB,yBAAqB,qCAAsB,QAAQ,MAAM,yBAAyB,CAAC;AAAA,EACrF,CAAC,EAAE,SAAS;AAAA,IACV,yBAAyB,mCAAS;AAAA,IAClC,eAAc,wCAAS,iBAAT,YAAyB,0BAAU;AAAA,EACnD,CAAC;AAED,QAAM,eAAW,4CAAsB,SAAS,UAAU;AAE1D,QAAM,2BAA2B,IAAI,SAAyC;AAC5E,UAAM,EAAE,cAAc,IAAI,KAAK,CAAC,KAAK,CAAC;AACtC,UAAM,mBAAe,oCAAmB,OAAO;AAC/C,UAAM,kBACJ,aAAa,SAAS,aAAa,IAAI,0BAAU,gBAAgB,UAAU,KAC3E,aAAa,QAAQ,IAAI,0BAAU,QAAQ,UAAU;AAEvD,UAAM,2BAAuB,gCAAU,SAAS,0BAAU,QAAQ,gBAAgB;AAClF,UAAM,2BAAuB,sCAAwB,oBAAoB;AACzE,WAAO;AAAA,UACL,gCAAe;AAAA,QACb,iBAAiB;AAAA,QACjB;AAAA,QACA,SAAS,aAAa,SAAS,SAAS;AAAA,QACxC,gBAAgB,qBAAqB,kBAAkB;AAAA,QACvD,WAAW,qBAAqB,aAAa;AAAA,QAC7C,WAAW,qBAAqB,aAAa;AAAA,QAC7C,eAAe,WAAW,cAAc,0BAAU,eAAe,WAAW,gBAAgB;AAAA,MAC9F,CAAC;AAAA,MACD,kBAAkB,OAAO,KAAK,kBAAiB,qCAAU;AAAA,IAC3D;AAAA,EACF;AAEA,QAAM,mBAAuC,CAAC,OAAO,CAAC,MAAM;AAC1D,UAAM,CAAC,GAAG,aAAa,IAAI,yBAAyB,IAAI;AACxD,WAAO,EAAE,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,mBAAuC,CAAC,OAAO,CAAC,MAAM;AAC1D,UAAM,CAAC,GAAG,aAAa,IAAI,yBAAyB,IAAI;AACxD,WAAO,EAAE,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,WAAW,cAAc,0BAAU,cAAc;AACnD,WAAO,OAAO,OAAO,YAAY,EAAE,kBAAkB,iBAAiB,CAAC;AAAA,EACzE;AAEA,SAAO;AACT;AAEA,KAAK,UAAU,UAAU,SAAgB;AA5LzC;AA8LE,UAAQ,aAAa;AAErB,QAAM,UAAU,UAAM,gCAAiB;AACvC,QAAM,mBAAiB,kCAAO,OAAP,mBAAW,YAAS,kCAAO,OAAP,mBAAW,UAAS,0BAAU;AACzE,QAAM,aAAa,MAAM,KAAK,EAAE,cAAc,eAAe,CAAC;AAE9D,QAAM,cAAU,8BAAc;AAAA,IAC5B;AAAA,IACA;AAAA,IACA,kBAAkB,WAAW;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,SAAO,QAAQ,GAAG,IAAI;AACxB;", "names": ["import_utils"]}