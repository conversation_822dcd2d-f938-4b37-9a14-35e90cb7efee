{"version": 3, "sources": ["../../../src/utils/invalidateNextRouterCache.ts"], "sourcesContent": ["// interface FetchDataOutput {\n//   dataHref: string;\n//   json: Record<string, any> | null;\n//   response: Response;\n//   text: string;\n//   cacheKey: string;\n// }\n//\n// interface NextDataCache {\n//   [asPath: string]: Promise<FetchDataOutput>;\n// }\n\n/**\n * A placeholder for the actual next types.\n * The types above are not exported from the next package currently,\n * we only include them here for documentation purposes.\n * see: https://github.com/vercel/next.js/blob/018208fb15c9b969e173684668cea89588f4c536/packages/next/src/shared/lib/router/router.ts#L655\n */\ntype NextDataCache = any;\n\n/**\n * Only for /pages router\n *\n * Next currently prefetches the page of every visible Link on the page.\n * For every prefetch request, the middleware runs and the response is cached in\n * window.next.router.sdc or window.next.router.sdc\n *\n * Imagine a scenario with a /protected page requiring the user to be signed in using middleware.\n * If we don't invalidate the cache, we end up in the following redirect flow:\n * home -> /protected -> middleware redirects to /sign-in -> perform sign-in\n *            -> try to navigate to /protected but the cached 307 response is used\n *                   -> redirect to /sign-in instead -> withRedirectToHome -> home\n * When the auth state changes and the middleware runs again, the client-side router\n * does not automatically invalidate the cache so the browser follows the cached response\n *\n * This helper invalidates both known caches help prevent the scenario described above.\n */\nexport const invalidateNextRouterCache = () => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  const invalidate = (cache: NextDataCache) => {\n    // deleting the keys without nuking the cache by reassigning the variable to an empty object,\n    // in case next holds a reference to it\n    Object.keys(cache).forEach(key => {\n      delete cache[key];\n    });\n  };\n\n  try {\n    invalidate((window as any).next.router.sdc);\n    invalidate((window as any).next.router.sbc);\n  } catch {\n    return;\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAqCO,MAAM,4BAA4B,MAAM;AAC7C,MAAI,OAAO,WAAW,aAAa;AACjC;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,UAAyB;AAG3C,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAO;AAChC,aAAO,MAAM,GAAG;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,MAAI;AACF,eAAY,OAAe,KAAK,OAAO,GAAG;AAC1C,eAAY,OAAe,KAAK,OAAO,GAAG;AAAA,EAC5C,QAAQ;AACN;AAAA,EACF;AACF;", "names": []}