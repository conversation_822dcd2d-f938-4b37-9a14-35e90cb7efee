{"version": 3, "sources": ["../../../../src/client-boundary/hooks/usePathnameWithoutCatchAll.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { usePagesRouter } from './usePagesRouter';\n\nexport const usePathnameWithoutCatchAll = () => {\n  const pathRef = React.useRef<string>();\n\n  const { pagesRouter } = usePagesRouter();\n\n  if (pagesRouter) {\n    if (pathRef.current) {\n      return pathRef.current;\n    } else {\n      // in pages router things are simpler as the pathname includes the catch all route\n      // which starts with [[... and we can just remove it\n      pathRef.current = pagesRouter.pathname.replace(/\\/\\[\\[\\.\\.\\..*/, '');\n      return pathRef.current;\n    }\n  }\n\n  // require is used to avoid importing next/navigation when the pages router is used,\n  // as it will throw an error. We cannot use dynamic import as it is async\n  // and we need the hook to be sync\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const usePathname = require('next/navigation').usePathname;\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const useParams = require('next/navigation').useParams;\n\n  // Get the pathname that includes any named or catch all params\n  // eg:\n  // the filesystem route /user/[id]/profile/[[...rest]]/page.tsx\n  // could give us the following pathname /user/123/profile/security\n  // if the user navigates to the security section of the user profile\n  const pathname = usePathname() || '';\n  const pathParts = pathname.split('/').filter(Boolean);\n  // the useParams hook returns an object with all named and catch all params\n  // for named params, the key in the returned object always contains a single value\n  // for catch all params, the key in the returned object contains an array of values\n  // we find the catch all params by checking if the value is an array\n  // and then we remove one path part for each catch all param\n  const catchAllParams = Object.values(useParams() || {})\n    .filter(v => Array.isArray(v))\n    .flat(Infinity);\n  // so we end up with the pathname where the components are mounted at\n  // eg /user/123/profile/security will return /user/123/profile as the path\n  if (pathRef.current) {\n    return pathRef.current;\n  } else {\n    pathRef.current = `/${pathParts.slice(0, pathParts.length - catchAllParams.length).join('/')}`;\n    return pathRef.current;\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAElB,4BAA+B;AAExB,MAAM,6BAA6B,MAAM;AAC9C,QAAM,UAAU,aAAAA,QAAM,OAAe;AAErC,QAAM,EAAE,YAAY,QAAI,sCAAe;AAEvC,MAAI,aAAa;AACf,QAAI,QAAQ,SAAS;AACnB,aAAO,QAAQ;AAAA,IACjB,OAAO;AAGL,cAAQ,UAAU,YAAY,SAAS,QAAQ,kBAAkB,EAAE;AACnE,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAMA,QAAM,cAAc,QAAQ,iBAAiB,EAAE;AAE/C,QAAM,YAAY,QAAQ,iBAAiB,EAAE;AAO7C,QAAM,WAAW,YAAY,KAAK;AAClC,QAAM,YAAY,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO;AAMpD,QAAM,iBAAiB,OAAO,OAAO,UAAU,KAAK,CAAC,CAAC,EACnD,OAAO,OAAK,MAAM,QAAQ,CAAC,CAAC,EAC5B,KAAK,QAAQ;AAGhB,MAAI,QAAQ,SAAS;AACnB,WAAO,QAAQ;AAAA,EACjB,OAAO;AACL,YAAQ,UAAU,IAAI,UAAU,MAAM,GAAG,UAAU,SAAS,eAAe,MAAM,EAAE,KAAK,GAAG,CAAC;AAC5F,WAAO,QAAQ;AAAA,EACjB;AACF;", "names": ["React"]}