"use strict";
"use client";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var uiComponents_exports = {};
__export(uiComponents_exports, {
  APIKeys: () => import_clerk_react2.APIKeys,
  CreateOrganization: () => import_clerk_react2.CreateOrganization,
  GoogleOneTap: () => import_clerk_react2.GoogleOneTap,
  OrganizationList: () => import_clerk_react2.OrganizationList,
  OrganizationProfile: () => OrganizationProfile,
  OrganizationSwitcher: () => import_clerk_react2.OrganizationSwitcher,
  PricingTable: () => import_clerk_react2.PricingTable,
  SignIn: () => SignIn,
  SignInButton: () => import_clerk_react2.SignInButton,
  SignInWithMetamaskButton: () => import_clerk_react2.SignInWithMetamaskButton,
  SignOutButton: () => import_clerk_react2.SignOutButton,
  SignUp: () => SignUp,
  SignUpButton: () => import_clerk_react2.SignUpButton,
  TaskChooseOrganization: () => import_clerk_react2.TaskChooseOrganization,
  UserButton: () => import_clerk_react2.UserButton,
  UserProfile: () => UserProfile,
  Waitlist: () => import_clerk_react2.Waitlist
});
module.exports = __toCommonJS(uiComponents_exports);
var import_clerk_react = require("@clerk/clerk-react");
var import_react = __toESM(require("react"));
var import_useEnforceRoutingProps = require("./hooks/useEnforceRoutingProps");
var import_clerk_react2 = require("@clerk/clerk-react");
const UserProfile = Object.assign(
  (props) => {
    return /* @__PURE__ */ import_react.default.createElement(import_clerk_react.UserProfile, { ...(0, import_useEnforceRoutingProps.useEnforceCorrectRoutingProps)("UserProfile", props) });
  },
  { ...import_clerk_react.UserProfile }
);
const OrganizationProfile = Object.assign(
  (props) => {
    return /* @__PURE__ */ import_react.default.createElement(import_clerk_react.OrganizationProfile, { ...(0, import_useEnforceRoutingProps.useEnforceCorrectRoutingProps)("OrganizationProfile", props) });
  },
  { ...import_clerk_react.OrganizationProfile }
);
const SignIn = (props) => {
  return /* @__PURE__ */ import_react.default.createElement(import_clerk_react.SignIn, { ...(0, import_useEnforceRoutingProps.useEnforceCorrectRoutingProps)("SignIn", props, false) });
};
const SignUp = (props) => {
  return /* @__PURE__ */ import_react.default.createElement(import_clerk_react.SignUp, { ...(0, import_useEnforceRoutingProps.useEnforceCorrectRoutingProps)("SignUp", props, false) });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  APIKeys,
  CreateOrganization,
  GoogleOneTap,
  OrganizationList,
  OrganizationProfile,
  OrganizationSwitcher,
  PricingTable,
  SignIn,
  SignInButton,
  SignInWithMetamaskButton,
  SignOutButton,
  SignUp,
  SignUpButton,
  TaskChooseOrganization,
  UserButton,
  UserProfile,
  Waitlist
});
//# sourceMappingURL=uiComponents.js.map