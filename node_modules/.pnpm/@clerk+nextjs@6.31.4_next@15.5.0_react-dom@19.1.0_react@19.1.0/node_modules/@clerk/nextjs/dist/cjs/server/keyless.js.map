{"version": 3, "sources": ["../../../src/server/keyless.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\n\nimport { canUseKeyless } from '../utils/feature-flags';\n\nconst keylessCookiePrefix = `__clerk_keys_`;\n\nasync function hashString(str: string) {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(str);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n  const hashArray = Array.from(new Uint8Array(hashBuffer));\n  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n  return hashHex.slice(0, 16); // Take only the first 16 characters\n}\n\nasync function getKeylessCookieName(): Promise<string> {\n  // eslint-disable-next-line turbo/no-undeclared-env-vars\n  const PATH = process.env.PWD;\n\n  // Handle gracefully missing PWD\n  if (!PATH) {\n    return `${keylessCookiePrefix}${0}`;\n  }\n\n  const lastThreeDirs = PATH.split('/').filter(Boolean).slice(-3).reverse().join('/');\n\n  // Hash the resulting string\n  const hash = await hashString(lastThreeDirs);\n\n  return `${keylessCookiePrefix}${hash}`;\n}\n\nasync function getKeylessCookieValue(\n  getter: (cookieName: string) => string | undefined,\n): Promise<AccountlessApplication | undefined> {\n  if (!canUseKeyless) {\n    return undefined;\n  }\n\n  const keylessCookieName = await getKeylessCookieName();\n  let keyless;\n\n  try {\n    if (keylessCookieName) {\n      keyless = JSON.parse(getter(keylessCookieName) || '{}');\n    }\n  } catch {\n    keyless = undefined;\n  }\n\n  return keyless;\n}\n\nexport { getKeylessCookieValue, getKeylessCookieName };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,2BAA8B;AAE9B,MAAM,sBAAsB;AAE5B,eAAe,WAAW,KAAa;AACrC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,QAAQ,OAAO,GAAG;AAC/B,QAAM,aAAa,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAC7D,QAAM,YAAY,MAAM,KAAK,IAAI,WAAW,UAAU,CAAC;AACvD,QAAM,UAAU,UAAU,IAAI,OAAK,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AAC3E,SAAO,QAAQ,MAAM,GAAG,EAAE;AAC5B;AAEA,eAAe,uBAAwC;AAErD,QAAM,OAAO,QAAQ,IAAI;AAGzB,MAAI,CAAC,MAAM;AACT,WAAO,GAAG,mBAAmB,GAAG,CAAC;AAAA,EACnC;AAEA,QAAM,gBAAgB,KAAK,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,KAAK,GAAG;AAGlF,QAAM,OAAO,MAAM,WAAW,aAAa;AAE3C,SAAO,GAAG,mBAAmB,GAAG,IAAI;AACtC;AAEA,eAAe,sBACb,QAC6C;AAC7C,MAAI,CAAC,oCAAe;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,MAAM,qBAAqB;AACrD,MAAI;AAEJ,MAAI;AACF,QAAI,mBAAmB;AACrB,gBAAU,KAAK,MAAM,OAAO,iBAAiB,KAAK,IAAI;AAAA,IACxD;AAAA,EACF,QAAQ;AACN,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;", "names": []}