{"version": 3, "sources": ["../../../src/server/clerkClient.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\n\nimport { buildRequestLike, isPrerenderingBailout } from '../app-router/server/utils';\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { getHeader } from './headers-utils';\nimport { clerkMiddlewareRequestDataStorage } from './middleware-storage';\nimport { decryptClerkRequestData } from './utils';\n\n/**\n * Constructs a BAPI client that accesses request data within the runtime.\n * Necessary if middleware dynamic keys are used.\n */\nconst clerkClient = async () => {\n  let requestData;\n\n  try {\n    const request = await buildRequestLike();\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    requestData = decryptClerkRequestData(encryptedRequestData);\n  } catch (err) {\n    if (err && isPrerenderingBailout(err)) {\n      throw err;\n    }\n  }\n\n  // Fallbacks between options from middleware runtime and `NextRequest` from application server\n  const options = clerkMiddlewareRequestDataStorage.getStore()?.get('requestData') ?? requestData;\n  if (options?.secretKey || options?.publishableKey) {\n    return createClerkClientWithOptions(options);\n  }\n\n  return createClerkClientWithOptions({});\n};\n\nexport { clerkClient };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAA0B;AAE1B,mBAAwD;AACxD,+BAA6C;AAC7C,2BAA0B;AAC1B,gCAAkD;AAClD,IAAAA,gBAAwC;AAMxC,MAAM,cAAc,YAAY;AAZhC;AAaE,MAAI;AAEJ,MAAI;AACF,UAAM,UAAU,UAAM,+BAAiB;AACvC,UAAM,2BAAuB,gCAAU,SAAS,0BAAU,QAAQ,gBAAgB;AAClF,sBAAc,uCAAwB,oBAAoB;AAAA,EAC5D,SAAS,KAAK;AACZ,QAAI,WAAO,oCAAsB,GAAG,GAAG;AACrC,YAAM;AAAA,IACR;AAAA,EACF;AAGA,QAAM,WAAU,uEAAkC,SAAS,MAA3C,mBAA8C,IAAI,mBAAlD,YAAoE;AACpF,OAAI,mCAAS,eAAa,mCAAS,iBAAgB;AACjD,eAAO,uDAA6B,OAAO;AAAA,EAC7C;AAEA,aAAO,uDAA6B,CAAC,CAAC;AACxC;", "names": ["import_utils"]}