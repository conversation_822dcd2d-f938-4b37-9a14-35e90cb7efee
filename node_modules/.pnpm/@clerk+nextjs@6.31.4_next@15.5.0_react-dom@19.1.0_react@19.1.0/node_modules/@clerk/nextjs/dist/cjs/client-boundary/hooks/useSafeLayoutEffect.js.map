{"version": 3, "sources": ["../../../../src/client-boundary/hooks/useSafeLayoutEffect.tsx"], "sourcesContent": ["import React from 'react';\n\n// TODO: Import from shared once [JS-118] is done\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAGX,MAAM,sBAAsB,OAAO,WAAW,cAAc,aAAAA,QAAM,kBAAkB,aAAAA,QAAM;", "names": ["React"]}