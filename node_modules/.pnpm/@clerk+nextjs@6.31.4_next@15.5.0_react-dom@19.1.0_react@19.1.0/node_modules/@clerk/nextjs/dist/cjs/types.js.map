{"version": 3, "sources": ["../../src/types.ts"], "sourcesContent": ["import type { ClerkProviderProps } from '@clerk/clerk-react';\nimport type { Without } from '@clerk/types';\n\nexport type NextClerkProviderProps = Without<ClerkProviderProps, 'publishableKey'> & {\n  /**\n   * Used to override the default NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY env variable if needed.\n   * This is optional for NextJS as the ClerkProvider will automatically use the NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY env variable if it exists.\n   */\n  publishableKey?: string;\n  /**\n   * If set to true, the NextJS middleware will be invoked\n   * every time the client-side auth state changes (sign-out, sign-in, organization switch etc.).\n   * That way, any auth-dependent logic can be placed inside the middleware.\n   * Example: Configuring the middleware to force a redirect to `/sign-in` when the user signs out\n   *\n   * @default true\n   */\n  __unstable_invokeMiddlewareOnAuthStateChange?: boolean;\n  /**\n   * If set to true, ClerkProvider will opt into dynamic rendering and make auth data available to all wrapper components.\n   *\n   * @default false\n   */\n  dynamic?: boolean;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}