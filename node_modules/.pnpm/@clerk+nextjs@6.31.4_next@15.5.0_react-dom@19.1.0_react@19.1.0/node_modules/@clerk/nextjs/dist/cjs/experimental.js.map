{"version": 3, "sources": ["../../src/experimental.ts"], "sourcesContent": ["'use client';\n\nexport * from '@clerk/clerk-react/experimental';\n\nexport type {\n  __experimental_CheckoutButtonProps as CheckoutButtonProps,\n  __experimental_SubscriptionDetailsButtonProps as SubscriptionDetailsButtonProps,\n  __experimental_PlanDetailsButtonProps as PlanDetailsButtonProps,\n} from '@clerk/types';\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAEA,iCAAc,4CAFd;", "names": []}