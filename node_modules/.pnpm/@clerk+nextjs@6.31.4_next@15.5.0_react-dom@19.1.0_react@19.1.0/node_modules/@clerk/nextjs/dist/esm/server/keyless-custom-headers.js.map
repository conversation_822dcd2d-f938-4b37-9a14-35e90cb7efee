{"version": 3, "sources": ["../../../src/server/keyless-custom-headers.ts"], "sourcesContent": ["import { headers } from 'next/headers';\n\ninterface MetadataHeaders {\n  nodeVersion?: string;\n  nextVersion?: string;\n  npmConfigUserAgent?: string;\n  userAgent?: string;\n}\n\n/**\n * Collects metadata from the environment and request headers\n */\nexport async function collectKeylessMetadata(): Promise<MetadataHeaders> {\n  const headerStore = await headers(); // eslint-disable-line\n\n  return {\n    nodeVersion: process.version,\n    nextVersion: getNextVersion(),\n    npmConfigUserAgent: process.env.npm_config_user_agent, // eslint-disable-line\n    userAgent: headerStore.get('User-Agent') ?? undefined,\n  };\n}\n\n/**\n * Extracts Next.js version from process title\n */\nfunction getNextVersion(): string | undefined {\n  try {\n    return process.title ?? 'unknown-process-title'; // 'next-server (v15.4.5)'\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * Converts metadata to HTTP headers\n */\nexport function formatMetadataHeaders(metadata: MetadataHeaders): Headers {\n  const headers = new Headers();\n\n  if (metadata.nodeVersion) {\n    headers.set('Clerk-Node-Version', metadata.nodeVersion);\n  }\n\n  if (metadata.nextVersion) {\n    headers.set('Clerk-Next-Version', metadata.nextVersion);\n  }\n\n  if (metadata.npmConfigUserAgent) {\n    headers.set('Clerk-NPM-Config-User-Agent', metadata.npmConfigUserAgent);\n  }\n\n  if (metadata.userAgent) {\n    headers.set('Clerk-Client-User-Agent', metadata.userAgent);\n  }\n\n  return headers;\n}\n"], "mappings": ";AAAA,SAAS,eAAe;AAYxB,eAAsB,yBAAmD;AAZzE;AAaE,QAAM,cAAc,MAAM,QAAQ;AAElC,SAAO;AAAA,IACL,aAAa,QAAQ;AAAA,IACrB,aAAa,eAAe;AAAA,IAC5B,oBAAoB,QAAQ,IAAI;AAAA;AAAA,IAChC,YAAW,iBAAY,IAAI,YAAY,MAA5B,YAAiC;AAAA,EAC9C;AACF;AAKA,SAAS,iBAAqC;AA1B9C;AA2BE,MAAI;AACF,YAAO,aAAQ,UAAR,YAAiB;AAAA,EAC1B,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAKO,SAAS,sBAAsB,UAAoC;AACxE,QAAMA,WAAU,IAAI,QAAQ;AAE5B,MAAI,SAAS,aAAa;AACxB,IAAAA,SAAQ,IAAI,sBAAsB,SAAS,WAAW;AAAA,EACxD;AAEA,MAAI,SAAS,aAAa;AACxB,IAAAA,SAAQ,IAAI,sBAAsB,SAAS,WAAW;AAAA,EACxD;AAEA,MAAI,SAAS,oBAAoB;AAC/B,IAAAA,SAAQ,IAAI,+BAA+B,SAAS,kBAAkB;AAAA,EACxE;AAEA,MAAI,SAAS,WAAW;AACtB,IAAAA,SAAQ,IAAI,2BAA2B,SAAS,SAAS;AAAA,EAC3D;AAEA,SAAOA;AACT;", "names": ["headers"]}