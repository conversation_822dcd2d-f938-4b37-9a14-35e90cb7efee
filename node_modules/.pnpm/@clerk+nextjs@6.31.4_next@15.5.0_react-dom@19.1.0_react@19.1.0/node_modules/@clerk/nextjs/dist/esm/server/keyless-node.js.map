{"version": 3, "sources": ["../../../src/server/keyless-node.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\n\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from './fs/utils';\nimport { collectKeylessMetadata, formatMetadataHeaders } from './keyless-custom-headers';\n\n/**\n * The Clerk-specific directory name.\n */\nconst CLERK_HIDDEN = '.clerk';\n\n/**\n * The Clerk-specific lock file that is used to mitigate multiple key creation.\n * This is automatically cleaned up.\n */\nconst CLERK_LOCK = 'clerk.lock';\n\n/**\n * The `.clerk/` directory is NOT safe to be committed as it may include sensitive information about a Clerk instance.\n * It may include an instance's secret key and the secret token for claiming that instance.\n */\nfunction updateGitignore() {\n  const { existsSync, writeFileSync, readFileSync, appendFileSync } = nodeFsOrThrow();\n\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  const gitignorePath = path.join(cwd(), '.gitignore');\n  if (!existsSync(gitignorePath)) {\n    writeFileSync(gitignorePath, '');\n  }\n\n  // Check if `.clerk/` entry exists in .gitignore\n  const gitignoreContent = readFileSync(gitignorePath, 'utf-8');\n  const COMMENT = `# clerk configuration (can include secrets)`;\n  if (!gitignoreContent.includes(CLERK_HIDDEN + '/')) {\n    appendFileSync(gitignorePath, `\\n${COMMENT}\\n/${CLERK_HIDDEN}/\\n`);\n  }\n}\n\nconst generatePath = (...slugs: string[]) => {\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  return path.join(cwd(), CLERK_HIDDEN, ...slugs);\n};\n\nconst _TEMP_DIR_NAME = '.tmp';\nconst getKeylessConfigurationPath = () => generatePath(_TEMP_DIR_NAME, 'keyless.json');\nconst getKeylessReadMePath = () => generatePath(_TEMP_DIR_NAME, 'README.md');\n\nlet isCreatingFile = false;\n\nexport function safeParseClerkFile(): AccountlessApplication | undefined {\n  const { readFileSync } = nodeFsOrThrow();\n  try {\n    const CONFIG_PATH = getKeylessConfigurationPath();\n    let fileAsString;\n    try {\n      fileAsString = readFileSync(CONFIG_PATH, { encoding: 'utf-8' }) || '{}';\n    } catch {\n      fileAsString = '{}';\n    }\n    return JSON.parse(fileAsString) as AccountlessApplication;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * Using both an in-memory and file system lock seems to be the most effective solution.\n */\nconst lockFileWriting = () => {\n  const { writeFileSync } = nodeFsOrThrow();\n\n  isCreatingFile = true;\n\n  writeFileSync(\n    CLERK_LOCK,\n    // In the rare case, the file persists give the developer enough context.\n    'This file can be deleted. Please delete this file and refresh your application',\n    {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    },\n  );\n};\n\nconst unlockFileWriting = () => {\n  const { rmSync } = nodeFsOrThrow();\n\n  try {\n    rmSync(CLERK_LOCK, { force: true, recursive: true });\n  } catch {\n    // Simply ignore if the removal of the directory/file fails\n  }\n\n  isCreatingFile = false;\n};\n\nconst isFileWritingLocked = () => {\n  const { existsSync } = nodeFsOrThrow();\n  return isCreatingFile || existsSync(CLERK_LOCK);\n};\n\nasync function createOrReadKeyless(): Promise<AccountlessApplication | null> {\n  const { writeFileSync, mkdirSync } = nodeFsOrThrow();\n\n  /**\n   * If another request is already in the process of acquiring keys return early.\n   * Using both an in-memory and file system lock seems to be the most effective solution.\n   */\n  if (isFileWritingLocked()) {\n    return null;\n  }\n\n  lockFileWriting();\n\n  const CONFIG_PATH = getKeylessConfigurationPath();\n  const README_PATH = getKeylessReadMePath();\n\n  mkdirSync(generatePath(_TEMP_DIR_NAME), { recursive: true });\n  updateGitignore();\n\n  /**\n   * When the configuration file exists, always read the keys from the file\n   */\n  const envVarsMap = safeParseClerkFile();\n  if (envVarsMap?.publishableKey && envVarsMap?.secretKey) {\n    unlockFileWriting();\n\n    return envVarsMap;\n  }\n\n  /**\n   * At this step, it is safe to create new keys and store them.\n   */\n  const client = createClerkClientWithOptions({});\n\n  // Collect metadata\n  const keylessHeaders = await collectKeylessMetadata()\n    .then(formatMetadataHeaders)\n    .catch(() => new Headers());\n\n  const accountlessApplication = await client.__experimental_accountlessApplications\n    .createAccountlessApplication({ requestHeaders: keylessHeaders })\n    .catch(() => null);\n\n  if (accountlessApplication) {\n    writeFileSync(CONFIG_PATH, JSON.stringify(accountlessApplication), {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    });\n\n    // TODO-KEYLESS: Add link to official documentation.\n    const README_NOTIFICATION = `\n## DO NOT COMMIT\nThis directory is auto-generated from \\`@clerk/nextjs\\` because you are running in Keyless mode. Avoid committing the \\`.clerk/\\` directory as it includes the secret key of the unclaimed instance.\n  `;\n\n    writeFileSync(README_PATH, README_NOTIFICATION, {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    });\n  }\n  /**\n   * Clean up locks.\n   */\n  unlockFileWriting();\n\n  return accountlessApplication;\n}\n\nfunction removeKeyless() {\n  const { rmSync } = nodeFsOrThrow();\n\n  /**\n   * If another request is already in the process of acquiring keys return early.\n   * Using both an in-memory and file system lock seems to be the most effective solution.\n   */\n  if (isFileWritingLocked()) {\n    return undefined;\n  }\n\n  lockFileWriting();\n\n  try {\n    rmSync(generatePath(), { force: true, recursive: true });\n  } catch {\n    // Simply ignore if the removal of the directory/file fails\n  }\n\n  /**\n   * Clean up locks.\n   */\n  unlockFileWriting();\n}\n\nexport { createOrReadKeyless, removeKeyless };\n"], "mappings": ";AAEA,SAAS,oCAAoC;AAC7C,SAAS,gBAAgB,eAAe,uBAAuB;AAC/D,SAAS,wBAAwB,6BAA6B;AAK9D,MAAM,eAAe;AAMrB,MAAM,aAAa;AAMnB,SAAS,kBAAkB;AACzB,QAAM,EAAE,YAAY,eAAe,cAAc,eAAe,IAAI,cAAc;AAElF,QAAM,OAAO,gBAAgB;AAC7B,QAAM,MAAM,eAAe;AAC3B,QAAM,gBAAgB,KAAK,KAAK,IAAI,GAAG,YAAY;AACnD,MAAI,CAAC,WAAW,aAAa,GAAG;AAC9B,kBAAc,eAAe,EAAE;AAAA,EACjC;AAGA,QAAM,mBAAmB,aAAa,eAAe,OAAO;AAC5D,QAAM,UAAU;AAChB,MAAI,CAAC,iBAAiB,SAAS,eAAe,GAAG,GAAG;AAClD,mBAAe,eAAe;AAAA,EAAK,OAAO;AAAA,GAAM,YAAY;AAAA,CAAK;AAAA,EACnE;AACF;AAEA,MAAM,eAAe,IAAI,UAAoB;AAC3C,QAAM,OAAO,gBAAgB;AAC7B,QAAM,MAAM,eAAe;AAC3B,SAAO,KAAK,KAAK,IAAI,GAAG,cAAc,GAAG,KAAK;AAChD;AAEA,MAAM,iBAAiB;AACvB,MAAM,8BAA8B,MAAM,aAAa,gBAAgB,cAAc;AACrF,MAAM,uBAAuB,MAAM,aAAa,gBAAgB,WAAW;AAE3E,IAAI,iBAAiB;AAEd,SAAS,qBAAyD;AACvE,QAAM,EAAE,aAAa,IAAI,cAAc;AACvC,MAAI;AACF,UAAM,cAAc,4BAA4B;AAChD,QAAI;AACJ,QAAI;AACF,qBAAe,aAAa,aAAa,EAAE,UAAU,QAAQ,CAAC,KAAK;AAAA,IACrE,QAAQ;AACN,qBAAe;AAAA,IACjB;AACA,WAAO,KAAK,MAAM,YAAY;AAAA,EAChC,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAKA,MAAM,kBAAkB,MAAM;AAC5B,QAAM,EAAE,cAAc,IAAI,cAAc;AAExC,mBAAiB;AAEjB;AAAA,IACE;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,MAAM,oBAAoB,MAAM;AAC9B,QAAM,EAAE,OAAO,IAAI,cAAc;AAEjC,MAAI;AACF,WAAO,YAAY,EAAE,OAAO,MAAM,WAAW,KAAK,CAAC;AAAA,EACrD,QAAQ;AAAA,EAER;AAEA,mBAAiB;AACnB;AAEA,MAAM,sBAAsB,MAAM;AAChC,QAAM,EAAE,WAAW,IAAI,cAAc;AACrC,SAAO,kBAAkB,WAAW,UAAU;AAChD;AAEA,eAAe,sBAA8D;AAC3E,QAAM,EAAE,eAAe,UAAU,IAAI,cAAc;AAMnD,MAAI,oBAAoB,GAAG;AACzB,WAAO;AAAA,EACT;AAEA,kBAAgB;AAEhB,QAAM,cAAc,4BAA4B;AAChD,QAAM,cAAc,qBAAqB;AAEzC,YAAU,aAAa,cAAc,GAAG,EAAE,WAAW,KAAK,CAAC;AAC3D,kBAAgB;AAKhB,QAAM,aAAa,mBAAmB;AACtC,OAAI,yCAAY,oBAAkB,yCAAY,YAAW;AACvD,sBAAkB;AAElB,WAAO;AAAA,EACT;AAKA,QAAM,SAAS,6BAA6B,CAAC,CAAC;AAG9C,QAAM,iBAAiB,MAAM,uBAAuB,EACjD,KAAK,qBAAqB,EAC1B,MAAM,MAAM,IAAI,QAAQ,CAAC;AAE5B,QAAM,yBAAyB,MAAM,OAAO,uCACzC,6BAA6B,EAAE,gBAAgB,eAAe,CAAC,EAC/D,MAAM,MAAM,IAAI;AAEnB,MAAI,wBAAwB;AAC1B,kBAAc,aAAa,KAAK,UAAU,sBAAsB,GAAG;AAAA,MACjE,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,sBAAsB;AAAA;AAAA;AAAA;AAK5B,kBAAc,aAAa,qBAAqB;AAAA,MAC9C,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAIA,oBAAkB;AAElB,SAAO;AACT;AAEA,SAAS,gBAAgB;AACvB,QAAM,EAAE,OAAO,IAAI,cAAc;AAMjC,MAAI,oBAAoB,GAAG;AACzB,WAAO;AAAA,EACT;AAEA,kBAAgB;AAEhB,MAAI;AACF,WAAO,aAAa,GAAG,EAAE,OAAO,MAAM,WAAW,KAAK,CAAC;AAAA,EACzD,QAAQ;AAAA,EAER;AAKA,oBAAkB;AACpB;", "names": []}