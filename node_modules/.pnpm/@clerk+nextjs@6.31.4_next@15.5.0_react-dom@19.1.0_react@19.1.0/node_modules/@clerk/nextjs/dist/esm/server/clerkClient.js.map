{"version": 3, "sources": ["../../../src/server/clerkClient.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\n\nimport { buildRequestLike, isPrerenderingBailout } from '../app-router/server/utils';\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { getHeader } from './headers-utils';\nimport { clerkMiddlewareRequestDataStorage } from './middleware-storage';\nimport { decryptClerkRequestData } from './utils';\n\n/**\n * Constructs a BAPI client that accesses request data within the runtime.\n * Necessary if middleware dynamic keys are used.\n */\nconst clerkClient = async () => {\n  let requestData;\n\n  try {\n    const request = await buildRequestLike();\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    requestData = decryptClerkRequestData(encryptedRequestData);\n  } catch (err) {\n    if (err && isPrerenderingBailout(err)) {\n      throw err;\n    }\n  }\n\n  // Fallbacks between options from middleware runtime and `NextRequest` from application server\n  const options = clerkMiddlewareRequestDataStorage.getStore()?.get('requestData') ?? requestData;\n  if (options?.secretKey || options?.publishableKey) {\n    return createClerkClientWithOptions(options);\n  }\n\n  return createClerkClientWithOptions({});\n};\n\nexport { clerkClient };\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAE1B,SAAS,kBAAkB,6BAA6B;AACxD,SAAS,oCAAoC;AAC7C,SAAS,iBAAiB;AAC1B,SAAS,yCAAyC;AAClD,SAAS,+BAA+B;AAMxC,MAAM,cAAc,YAAY;AAZhC;AAaE,MAAI;AAEJ,MAAI;AACF,UAAM,UAAU,MAAM,iBAAiB;AACvC,UAAM,uBAAuB,UAAU,SAAS,UAAU,QAAQ,gBAAgB;AAClF,kBAAc,wBAAwB,oBAAoB;AAAA,EAC5D,SAAS,KAAK;AACZ,QAAI,OAAO,sBAAsB,GAAG,GAAG;AACrC,YAAM;AAAA,IACR;AAAA,EACF;AAGA,QAAM,WAAU,6CAAkC,SAAS,MAA3C,mBAA8C,IAAI,mBAAlD,YAAoE;AACpF,OAAI,mCAAS,eAAa,mCAAS,iBAAgB;AACjD,WAAO,6BAA6B,OAAO;AAAA,EAC7C;AAEA,SAAO,6BAA6B,CAAC,CAAC;AACxC;", "names": []}