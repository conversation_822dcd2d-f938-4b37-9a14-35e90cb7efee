{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  AuthenticateWithRedirect<PERSON><PERSON>back,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ClerkF<PERSON>,\n  <PERSON>Loaded,\n  ClerkLoading,\n  RedirectToCreateOrganization,\n  RedirectToOrganizationProfile,\n  RedirectToSignIn,\n  RedirectToSignUp,\n  RedirectToTasks,\n  RedirectToUserProfile,\n} from './client-boundary/controlComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  APIKeys,\n  CreateOrganization,\n  GoogleOneTap,\n  OrganizationList,\n  OrganizationProfile,\n  OrganizationSwitcher,\n  PricingTable,\n  SignIn,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUp,\n  SignUpButton,\n  TaskChooseOrganization,\n  UserButton,\n  UserProfile,\n  Waitlist,\n} from './client-boundary/uiComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  useAuth,\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useReverification,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n} from './client-boundary/hooks';\n\n/**\n * Conditionally export components that exhibit different behavior\n * when used in /app vs /pages.\n * We defined the runtime and the type values explicitly,\n * because TS will not recognize the subpath import unless the HOST\n * application sets moduleResolution to 'NodeNext'.\n */\n// @ts-ignore\nimport * as ComponentsModule from '#components';\n\nimport type { ServerComponentsServerModuleTypes } from './components.server';\n\nexport const ClerkProvider = ComponentsModule.ClerkProvider as ServerComponentsServerModuleTypes['ClerkProvider'];\nexport const SignedIn = ComponentsModule.SignedIn as ServerComponentsServerModuleTypes['SignedIn'];\nexport const SignedOut = ComponentsModule.SignedOut as ServerComponentsServerModuleTypes['SignedOut'];\nexport const Protect = ComponentsModule.Protect as ServerComponentsServerModuleTypes['Protect'];\n"], "mappings": ";AAIA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAMP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAMP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAUP,YAAY,sBAAsB;AAI3B,MAAM,gBAAgB,iBAAiB;AACvC,MAAM,WAAW,iBAAiB;AAClC,MAAM,YAAY,iBAAiB;AACnC,MAAM,UAAU,iBAAiB;", "names": []}