import "../chunk-BUSYA2B4.js";
import { headers } from "next/headers";
async function collectKeylessMetadata() {
  var _a;
  const headerStore = await headers();
  return {
    nodeVersion: process.version,
    nextVersion: getNextVersion(),
    npmConfigUserAgent: process.env.npm_config_user_agent,
    // eslint-disable-line
    userAgent: (_a = headerStore.get("User-Agent")) != null ? _a : void 0
  };
}
function getNextVersion() {
  var _a;
  try {
    return (_a = process.title) != null ? _a : "unknown-process-title";
  } catch {
    return void 0;
  }
}
function formatMetadataHeaders(metadata) {
  const headers2 = new Headers();
  if (metadata.nodeVersion) {
    headers2.set("Clerk-Node-Version", metadata.nodeVersion);
  }
  if (metadata.nextVersion) {
    headers2.set("Clerk-Next-Version", metadata.nextVersion);
  }
  if (metadata.npmConfigUserAgent) {
    headers2.set("Clerk-NPM-Config-User-Agent", metadata.npmConfigUserAgent);
  }
  if (metadata.userAgent) {
    headers2.set("Clerk-Client-User-Agent", metadata.userAgent);
  }
  return headers2;
}
export {
  collectKeylessMetadata,
  formatMetadataHeaders
};
//# sourceMappingURL=keyless-custom-headers.js.map