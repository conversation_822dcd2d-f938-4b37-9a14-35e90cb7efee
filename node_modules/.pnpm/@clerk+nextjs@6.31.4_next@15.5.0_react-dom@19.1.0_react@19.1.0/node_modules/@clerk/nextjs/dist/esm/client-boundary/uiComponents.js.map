{"version": 3, "sources": ["../../../src/client-boundary/uiComponents.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { ComponentProps } from 'react';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  APIKeys,\n  CreateOrganization,\n  GoogleOneTap,\n  OrganizationList,\n  OrganizationSwitcher,\n  PricingTable,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  TaskChooseOrganization,\n  UserButton,\n  Waitlist,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: ComponentProps<typeof BaseUserProfile>) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: ComponentProps<typeof BaseOrganizationProfile>) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: ComponentProps<typeof BaseSignIn>) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: ComponentProps<typeof BaseSignUp>) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "mappings": ";;AAEA;AAAA,EACE,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,OACV;AAEP,OAAO,WAAW;AAElB,SAAS,qCAAqC;AAE9C;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAMA,MAAM,cAAsC,OAAO;AAAA,EACxD,CAAC,UAAkD;AACjD,WAAO,oCAAC,mBAAiB,GAAG,8BAA8B,eAAe,KAAK,GAAG;AAAA,EACnF;AAAA,EACA,EAAE,GAAG,gBAAgB;AACvB;AAMO,MAAM,sBAAsD,OAAO;AAAA,EACxE,CAAC,UAA0D;AACzD,WAAO,oCAAC,2BAAyB,GAAG,8BAA8B,uBAAuB,KAAK,GAAG;AAAA,EACnG;AAAA,EACA,EAAE,GAAG,wBAAwB;AAC/B;AAEO,MAAM,SAAS,CAAC,UAA6C;AAClE,SAAO,oCAAC,cAAY,GAAG,8BAA8B,UAAU,OAAO,KAAK,GAAG;AAChF;AAEO,MAAM,SAAS,CAAC,UAA6C;AAClE,SAAO,oCAAC,cAAY,GAAG,8BAA8B,UAAU,OAAO,KAAK,GAAG;AAChF;", "names": []}