{"version": 3, "sources": ["../../../src/client-boundary/hooks.ts"], "sourcesContent": ["'use client';\n\nexport {\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n  useReverification,\n} from '@clerk/clerk-react';\n\nexport {\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isReverificationCancelledError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n} from '@clerk/clerk-react/errors';\n\nexport { usePromisifiedAuth as useAuth } from './PromisifiedAuthProvider';\n"], "mappings": ";;AAEA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,SAA+B,0BAAe;", "names": []}