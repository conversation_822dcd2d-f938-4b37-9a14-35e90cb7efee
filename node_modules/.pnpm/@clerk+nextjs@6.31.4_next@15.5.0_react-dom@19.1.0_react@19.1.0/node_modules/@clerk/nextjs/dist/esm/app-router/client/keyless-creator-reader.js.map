{"version": 3, "sources": ["../../../../src/app-router/client/keyless-creator-reader.tsx"], "sourcesContent": ["import { useSelectedLayoutSegments } from 'next/navigation';\nimport React, { useEffect } from 'react';\n\nimport type { NextClerkProviderProps } from '../../types';\nimport { createOrReadKeylessAction } from '../keyless-actions';\n\nexport const KeylessCreatorOrReader = (props: NextClerkProviderProps) => {\n  const { children } = props;\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n  const [state, fetchKeys] = React.useActionState(createOrReadKeylessAction, null);\n  useEffect(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    React.startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n\n  if (!React.isValidElement(children)) {\n    return children;\n  }\n\n  return React.cloneElement(children, {\n    key: state?.publishableKey,\n    publishableKey: state?.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state?.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state?.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true,\n  } as any);\n};\n"], "mappings": ";AAAA,SAAS,iCAAiC;AAC1C,OAAO,SAAS,iBAAiB;AAGjC,SAAS,iCAAiC;AAEnC,MAAM,yBAAyB,CAAC,UAAkC;AANzE;AAOE,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,WAAW,0BAA0B;AAC3C,QAAM,oBAAkB,cAAS,CAAC,MAAV,mBAAa,WAAW,mBAAkB;AAClE,QAAM,CAAC,OAAO,SAAS,IAAI,MAAM,eAAe,2BAA2B,IAAI;AAC/E,YAAU,MAAM;AACd,QAAI,iBAAiB;AACnB;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM;AAC1B,gBAAU;AAAA,IACZ,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,CAAC;AAEpB,MAAI,CAAC,MAAM,eAAe,QAAQ,GAAG;AACnC,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,aAAa,UAAU;AAAA,IAClC,KAAK,+BAAO;AAAA,IACZ,gBAAgB,+BAAO;AAAA,IACvB,+CAA+C,+BAAO;AAAA,IACtD,wCAAwC,+BAAO;AAAA,IAC/C,wCAAwC;AAAA,EAC1C,CAAQ;AACV;", "names": []}