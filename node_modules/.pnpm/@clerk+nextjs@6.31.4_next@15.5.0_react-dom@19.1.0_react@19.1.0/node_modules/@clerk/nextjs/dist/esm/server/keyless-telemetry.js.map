{"version": 3, "sources": ["../../../src/server/keyless-telemetry.ts"], "sourcesContent": ["import type { TelemetryEventRaw } from '@clerk/types';\nimport { dirname, join } from 'path';\n\nimport { canUseKeyless } from '../utils/feature-flags';\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { nodeFsOrThrow } from './fs/utils';\n\nconst EVENT_KEYLESS_ENV_DRIFT_DETECTED = 'KEYLESS_ENV_DRIFT_DETECTED';\nconst EVENT_SAMPLING_RATE = 1; // 100% sampling rate\nconst TELEMETRY_FLAG_FILE = '.clerk/.tmp/telemetry.json';\n\ntype EventKeylessEnvDriftPayload = {\n  publicKeyMatch: boolean;\n  secretKeyMatch: boolean;\n  envVarsMissing: boolean;\n  keylessFileHasKeys: boolean;\n  keylessPublishableKey: string;\n  envPublishableKey: string;\n};\n\n/**\n * Gets the absolute path to the telemetry flag file.\n *\n * This file is used to track whether telemetry events have already been fired\n * to prevent duplicate event reporting during the application lifecycle.\n *\n * @returns The absolute path to the telemetry flag file in the project's .clerk/.tmp directory\n */\nfunction getTelemetryFlagFilePath(): string {\n  return join(process.cwd(), TELEMETRY_FLAG_FILE);\n}\n\n/**\n * Attempts to create a telemetry flag file to mark that a telemetry event has been fired.\n *\n * This function uses the 'wx' flag to create the file atomically - it will only succeed\n * if the file doesn't already exist. This ensures that telemetry events are only fired\n * once per application lifecycle, preventing duplicate event reporting.\n *\n * @returns Promise<boolean> - Returns true if the flag file was successfully created (meaning\n *   the event should be fired), false if the file already exists (meaning the event was\n *   already fired) or if there was an error creating the file\n */\nfunction tryMarkTelemetryEventAsFired(): boolean {\n  try {\n    if (canUseKeyless) {\n      const { mkdirSync, writeFileSync } = nodeFsOrThrow();\n      const flagFilePath = getTelemetryFlagFilePath();\n      const flagDirectory = dirname(flagFilePath);\n\n      // Ensure the directory exists before attempting to write the file\n      mkdirSync(flagDirectory, { recursive: true });\n\n      const flagData = {\n        firedAt: new Date().toISOString(),\n        event: EVENT_KEYLESS_ENV_DRIFT_DETECTED,\n      };\n      writeFileSync(flagFilePath, JSON.stringify(flagData, null, 2), { flag: 'wx' });\n      return true;\n    } else {\n      return false;\n    }\n  } catch (error: unknown) {\n    if ((error as { code?: string })?.code === 'EEXIST') {\n      return false;\n    }\n    console.warn('Failed to create telemetry flag file:', error);\n    return false;\n  }\n}\n\n/**\n * Detects and reports environment drift between keyless configuration and environment variables.\n *\n * This function compares the Clerk keys stored in the keyless configuration file (.clerk/clerk.json)\n * with the keys set in environment variables (NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY and CLERK_SECRET_KEY).\n * It only reports drift when there's an actual mismatch between existing keys, not when keys are simply missing.\n *\n * The function handles several scenarios and only reports drift in specific cases:\n * - **Normal keyless mode**: env vars missing but keyless file has keys → no drift (expected)\n * - **No configuration**: neither env vars nor keyless file have keys → no drift (nothing to compare)\n * - **Actual drift**: env vars exist and don't match keyless file keys → drift detected\n * - **Empty keyless file**: keyless file exists but has no keys → no drift (nothing to compare)\n *\n * Drift is only detected when:\n * 1. Both environment variables and keyless file contain keys\n * 2. The keys in environment variables don't match the keys in the keyless file\n *\n * Telemetry events are only fired once per application lifecycle using a flag file mechanism\n * to prevent duplicate reporting.\n *\n * @returns Promise<void> - Function completes silently, errors are logged but don't throw\n */\nexport async function detectKeylessEnvDrift(): Promise<void> {\n  if (!canUseKeyless) {\n    return;\n  }\n  // Only run on server side\n  if (typeof window !== 'undefined') {\n    return;\n  }\n\n  try {\n    // Dynamically import server-side dependencies to avoid client-side issues\n    const { safeParseClerkFile } = await import('./keyless-node.js');\n\n    // Read the keyless configuration file\n    const keylessFile = safeParseClerkFile();\n\n    if (!keylessFile) {\n      return;\n    }\n\n    // Get environment variables\n    const envPublishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;\n    const envSecretKey = process.env.CLERK_SECRET_KEY;\n\n    // Check the state of environment variables and keyless file\n    const hasEnvVars = Boolean(envPublishableKey || envSecretKey);\n    const keylessFileHasKeys = Boolean(keylessFile?.publishableKey && keylessFile?.secretKey);\n    const envVarsMissing = !envPublishableKey && !envSecretKey;\n\n    // Early return conditions - no drift to detect in these scenarios:\n    if (!hasEnvVars && !keylessFileHasKeys) {\n      // Neither env vars nor keyless file have keys - nothing to compare\n      return;\n    }\n\n    if (envVarsMissing && keylessFileHasKeys) {\n      // Environment variables are missing but keyless file has keys - this is normal for keyless mode\n      return;\n    }\n\n    if (!keylessFileHasKeys) {\n      // Keyless file doesn't have keys, so no drift can be detected\n      return;\n    }\n\n    // Only proceed with drift detection if we have something meaningful to compare\n    if (!hasEnvVars) {\n      return;\n    }\n\n    // Compare keys only when both sides have values to compare\n    const publicKeyMatch = Boolean(\n      envPublishableKey && keylessFile.publishableKey && envPublishableKey === keylessFile.publishableKey,\n    );\n\n    const secretKeyMatch = Boolean(envSecretKey && keylessFile.secretKey && envSecretKey === keylessFile.secretKey);\n\n    // Determine if there's an actual drift:\n    // Drift occurs when we have env vars that don't match the keyless file keys\n    const hasActualDrift =\n      (envPublishableKey && keylessFile.publishableKey && !publicKeyMatch) ||\n      (envSecretKey && keylessFile.secretKey && !secretKeyMatch);\n\n    // Only fire telemetry if there's an actual drift (not just missing keys)\n    if (!hasActualDrift) {\n      return;\n    }\n\n    const payload: EventKeylessEnvDriftPayload = {\n      publicKeyMatch,\n      secretKeyMatch,\n      envVarsMissing,\n      keylessFileHasKeys,\n      keylessPublishableKey: keylessFile.publishableKey ?? '',\n      envPublishableKey: envPublishableKey ?? '',\n    };\n\n    // Create a clerk client to access telemetry\n    const clerkClient = createClerkClientWithOptions({\n      publishableKey: keylessFile.publishableKey,\n      secretKey: keylessFile.secretKey,\n      telemetry: {\n        samplingRate: 1,\n      },\n    });\n\n    const shouldFireEvent = tryMarkTelemetryEventAsFired();\n\n    if (shouldFireEvent) {\n      // Fire drift detected event only if we successfully created the flag\n      const driftDetectedEvent: TelemetryEventRaw<EventKeylessEnvDriftPayload> = {\n        event: EVENT_KEYLESS_ENV_DRIFT_DETECTED,\n        eventSamplingRate: EVENT_SAMPLING_RATE,\n        payload,\n      };\n\n      clerkClient.telemetry?.record(driftDetectedEvent);\n    }\n  } catch (error) {\n    // Silently handle errors to avoid breaking the application\n    console.warn('Failed to detect keyless environment drift:', error);\n  }\n}\n"], "mappings": ";AACA,SAAS,SAAS,YAAY;AAE9B,SAAS,qBAAqB;AAC9B,SAAS,oCAAoC;AAC7C,SAAS,qBAAqB;AAE9B,MAAM,mCAAmC;AACzC,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAmB5B,SAAS,2BAAmC;AAC1C,SAAO,KAAK,QAAQ,IAAI,GAAG,mBAAmB;AAChD;AAaA,SAAS,+BAAwC;AAC/C,MAAI;AACF,QAAI,eAAe;AACjB,YAAM,EAAE,WAAW,cAAc,IAAI,cAAc;AACnD,YAAM,eAAe,yBAAyB;AAC9C,YAAM,gBAAgB,QAAQ,YAAY;AAG1C,gBAAU,eAAe,EAAE,WAAW,KAAK,CAAC;AAE5C,YAAM,WAAW;AAAA,QACf,UAAS,oBAAI,KAAK,GAAE,YAAY;AAAA,QAChC,OAAO;AAAA,MACT;AACA,oBAAc,cAAc,KAAK,UAAU,UAAU,MAAM,CAAC,GAAG,EAAE,MAAM,KAAK,CAAC;AAC7E,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,SAAS,OAAgB;AACvB,SAAK,+BAA6B,UAAS,UAAU;AACnD,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,yCAAyC,KAAK;AAC3D,WAAO;AAAA,EACT;AACF;AAwBA,eAAsB,wBAAuC;AA7F7D;AA8FE,MAAI,CAAC,eAAe;AAClB;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,aAAa;AACjC;AAAA,EACF;AAEA,MAAI;AAEF,UAAM,EAAE,mBAAmB,IAAI,MAAM,OAAO,mBAAmB;AAG/D,UAAM,cAAc,mBAAmB;AAEvC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AAGA,UAAM,oBAAoB,QAAQ,IAAI;AACtC,UAAM,eAAe,QAAQ,IAAI;AAGjC,UAAM,aAAa,QAAQ,qBAAqB,YAAY;AAC5D,UAAM,qBAAqB,SAAQ,2CAAa,oBAAkB,2CAAa,UAAS;AACxF,UAAM,iBAAiB,CAAC,qBAAqB,CAAC;AAG9C,QAAI,CAAC,cAAc,CAAC,oBAAoB;AAEtC;AAAA,IACF;AAEA,QAAI,kBAAkB,oBAAoB;AAExC;AAAA,IACF;AAEA,QAAI,CAAC,oBAAoB;AAEvB;AAAA,IACF;AAGA,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAGA,UAAM,iBAAiB;AAAA,MACrB,qBAAqB,YAAY,kBAAkB,sBAAsB,YAAY;AAAA,IACvF;AAEA,UAAM,iBAAiB,QAAQ,gBAAgB,YAAY,aAAa,iBAAiB,YAAY,SAAS;AAI9G,UAAM,iBACH,qBAAqB,YAAY,kBAAkB,CAAC,kBACpD,gBAAgB,YAAY,aAAa,CAAC;AAG7C,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AAEA,UAAM,UAAuC;AAAA,MAC3C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,wBAAuB,iBAAY,mBAAZ,YAA8B;AAAA,MACrD,mBAAmB,gDAAqB;AAAA,IAC1C;AAGA,UAAM,cAAc,6BAA6B;AAAA,MAC/C,gBAAgB,YAAY;AAAA,MAC5B,WAAW,YAAY;AAAA,MACvB,WAAW;AAAA,QACT,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAED,UAAM,kBAAkB,6BAA6B;AAErD,QAAI,iBAAiB;AAEnB,YAAM,qBAAqE;AAAA,QACzE,OAAO;AAAA,QACP,mBAAmB;AAAA,QACnB;AAAA,MACF;AAEA,wBAAY,cAAZ,mBAAuB,OAAO;AAAA,IAChC;AAAA,EACF,SAAS,OAAO;AAEd,YAAQ,KAAK,+CAA+C,KAAK;AAAA,EACnE;AACF;", "names": []}