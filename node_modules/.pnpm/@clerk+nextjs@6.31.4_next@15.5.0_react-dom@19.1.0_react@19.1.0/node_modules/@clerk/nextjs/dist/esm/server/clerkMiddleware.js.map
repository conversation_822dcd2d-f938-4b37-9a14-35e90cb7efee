{"version": 3, "sources": ["../../../src/server/clerkMiddleware.ts"], "sourcesContent": ["import type { AuthObject, ClerkClient } from '@clerk/backend';\nimport type {\n  AuthenticatedState,\n  AuthenticateRequestOptions,\n  ClerkRequest,\n  RedirectFun,\n  RequestState,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n  UnauthenticatedState,\n} from '@clerk/backend/internal';\nimport {\n  AuthStatus,\n  constants,\n  createClerkRequest,\n  createRedirect,\n  getAuthObjectForAcceptedToken,\n  isMachineTokenByPrefix,\n  isTokenTypeAccepted,\n  makeAuthObjectSerializable,\n  TokenType,\n} from '@clerk/backend/internal';\nimport { parsePublishableKey } from '@clerk/shared/keys';\nimport { notFound as nextjsNotFound } from 'next/navigation';\nimport type { NextMiddleware, NextRequest } from 'next/server';\nimport { NextResponse } from 'next/server';\n\nimport type { AuthFn } from '../app-router/server/auth';\nimport type { GetAuthOptions } from '../server/createGetAuth';\nimport { isRedirect, serverRedirectWithA<PERSON>, setHeader } from '../utils';\nimport { withLogger } from '../utils/debugLogger';\nimport { canUseKeyless } from '../utils/feature-flags';\nimport { clerkClient } from './clerkClient';\nimport { PUBLISHABLE_KEY, SECRET_KEY, SIGN_IN_URL, SIGN_UP_URL } from './constants';\nimport { type ContentSecurityPolicyOptions, createContentSecurityPolicyHeaders } from './content-security-policy';\nimport { errorThrower } from './errorThrower';\nimport { getHeader } from './headers-utils';\nimport { getKeylessCookieValue } from './keyless';\nimport { clerkMiddlewareRequestDataStorage, clerkMiddlewareRequestDataStore } from './middleware-storage';\nimport {\n  isNextjsNotFoundError,\n  isNextjsRedirectError,\n  isNextjsUnauthorizedError,\n  isRedirectToSignInError,\n  isRedirectToSignUpError,\n  nextjsRedirectError,\n  redirectToSignInError,\n  redirectToSignUpError,\n  unauthorized,\n} from './nextErrors';\nimport type { AuthProtect } from './protect';\nimport { createProtect } from './protect';\nimport type { NextMiddlewareEvtParam, NextMiddlewareRequestParam, NextMiddlewareReturn } from './types';\nimport {\n  assertKey,\n  decorateRequest,\n  handleMultiDomainAndProxy,\n  redirectAdapter,\n  setRequestHeadersOnNextResponse,\n} from './utils';\n\nexport type ClerkMiddlewareSessionAuthObject = (SignedInAuthObject | SignedOutAuthObject) & {\n  redirectToSignIn: RedirectFun<Response>;\n  redirectToSignUp: RedirectFun<Response>;\n};\n\n/**\n * @deprecated Use `ClerkMiddlewareSessionAuthObject` instead.\n */\nexport type ClerkMiddlewareAuthObject = ClerkMiddlewareSessionAuthObject;\n\nexport type ClerkMiddlewareAuth = AuthFn<Response>;\n\ntype ClerkMiddlewareHandler = (\n  auth: ClerkMiddlewareAuth,\n  request: NextMiddlewareRequestParam,\n  event: NextMiddlewareEvtParam,\n) => NextMiddlewareReturn;\n\ntype AuthenticateAnyRequestOptions = Omit<AuthenticateRequestOptions, 'acceptsToken'>;\n\n/**\n * The `clerkMiddleware()` function accepts an optional object. The following options are available.\n * @interface\n */\nexport interface ClerkMiddlewareOptions extends AuthenticateAnyRequestOptions {\n  /**\n   * If true, additional debug information will be logged to the console.\n   */\n  debug?: boolean;\n\n  /**\n   * When set, automatically injects a Content-Security-Policy header(s) compatible with Clerk.\n   */\n  contentSecurityPolicy?: ContentSecurityPolicyOptions;\n}\n\ntype ClerkMiddlewareOptionsCallback = (req: NextRequest) => ClerkMiddlewareOptions | Promise<ClerkMiddlewareOptions>;\n\n/**\n * Middleware for Next.js that handles authentication and authorization with Clerk.\n * For more details, please refer to the docs: https://clerk.com/docs/references/nextjs/clerk-middleware\n */\ninterface ClerkMiddleware {\n  /**\n   * @example\n   * export default clerkMiddleware((auth, request, event) => { ... }, options);\n   */\n  (handler: ClerkMiddlewareHandler, options?: ClerkMiddlewareOptions): NextMiddleware;\n\n  /**\n   * @example\n   * export default clerkMiddleware((auth, request, event) => { ... }, (req) => options);\n   */\n  (handler: ClerkMiddlewareHandler, options?: ClerkMiddlewareOptionsCallback): NextMiddleware;\n\n  /**\n   * @example\n   * export default clerkMiddleware(options);\n   */\n  (options?: ClerkMiddlewareOptions): NextMiddleware;\n\n  /**\n   * @example\n   * export default clerkMiddleware;\n   */\n  (request: NextMiddlewareRequestParam, event: NextMiddlewareEvtParam): NextMiddlewareReturn;\n}\n\n/**\n * The `clerkMiddleware()` helper integrates Clerk authentication into your Next.js application through Middleware. `clerkMiddleware()` is compatible with both the App and Pages routers.\n */\nexport const clerkMiddleware = ((...args: unknown[]): NextMiddleware | NextMiddlewareReturn => {\n  const [request, event] = parseRequestAndEvent(args);\n  const [handler, params] = parseHandlerAndOptions(args);\n\n  const middleware = clerkMiddlewareRequestDataStorage.run(clerkMiddlewareRequestDataStore, () => {\n    const baseNextMiddleware: NextMiddleware = withLogger('clerkMiddleware', logger => async (request, event) => {\n      // Handles the case where `options` is a callback function to dynamically access `NextRequest`\n      const resolvedParams = typeof params === 'function' ? await params(request) : params;\n\n      const keyless = await getKeylessCookieValue(name => request.cookies.get(name)?.value);\n\n      const publishableKey = assertKey(\n        resolvedParams.publishableKey || PUBLISHABLE_KEY || keyless?.publishableKey,\n        () => errorThrower.throwMissingPublishableKeyError(),\n      );\n\n      const secretKey = assertKey(resolvedParams.secretKey || SECRET_KEY || keyless?.secretKey, () =>\n        errorThrower.throwMissingSecretKeyError(),\n      );\n      const signInUrl = resolvedParams.signInUrl || SIGN_IN_URL;\n      const signUpUrl = resolvedParams.signUpUrl || SIGN_UP_URL;\n\n      const options = {\n        publishableKey,\n        secretKey,\n        signInUrl,\n        signUpUrl,\n        ...resolvedParams,\n      };\n\n      // Propagates the request data to be accessed on the server application runtime from helpers such as `clerkClient`\n      clerkMiddlewareRequestDataStore.set('requestData', options);\n      const resolvedClerkClient = await clerkClient();\n\n      if (options.debug) {\n        logger.enable();\n      }\n      const clerkRequest = createClerkRequest(request);\n      logger.debug('options', options);\n      logger.debug('url', () => clerkRequest.toJSON());\n\n      const authHeader = request.headers.get(constants.Headers.Authorization);\n      if (authHeader && authHeader.startsWith('Basic ')) {\n        logger.debug('Basic Auth detected');\n      }\n\n      const cspHeader = request.headers.get(constants.Headers.ContentSecurityPolicy);\n      if (cspHeader) {\n        logger.debug('Content-Security-Policy detected', () => ({\n          value: cspHeader,\n        }));\n      }\n\n      const requestState = await resolvedClerkClient.authenticateRequest(\n        clerkRequest,\n        createAuthenticateRequestOptions(clerkRequest, options),\n      );\n\n      logger.debug('requestState', () => ({\n        status: requestState.status,\n        // @ts-expect-error : FIXME\n        headers: JSON.stringify(Object.fromEntries(requestState.headers)),\n        reason: requestState.reason,\n      }));\n\n      const locationHeader = requestState.headers.get(constants.Headers.Location);\n      if (locationHeader) {\n        const res = NextResponse.redirect(locationHeader);\n        requestState.headers.forEach((value, key) => {\n          if (key === constants.Headers.Location) {\n            return;\n          }\n          res.headers.append(key, value);\n        });\n        return res;\n      } else if (requestState.status === AuthStatus.Handshake) {\n        throw new Error('Clerk: handshake status without redirect');\n      }\n\n      const authObject = requestState.toAuth();\n      logger.debug('auth', () => ({ auth: authObject, debug: authObject.debug() }));\n\n      const redirectToSignIn = createMiddlewareRedirectToSignIn(clerkRequest);\n      const redirectToSignUp = createMiddlewareRedirectToSignUp(clerkRequest);\n      const protect = await createMiddlewareProtect(clerkRequest, authObject, redirectToSignIn);\n\n      const authHandler = createMiddlewareAuthHandler(requestState, redirectToSignIn, redirectToSignUp);\n      authHandler.protect = protect;\n\n      let handlerResult: Response = NextResponse.next();\n      try {\n        const userHandlerResult = await clerkMiddlewareRequestDataStorage.run(\n          clerkMiddlewareRequestDataStore,\n          async () => handler?.(authHandler, request, event),\n        );\n        handlerResult = userHandlerResult || handlerResult;\n      } catch (e: any) {\n        handlerResult = handleControlFlowErrors(e, clerkRequest, request, requestState);\n      }\n      if (options.contentSecurityPolicy) {\n        const { headers } = createContentSecurityPolicyHeaders(\n          (parsePublishableKey(publishableKey)?.frontendApi ?? '').replace('$', ''),\n          options.contentSecurityPolicy,\n        );\n\n        headers.forEach(([key, value]) => {\n          setHeader(handlerResult, key, value);\n        });\n\n        logger.debug('Clerk generated CSP', () => ({\n          headers,\n        }));\n      }\n\n      // TODO @nikos: we need to make this more generic\n      // and move the logic in clerk/backend\n      if (requestState.headers) {\n        requestState.headers.forEach((value, key) => {\n          if (key === constants.Headers.ContentSecurityPolicy) {\n            logger.debug('Content-Security-Policy detected', () => ({\n              value,\n            }));\n          }\n          handlerResult.headers.append(key, value);\n        });\n      }\n\n      if (isRedirect(handlerResult)) {\n        logger.debug('handlerResult is redirect');\n        return serverRedirectWithAuth(clerkRequest, handlerResult, options);\n      }\n\n      if (options.debug) {\n        setRequestHeadersOnNextResponse(handlerResult, clerkRequest, { [constants.Headers.EnableDebug]: 'true' });\n      }\n\n      const keylessKeysForRequestData =\n        // Only pass keyless credentials when there are no explicit keys\n        secretKey === keyless?.secretKey\n          ? {\n              publishableKey: keyless?.publishableKey,\n              secretKey: keyless?.secretKey,\n            }\n          : {};\n\n      decorateRequest(\n        clerkRequest,\n        handlerResult,\n        requestState,\n        resolvedParams,\n        keylessKeysForRequestData,\n        authObject.tokenType === 'session_token' ? null : makeAuthObjectSerializable(authObject),\n      );\n\n      return handlerResult;\n    });\n\n    const keylessMiddleware: NextMiddleware = async (request, event) => {\n      /**\n       * This mechanism replaces a full-page reload. Ensures that middleware will re-run and authenticate the request properly without the secret key or publishable key to be missing.\n       */\n      if (isKeylessSyncRequest(request)) {\n        return returnBackFromKeylessSync(request);\n      }\n\n      const resolvedParams = typeof params === 'function' ? await params(request) : params;\n      const keyless = await getKeylessCookieValue(name => request.cookies.get(name)?.value);\n\n      const isMissingPublishableKey = !(resolvedParams.publishableKey || PUBLISHABLE_KEY || keyless?.publishableKey);\n      const authHeader = getHeader(request, constants.Headers.Authorization)?.replace('Bearer ', '') ?? '';\n\n      /**\n       * In keyless mode, if the publishable key is missing, let the request through, to render `<ClerkProvider/>` that will resume the flow gracefully.\n       */\n      if (isMissingPublishableKey && !isMachineTokenByPrefix(authHeader)) {\n        const res = NextResponse.next();\n        setRequestHeadersOnNextResponse(res, request, {\n          [constants.Headers.AuthStatus]: 'signed-out',\n        });\n        return res;\n      }\n\n      return baseNextMiddleware(request, event);\n    };\n\n    const nextMiddleware: NextMiddleware = async (request, event) => {\n      if (canUseKeyless) {\n        return keylessMiddleware(request, event);\n      }\n\n      return baseNextMiddleware(request, event);\n    };\n\n    // If we have a request and event, we're being called as a middleware directly\n    // eg, export default clerkMiddleware;\n    if (request && event) {\n      return nextMiddleware(request, event);\n    }\n\n    // Otherwise, return a middleware that can be called with a request and event\n    // eg, export default clerkMiddleware(auth => { ... });\n    return nextMiddleware;\n  });\n\n  return middleware;\n}) as ClerkMiddleware;\n\nconst parseRequestAndEvent = (args: unknown[]) => {\n  return [args[0] instanceof Request ? args[0] : undefined, args[0] instanceof Request ? args[1] : undefined] as [\n    NextMiddlewareRequestParam | undefined,\n    NextMiddlewareEvtParam | undefined,\n  ];\n};\n\nconst parseHandlerAndOptions = (args: unknown[]) => {\n  return [\n    typeof args[0] === 'function' ? args[0] : undefined,\n    (args.length === 2 ? args[1] : typeof args[0] === 'function' ? {} : args[0]) || {},\n  ] as [ClerkMiddlewareHandler | undefined, ClerkMiddlewareOptions | ClerkMiddlewareOptionsCallback];\n};\n\nconst isKeylessSyncRequest = (request: NextMiddlewareRequestParam) =>\n  request.nextUrl.pathname === '/clerk-sync-keyless';\n\nconst returnBackFromKeylessSync = (request: NextMiddlewareRequestParam) => {\n  const returnUrl = request.nextUrl.searchParams.get('returnUrl');\n  const url = new URL(request.url);\n  url.pathname = '';\n\n  return NextResponse.redirect(returnUrl || url.toString());\n};\n\ntype AuthenticateRequest = Pick<ClerkClient, 'authenticateRequest'>['authenticateRequest'];\n\nexport const createAuthenticateRequestOptions = (\n  clerkRequest: ClerkRequest,\n  options: ClerkMiddlewareOptions,\n): Parameters<AuthenticateRequest>[1] => {\n  return {\n    ...options,\n    ...handleMultiDomainAndProxy(clerkRequest, options),\n    // TODO: Leaving the acceptsToken as 'any' opens up the possibility of\n    // an economic attack. We should revisit this and only verify a token\n    // when auth() or auth.protect() is invoked.\n    acceptsToken: 'any',\n  };\n};\n\nconst createMiddlewareRedirectToSignIn = (\n  clerkRequest: ClerkRequest,\n): ClerkMiddlewareSessionAuthObject['redirectToSignIn'] => {\n  return (opts = {}) => {\n    const url = clerkRequest.clerkUrl.toString();\n    redirectToSignInError(url, opts.returnBackUrl);\n  };\n};\n\nconst createMiddlewareRedirectToSignUp = (\n  clerkRequest: ClerkRequest,\n): ClerkMiddlewareSessionAuthObject['redirectToSignUp'] => {\n  return (opts = {}) => {\n    const url = clerkRequest.clerkUrl.toString();\n    redirectToSignUpError(url, opts.returnBackUrl);\n  };\n};\n\nconst createMiddlewareProtect = (\n  clerkRequest: ClerkRequest,\n  rawAuthObject: AuthObject,\n  redirectToSignIn: RedirectFun<Response>,\n) => {\n  return (async (params: any, options: any) => {\n    const notFound = () => nextjsNotFound();\n\n    const redirect = (url: string) =>\n      nextjsRedirectError(url, {\n        redirectUrl: url,\n      });\n\n    const requestedToken = params?.token || options?.token || TokenType.SessionToken;\n    const authObject = getAuthObjectForAcceptedToken({ authObject: rawAuthObject, acceptsToken: requestedToken });\n\n    return createProtect({\n      request: clerkRequest,\n      redirect,\n      notFound,\n      unauthorized,\n      authObject,\n      redirectToSignIn,\n    })(params, options);\n  }) as unknown as Promise<AuthProtect>;\n};\n\n/**\n * Modifies the auth object based on the token type.\n * - For session tokens: adds redirect functions to the auth object\n * - For machine tokens: validates token type and returns appropriate auth object\n */\nconst createMiddlewareAuthHandler = (\n  requestState: AuthenticatedState<'session_token'> | UnauthenticatedState<'session_token'>,\n  redirectToSignIn: RedirectFun<Response>,\n  redirectToSignUp: RedirectFun<Response>,\n): ClerkMiddlewareAuth => {\n  const authHandler = async (options?: GetAuthOptions) => {\n    const rawAuthObject = requestState.toAuth({ treatPendingAsSignedOut: options?.treatPendingAsSignedOut });\n    const acceptsToken = options?.acceptsToken ?? TokenType.SessionToken;\n\n    const authObject = getAuthObjectForAcceptedToken({\n      authObject: rawAuthObject,\n      acceptsToken,\n    });\n\n    if (authObject.tokenType === TokenType.SessionToken && isTokenTypeAccepted(TokenType.SessionToken, acceptsToken)) {\n      return Object.assign(authObject, {\n        redirectToSignIn,\n        redirectToSignUp,\n      });\n    }\n\n    return authObject;\n  };\n\n  return authHandler as ClerkMiddlewareAuth;\n};\n\n// Handle errors thrown by protect() and redirectToSignIn() calls,\n// as we want to align the APIs between middleware, pages and route handlers\n// Normally, middleware requires to explicitly return a response, but we want to\n// avoid discrepancies between the APIs as it's easy to miss the `return` statement\n// especially when copy-pasting code from one place to another.\n// This function handles the known errors thrown by the APIs described above,\n// and returns the appropriate response.\nconst handleControlFlowErrors = (\n  e: any,\n  clerkRequest: ClerkRequest,\n  nextRequest: NextRequest,\n  requestState: RequestState,\n): Response => {\n  if (isNextjsUnauthorizedError(e)) {\n    const response = new NextResponse(null, { status: 401 });\n\n    // RequestState.toAuth() returns a session_token type by default.\n    // We need to cast it to the correct type to check for OAuth tokens.\n    const authObject = (requestState as RequestState<TokenType>).toAuth();\n    if (authObject && authObject.tokenType === TokenType.OAuthToken) {\n      // Following MCP spec, we return WWW-Authenticate header on 401 responses\n      // to enable OAuth 2.0 authorization server discovery (RFC9728).\n      // See https://modelcontextprotocol.io/specification/draft/basic/authorization#2-3-1-authorization-server-location\n      const publishableKey = parsePublishableKey(requestState.publishableKey);\n      return setHeader(\n        response,\n        'WWW-Authenticate',\n        `Bearer resource_metadata=\"https://${publishableKey?.frontendApi}/.well-known/oauth-protected-resource\"`,\n      );\n    }\n\n    return response;\n  }\n\n  if (isNextjsNotFoundError(e)) {\n    // Rewrite to a bogus URL to force not found error\n    return setHeader(\n      // This is an internal rewrite purely to trigger a not found error. We do not want Next.js to think that the\n      // destination URL is a valid page, so we use `nextRequest.url` as the base for the fake URL, which Next.js\n      // understands is an internal URL and won't run middleware against the request.\n      NextResponse.rewrite(new URL(`/clerk_${Date.now()}`, nextRequest.url)),\n      constants.Headers.AuthReason,\n      'protect-rewrite',\n    );\n  }\n\n  const isRedirectToSignIn = isRedirectToSignInError(e);\n  const isRedirectToSignUp = isRedirectToSignUpError(e);\n\n  if (isRedirectToSignIn || isRedirectToSignUp) {\n    const redirect = createRedirect({\n      redirectAdapter,\n      baseUrl: clerkRequest.clerkUrl,\n      signInUrl: requestState.signInUrl,\n      signUpUrl: requestState.signUpUrl,\n      publishableKey: requestState.publishableKey,\n      sessionStatus: requestState.toAuth()?.sessionStatus,\n    });\n\n    const { returnBackUrl } = e;\n    return redirect[isRedirectToSignIn ? 'redirectToSignIn' : 'redirectToSignUp']({ returnBackUrl });\n  }\n\n  if (isNextjsRedirectError(e)) {\n    return redirectAdapter(e.redirectUrl);\n  }\n\n  throw e;\n};\n"], "mappings": ";AAWA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,2BAA2B;AACpC,SAAS,YAAY,sBAAsB;AAE3C,SAAS,oBAAoB;AAI7B,SAAS,YAAY,wBAAwB,iBAAiB;AAC9D,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,mBAAmB;AAC5B,SAAS,iBAAiB,YAAY,aAAa,mBAAmB;AACtE,SAA4C,0CAA0C;AACtF,SAAS,oBAAoB;AAC7B,SAAS,iBAAiB;AAC1B,SAAS,6BAA6B;AACtC,SAAS,mCAAmC,uCAAuC;AACnF;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,SAAS,qBAAqB;AAE9B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAyEA,MAAM,kBAAmB,IAAI,SAA2D;AAC7F,QAAM,CAAC,SAAS,KAAK,IAAI,qBAAqB,IAAI;AAClD,QAAM,CAAC,SAAS,MAAM,IAAI,uBAAuB,IAAI;AAErD,QAAM,aAAa,kCAAkC,IAAI,iCAAiC,MAAM;AAC9F,UAAM,qBAAqC,WAAW,mBAAmB,YAAU,OAAOA,UAASC,WAAU;AAzIjH;AA2IM,YAAM,iBAAiB,OAAO,WAAW,aAAa,MAAM,OAAOD,QAAO,IAAI;AAE9E,YAAM,UAAU,MAAM,sBAAsB,UAAK;AA7IvD,YAAAE;AA6I0D,gBAAAA,MAAAF,SAAQ,QAAQ,IAAI,IAAI,MAAxB,gBAAAE,IAA2B;AAAA,OAAK;AAEpF,YAAM,iBAAiB;AAAA,QACrB,eAAe,kBAAkB,oBAAmB,mCAAS;AAAA,QAC7D,MAAM,aAAa,gCAAgC;AAAA,MACrD;AAEA,YAAM,YAAY;AAAA,QAAU,eAAe,aAAa,eAAc,mCAAS;AAAA,QAAW,MACxF,aAAa,2BAA2B;AAAA,MAC1C;AACA,YAAM,YAAY,eAAe,aAAa;AAC9C,YAAM,YAAY,eAAe,aAAa;AAE9C,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACL;AAGA,sCAAgC,IAAI,eAAe,OAAO;AAC1D,YAAM,sBAAsB,MAAM,YAAY;AAE9C,UAAI,QAAQ,OAAO;AACjB,eAAO,OAAO;AAAA,MAChB;AACA,YAAM,eAAe,mBAAmBF,QAAO;AAC/C,aAAO,MAAM,WAAW,OAAO;AAC/B,aAAO,MAAM,OAAO,MAAM,aAAa,OAAO,CAAC;AAE/C,YAAM,aAAaA,SAAQ,QAAQ,IAAI,UAAU,QAAQ,aAAa;AACtE,UAAI,cAAc,WAAW,WAAW,QAAQ,GAAG;AACjD,eAAO,MAAM,qBAAqB;AAAA,MACpC;AAEA,YAAM,YAAYA,SAAQ,QAAQ,IAAI,UAAU,QAAQ,qBAAqB;AAC7E,UAAI,WAAW;AACb,eAAO,MAAM,oCAAoC,OAAO;AAAA,UACtD,OAAO;AAAA,QACT,EAAE;AAAA,MACJ;AAEA,YAAM,eAAe,MAAM,oBAAoB;AAAA,QAC7C;AAAA,QACA,iCAAiC,cAAc,OAAO;AAAA,MACxD;AAEA,aAAO,MAAM,gBAAgB,OAAO;AAAA,QAClC,QAAQ,aAAa;AAAA;AAAA,QAErB,SAAS,KAAK,UAAU,OAAO,YAAY,aAAa,OAAO,CAAC;AAAA,QAChE,QAAQ,aAAa;AAAA,MACvB,EAAE;AAEF,YAAM,iBAAiB,aAAa,QAAQ,IAAI,UAAU,QAAQ,QAAQ;AAC1E,UAAI,gBAAgB;AAClB,cAAM,MAAM,aAAa,SAAS,cAAc;AAChD,qBAAa,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AAC3C,cAAI,QAAQ,UAAU,QAAQ,UAAU;AACtC;AAAA,UACF;AACA,cAAI,QAAQ,OAAO,KAAK,KAAK;AAAA,QAC/B,CAAC;AACD,eAAO;AAAA,MACT,WAAW,aAAa,WAAW,WAAW,WAAW;AACvD,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,YAAM,aAAa,aAAa,OAAO;AACvC,aAAO,MAAM,QAAQ,OAAO,EAAE,MAAM,YAAY,OAAO,WAAW,MAAM,EAAE,EAAE;AAE5E,YAAM,mBAAmB,iCAAiC,YAAY;AACtE,YAAM,mBAAmB,iCAAiC,YAAY;AACtE,YAAM,UAAU,MAAM,wBAAwB,cAAc,YAAY,gBAAgB;AAExF,YAAM,cAAc,4BAA4B,cAAc,kBAAkB,gBAAgB;AAChG,kBAAY,UAAU;AAEtB,UAAI,gBAA0B,aAAa,KAAK;AAChD,UAAI;AACF,cAAM,oBAAoB,MAAM,kCAAkC;AAAA,UAChE;AAAA,UACA,YAAY,mCAAU,aAAaA,UAASC;AAAA,QAC9C;AACA,wBAAgB,qBAAqB;AAAA,MACvC,SAAS,GAAQ;AACf,wBAAgB,wBAAwB,GAAG,cAAcD,UAAS,YAAY;AAAA,MAChF;AACA,UAAI,QAAQ,uBAAuB;AACjC,cAAM,EAAE,QAAQ,IAAI;AAAA,YACjB,+BAAoB,cAAc,MAAlC,mBAAqC,gBAArC,YAAoD,IAAI,QAAQ,KAAK,EAAE;AAAA,UACxE,QAAQ;AAAA,QACV;AAEA,gBAAQ,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAChC,oBAAU,eAAe,KAAK,KAAK;AAAA,QACrC,CAAC;AAED,eAAO,MAAM,uBAAuB,OAAO;AAAA,UACzC;AAAA,QACF,EAAE;AAAA,MACJ;AAIA,UAAI,aAAa,SAAS;AACxB,qBAAa,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AAC3C,cAAI,QAAQ,UAAU,QAAQ,uBAAuB;AACnD,mBAAO,MAAM,oCAAoC,OAAO;AAAA,cACtD;AAAA,YACF,EAAE;AAAA,UACJ;AACA,wBAAc,QAAQ,OAAO,KAAK,KAAK;AAAA,QACzC,CAAC;AAAA,MACH;AAEA,UAAI,WAAW,aAAa,GAAG;AAC7B,eAAO,MAAM,2BAA2B;AACxC,eAAO,uBAAuB,cAAc,eAAe,OAAO;AAAA,MACpE;AAEA,UAAI,QAAQ,OAAO;AACjB,wCAAgC,eAAe,cAAc,EAAE,CAAC,UAAU,QAAQ,WAAW,GAAG,OAAO,CAAC;AAAA,MAC1G;AAEA,YAAM;AAAA;AAAA,QAEJ,eAAc,mCAAS,aACnB;AAAA,UACE,gBAAgB,mCAAS;AAAA,UACzB,WAAW,mCAAS;AAAA,QACtB,IACA,CAAC;AAAA;AAEP;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,cAAc,kBAAkB,OAAO,2BAA2B,UAAU;AAAA,MACzF;AAEA,aAAO;AAAA,IACT,CAAC;AAED,UAAM,oBAAoC,OAAOA,UAASC,WAAU;AAjSxE;AAqSM,UAAI,qBAAqBD,QAAO,GAAG;AACjC,eAAO,0BAA0BA,QAAO;AAAA,MAC1C;AAEA,YAAM,iBAAiB,OAAO,WAAW,aAAa,MAAM,OAAOA,QAAO,IAAI;AAC9E,YAAM,UAAU,MAAM,sBAAsB,UAAK;AA1SvD,YAAAE;AA0S0D,gBAAAA,MAAAF,SAAQ,QAAQ,IAAI,IAAI,MAAxB,gBAAAE,IAA2B;AAAA,OAAK;AAEpF,YAAM,0BAA0B,EAAE,eAAe,kBAAkB,oBAAmB,mCAAS;AAC/F,YAAM,cAAa,qBAAUF,UAAS,UAAU,QAAQ,aAAa,MAAlD,mBAAqD,QAAQ,WAAW,QAAxE,YAA+E;AAKlG,UAAI,2BAA2B,CAAC,uBAAuB,UAAU,GAAG;AAClE,cAAM,MAAM,aAAa,KAAK;AAC9B,wCAAgC,KAAKA,UAAS;AAAA,UAC5C,CAAC,UAAU,QAAQ,UAAU,GAAG;AAAA,QAClC,CAAC;AACD,eAAO;AAAA,MACT;AAEA,aAAO,mBAAmBA,UAASC,MAAK;AAAA,IAC1C;AAEA,UAAM,iBAAiC,OAAOD,UAASC,WAAU;AAC/D,UAAI,eAAe;AACjB,eAAO,kBAAkBD,UAASC,MAAK;AAAA,MACzC;AAEA,aAAO,mBAAmBD,UAASC,MAAK;AAAA,IAC1C;AAIA,QAAI,WAAW,OAAO;AACpB,aAAO,eAAe,SAAS,KAAK;AAAA,IACtC;AAIA,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;AAEA,MAAM,uBAAuB,CAAC,SAAoB;AAChD,SAAO,CAAC,KAAK,CAAC,aAAa,UAAU,KAAK,CAAC,IAAI,QAAW,KAAK,CAAC,aAAa,UAAU,KAAK,CAAC,IAAI,MAAS;AAI5G;AAEA,MAAM,yBAAyB,CAAC,SAAoB;AAClD,SAAO;AAAA,IACL,OAAO,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,IAAI;AAAA,KACzC,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,MAAM,aAAa,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;AAAA,EACnF;AACF;AAEA,MAAM,uBAAuB,CAAC,YAC5B,QAAQ,QAAQ,aAAa;AAE/B,MAAM,4BAA4B,CAAC,YAAwC;AACzE,QAAM,YAAY,QAAQ,QAAQ,aAAa,IAAI,WAAW;AAC9D,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,MAAI,WAAW;AAEf,SAAO,aAAa,SAAS,aAAa,IAAI,SAAS,CAAC;AAC1D;AAIO,MAAM,mCAAmC,CAC9C,cACA,YACuC;AACvC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG,0BAA0B,cAAc,OAAO;AAAA;AAAA;AAAA;AAAA,IAIlD,cAAc;AAAA,EAChB;AACF;AAEA,MAAM,mCAAmC,CACvC,iBACyD;AACzD,SAAO,CAAC,OAAO,CAAC,MAAM;AACpB,UAAM,MAAM,aAAa,SAAS,SAAS;AAC3C,0BAAsB,KAAK,KAAK,aAAa;AAAA,EAC/C;AACF;AAEA,MAAM,mCAAmC,CACvC,iBACyD;AACzD,SAAO,CAAC,OAAO,CAAC,MAAM;AACpB,UAAM,MAAM,aAAa,SAAS,SAAS;AAC3C,0BAAsB,KAAK,KAAK,aAAa;AAAA,EAC/C;AACF;AAEA,MAAM,0BAA0B,CAC9B,cACA,eACA,qBACG;AACH,SAAQ,OAAO,QAAa,YAAiB;AAC3C,UAAM,WAAW,MAAM,eAAe;AAEtC,UAAM,WAAW,CAAC,QAChB,oBAAoB,KAAK;AAAA,MACvB,aAAa;AAAA,IACf,CAAC;AAEH,UAAM,kBAAiB,iCAAQ,WAAS,mCAAS,UAAS,UAAU;AACpE,UAAM,aAAa,8BAA8B,EAAE,YAAY,eAAe,cAAc,eAAe,CAAC;AAE5G,WAAO,cAAc;AAAA,MACnB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,QAAQ,OAAO;AAAA,EACpB;AACF;AAOA,MAAM,8BAA8B,CAClC,cACA,kBACA,qBACwB;AACxB,QAAM,cAAc,OAAO,YAA6B;AAnb1D;AAobI,UAAM,gBAAgB,aAAa,OAAO,EAAE,yBAAyB,mCAAS,wBAAwB,CAAC;AACvG,UAAM,gBAAe,wCAAS,iBAAT,YAAyB,UAAU;AAExD,UAAM,aAAa,8BAA8B;AAAA,MAC/C,YAAY;AAAA,MACZ;AAAA,IACF,CAAC;AAED,QAAI,WAAW,cAAc,UAAU,gBAAgB,oBAAoB,UAAU,cAAc,YAAY,GAAG;AAChH,aAAO,OAAO,OAAO,YAAY;AAAA,QAC/B;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASA,MAAM,0BAA0B,CAC9B,GACA,cACA,aACA,iBACa;AArdf;AAsdE,MAAI,0BAA0B,CAAC,GAAG;AAChC,UAAM,WAAW,IAAI,aAAa,MAAM,EAAE,QAAQ,IAAI,CAAC;AAIvD,UAAM,aAAc,aAAyC,OAAO;AACpE,QAAI,cAAc,WAAW,cAAc,UAAU,YAAY;AAI/D,YAAM,iBAAiB,oBAAoB,aAAa,cAAc;AACtE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,qCAAqC,iDAAgB,WAAW;AAAA,MAClE;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,sBAAsB,CAAC,GAAG;AAE5B,WAAO;AAAA;AAAA;AAAA;AAAA,MAIL,aAAa,QAAQ,IAAI,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,YAAY,GAAG,CAAC;AAAA,MACrE,UAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAEA,QAAM,qBAAqB,wBAAwB,CAAC;AACpD,QAAM,qBAAqB,wBAAwB,CAAC;AAEpD,MAAI,sBAAsB,oBAAoB;AAC5C,UAAM,WAAW,eAAe;AAAA,MAC9B;AAAA,MACA,SAAS,aAAa;AAAA,MACtB,WAAW,aAAa;AAAA,MACxB,WAAW,aAAa;AAAA,MACxB,gBAAgB,aAAa;AAAA,MAC7B,gBAAe,kBAAa,OAAO,MAApB,mBAAuB;AAAA,IACxC,CAAC;AAED,UAAM,EAAE,cAAc,IAAI;AAC1B,WAAO,SAAS,qBAAqB,qBAAqB,kBAAkB,EAAE,EAAE,cAAc,CAAC;AAAA,EACjG;AAEA,MAAI,sBAAsB,CAAC,GAAG;AAC5B,WAAO,gBAAgB,EAAE,WAAW;AAAA,EACtC;AAEA,QAAM;AACR;", "names": ["request", "event", "_a"]}