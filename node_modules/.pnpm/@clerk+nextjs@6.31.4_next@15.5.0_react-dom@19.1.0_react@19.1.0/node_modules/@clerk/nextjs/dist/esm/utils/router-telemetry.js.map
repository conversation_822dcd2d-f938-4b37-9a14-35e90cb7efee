{"version": 3, "sources": ["../../../src/utils/router-telemetry.ts"], "sourcesContent": ["import { eventFrameworkMetadata } from '@clerk/shared/telemetry';\n\nimport { useClerk } from '../client-boundary/hooks';\nimport { usePagesRouter } from '../client-boundary/hooks/usePagesRouter';\n\nconst RouterTelemetry = () => {\n  const clerk = useClerk();\n  const { pagesRouter } = usePagesRouter();\n\n  /**\n   * Caching and throttling is handled internally it's safe to execute on every navigation.\n   */\n  clerk.telemetry?.record(\n    eventFrameworkMetadata({\n      router: pagesRouter ? 'pages' : 'app',\n      ...(globalThis?.next?.version ? { nextjsVersion: globalThis.next.version } : {}),\n    }),\n  );\n\n  return null;\n};\n\nexport { RouterTelemetry };\n"], "mappings": ";AAAA,SAAS,8BAA8B;AAEvC,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;AAE/B,MAAM,kBAAkB,MAAM;AAL9B;AAME,QAAM,QAAQ,SAAS;AACvB,QAAM,EAAE,YAAY,IAAI,eAAe;AAKvC,cAAM,cAAN,mBAAiB;AAAA,IACf,uBAAuB;AAAA,MACrB,QAAQ,cAAc,UAAU;AAAA,MAChC,KAAI,8CAAY,SAAZ,mBAAkB,WAAU,EAAE,eAAe,WAAW,KAAK,QAAQ,IAAI,CAAC;AAAA,IAChF,CAAC;AAAA;AAGH,SAAO;AACT;", "names": []}