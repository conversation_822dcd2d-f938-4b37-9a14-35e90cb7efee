{"version": 3, "sources": ["../../../src/server/buildClerkProps.ts"], "sourcesContent": ["import type { AuthObject, Organization, Session, User } from '@clerk/backend';\nimport { makeAuthObjectSerializable, stripPrivateDataFromObject } from '@clerk/backend/internal';\n\nimport { getAuthDataFromRequest } from './data/getAuthDataFromRequest';\nimport type { RequestLike } from './types';\n\ntype BuildClerkPropsInitState = { user?: User | null; session?: Session | null; organization?: Organization | null };\n\ntype BuildClerkProps = (req: RequestLike, authState?: BuildClerkPropsInitState) => Record<string, unknown>;\n\n/**\n * Clerk uses `buildClerkProps` to inform the client-side helpers of the authentication state of the user. This function is used SSR in the `getServerSideProps` function of your Next.js application.\n *\n * @example\n * ### Basic usage\n *\n * ```tsx {{ filename: 'pages/myServerSidePage.tsx' }}\n * import { getAuth, buildClerkProps } from '@clerk/nextjs/server'\n * import { GetServerSideProps } from 'next'\n *\n * export const getServerSideProps: GetServerSideProps = async (ctx) => {\n *  const { userId } = getAuth(ctx.req)\n *\n *  if (!userId) {\n *    // handle user is not signed in.\n *  }\n *\n *  // Load any data your application needs for the page using the userId\n *  return { props: { ...buildClerkProps(ctx.req) } }\n * }\n * ```\n *\n * @example\n * ### Usage with `clerkClient`\n *\n * The `clerkClient` allows you to access the Clerk API. You can use this to retrieve or update data.\n *\n * ```tsx {{ filename: 'pages/api/example.ts' }}\n * import { getAuth, buildClerkProps, clerkClient } from '@clerk/nextjs/server'\n * import { GetServerSideProps } from 'next'\n *\n * export const getServerSideProps: GetServerSideProps = async (ctx) => {\n *  const { userId } = getAuth(ctx.req)\n *\n *  const user = userId ? await clerkClient().users.getUser(userId) : undefined\n *\n *  return { props: { ...buildClerkProps(ctx.req, { user }) } }\n * }\n * ```\n */\nexport const buildClerkProps: BuildClerkProps = (req, initialState = {}) => {\n  const sanitizedAuthObject = getDynamicAuthData(req, initialState);\n\n  // Serializing the state on dev env is a temp workaround for the following issue:\n  // https://github.com/vercel/next.js/discussions/11209|Next.js\n  const __clerk_ssr_state =\n    process.env.NODE_ENV !== 'production' ? JSON.parse(JSON.stringify(sanitizedAuthObject)) : sanitizedAuthObject;\n  return { __clerk_ssr_state };\n};\n\nexport function getDynamicAuthData(req: RequestLike, initialState = {}) {\n  const authObject = getAuthDataFromRequest(req);\n\n  return makeAuthObjectSerializable(stripPrivateDataFromObject({ ...authObject, ...initialState })) as AuthObject;\n}\n"], "mappings": ";AACA,SAAS,4BAA4B,kCAAkC;AAEvE,SAAS,8BAA8B;AA+ChC,MAAM,kBAAmC,CAAC,KAAK,eAAe,CAAC,MAAM;AAC1E,QAAM,sBAAsB,mBAAmB,KAAK,YAAY;AAIhE,QAAM,oBACJ,QAAQ,IAAI,aAAa,eAAe,KAAK,MAAM,KAAK,UAAU,mBAAmB,CAAC,IAAI;AAC5F,SAAO,EAAE,kBAAkB;AAC7B;AAEO,SAAS,mBAAmB,KAAkB,eAAe,CAAC,GAAG;AACtE,QAAM,aAAa,uBAAuB,GAAG;AAE7C,SAAO,2BAA2B,2BAA2B,EAAE,GAAG,YAAY,GAAG,aAAa,CAAC,CAAC;AAClG;", "names": []}