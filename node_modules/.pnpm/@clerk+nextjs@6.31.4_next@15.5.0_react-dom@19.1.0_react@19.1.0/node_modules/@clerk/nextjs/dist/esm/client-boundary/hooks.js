"use client";
import "../chunk-BUSYA2B4.js";
import {
  useClerk,
  useEmailLink,
  useOrganization,
  useOrganizationList,
  useSession,
  useSessionList,
  useSignIn,
  useSignUp,
  useUser,
  useReverification
} from "@clerk/clerk-react";
import {
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isReverificationCancelledError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus
} from "@clerk/clerk-react/errors";
import { usePromisifiedAuth } from "./PromisifiedAuthProvider";
export {
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isReverificationCancelledError,
  usePromisifiedAuth as useAuth,
  useClerk,
  useEmailLink,
  useOrganization,
  useOrganizationList,
  useReverification,
  useSession,
  useSessionList,
  useSignIn,
  useSignUp,
  useUser
};
//# sourceMappingURL=hooks.js.map