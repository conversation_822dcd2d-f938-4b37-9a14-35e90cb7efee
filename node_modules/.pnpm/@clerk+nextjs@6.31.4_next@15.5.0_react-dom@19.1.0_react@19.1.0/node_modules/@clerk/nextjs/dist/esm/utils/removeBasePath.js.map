{"version": 3, "sources": ["../../../src/utils/removeBasePath.ts"], "sourcesContent": ["/**\n * Removes the Next.js basePath from the provided destination if set.\n * @param to Destination route to navigate to\n * @returns Destination without basePath, if set\n */\nexport function removeBasePath(to: string): string {\n  let destination = to;\n  const basePath = process.env.__NEXT_ROUTER_BASEPATH;\n  if (basePath && destination.startsWith(basePath)) {\n    destination = destination.slice(basePath.length);\n  }\n\n  return destination;\n}\n"], "mappings": ";AAKO,SAAS,eAAe,IAAoB;AACjD,MAAI,cAAc;AAClB,QAAM,WAAW,QAAQ,IAAI;AAC7B,MAAI,YAAY,YAAY,WAAW,QAAQ,GAAG;AAChD,kBAAc,YAAY,MAAM,SAAS,MAAM;AAAA,EACjD;AAEA,SAAO;AACT;", "names": []}