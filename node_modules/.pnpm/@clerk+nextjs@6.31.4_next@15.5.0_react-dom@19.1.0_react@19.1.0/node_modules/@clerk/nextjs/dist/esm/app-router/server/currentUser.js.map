{"version": 3, "sources": ["../../../../src/app-router/server/currentUser.ts"], "sourcesContent": ["import type { User } from '@clerk/backend';\n\nimport { clerkClient } from '../../server/clerkClient';\nimport { auth } from './auth';\n\n/**\n * The `currentUser` helper returns the [Backend User](https://clerk.com/docs/references/backend/types/backend-user) object of the currently active user. It can be used in Server Components, Route Handlers, and Server Actions.\n *\n * Under the hood, this helper:\n * - calls `fetch()`, so it is automatically deduped per request.\n * - uses the [`GET /v1/users/{user_id}`](https://clerk.com/docs/reference/backend-api/tag/Users#operation/GetUser) endpoint.\n * - counts towards the [Backend API request rate limit](https://clerk.com/docs/backend-requests/resources/rate-limits).\n *\n * @example\n * ```tsx {{ filename: 'app/page.tsx' }}\n * import { currentUser } from '@clerk/nextjs/server'\n *\n * export default async function Page() {\n *  const user = await currentUser()\n *\n *  if (!user) return <div>Not signed in</div>\n *\n *  return <div>Hello {user?.firstName}</div>\n * }\n * ```\n */\nexport async function currentUser(): Promise<User | null> {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  require('server-only');\n\n  const { userId } = await auth();\n  if (!userId) {\n    return null;\n  }\n\n  return (await clerkClient()).users.getUser(userId);\n}\n"], "mappings": ";AAEA,SAAS,mBAAmB;AAC5B,SAAS,YAAY;AAuBrB,eAAsB,cAAoC;AAExD,UAAQ,aAAa;AAErB,QAAM,EAAE,OAAO,IAAI,MAAM,KAAK;AAC9B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,UAAQ,MAAM,YAAY,GAAG,MAAM,QAAQ,MAAM;AACnD;", "names": []}