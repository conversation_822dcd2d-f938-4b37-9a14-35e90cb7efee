{"version": 3, "sources": ["../../../src/server/middleware-storage.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks';\n\nimport type { AuthenticateRequestOptions } from '@clerk/backend/internal';\n\nexport const clerkMiddlewareRequestDataStore = new Map<'requestData', AuthenticateRequestOptions>();\nexport const clerkMiddlewareRequestDataStorage = new AsyncLocalStorage<typeof clerkMiddlewareRequestDataStore>();\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAI3B,MAAM,kCAAkC,oBAAI,IAA+C;AAC3F,MAAM,oCAAoC,IAAI,kBAA0D;", "names": []}