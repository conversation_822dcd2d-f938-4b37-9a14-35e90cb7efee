{"version": 3, "sources": ["../../../../src/app-router/server/keyless-provider.tsx"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport type { Without } from '@clerk/types';\nimport { headers } from 'next/headers';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport { createClerkClientWithOptions } from '../../server/createClerkClient';\nimport { collectKeylessMetadata, formatMetadataHeaders } from '../../server/keyless-custom-headers';\nimport type { NextClerkProviderProps } from '../../types';\nimport { canUseKeyless } from '../../utils/feature-flags';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { onlyTry } from '../../utils/only-try';\nimport { ClientClerkProvider } from '../client/ClerkProvider';\nimport { deleteKeylessAction } from '../keyless-actions';\n\nexport async function getKeylessStatus(\n  params: Without<NextClerkProviderProps, '__unstable_invokeMiddlewareOnAuthStateChange'>,\n) {\n  let [shouldRunAsKeyless, runningWithClaimedKeys, locallyStoredPublishableKey] = [false, false, ''];\n  if (canUseKeyless) {\n    locallyStoredPublishableKey = await import('../../server/keyless-node.js')\n      .then(mod => mod.safeParseClerkFile()?.publishableKey || '')\n      .catch(() => '');\n\n    runningWithClaimedKeys = Boolean(params.publishableKey) && params.publishableKey === locallyStoredPublishableKey;\n    shouldRunAsKeyless = !params.publishableKey || runningWithClaimedKeys;\n  }\n\n  return {\n    shouldRunAsKeyless,\n    runningWithClaimedKeys,\n  };\n}\n\ntype KeylessProviderProps = PropsWithChildren<{\n  rest: Without<NextClerkProviderProps, '__unstable_invokeMiddlewareOnAuthStateChange'>;\n  runningWithClaimedKeys: boolean;\n  generateStatePromise: () => Promise<AuthObject | null>;\n  generateNonce: () => Promise<string>;\n}>;\n\nexport const KeylessProvider = async (props: KeylessProviderProps) => {\n  const { rest, runningWithClaimedKeys, generateNonce, generateStatePromise, children } = props;\n\n  // NOTE: Create or read keys on every render. Usually this means only on hard refresh or hard navigations.\n  const newOrReadKeys = await import('../../server/keyless-node.js')\n    .then(mod => mod.createOrReadKeyless())\n    .catch(() => null);\n\n  const { clerkDevelopmentCache, createConfirmationMessage, createKeylessModeMessage } = await import(\n    '../../server/keyless-log-cache.js'\n  );\n\n  if (!newOrReadKeys) {\n    // When case keyless should run, but keys are not available, then fallback to throwing for missing keys\n    return (\n      <ClientClerkProvider\n        {...mergeNextClerkPropsWithEnv(rest)}\n        nonce={await generateNonce()}\n        initialState={await generateStatePromise()}\n        disableKeyless\n      >\n        {children}\n      </ClientClerkProvider>\n    );\n  }\n\n  const clientProvider = (\n    <ClientClerkProvider\n      {...mergeNextClerkPropsWithEnv({\n        ...rest,\n        publishableKey: newOrReadKeys.publishableKey,\n        __internal_keyless_claimKeylessApplicationUrl: newOrReadKeys.claimUrl,\n        __internal_keyless_copyInstanceKeysUrl: newOrReadKeys.apiKeysUrl,\n        // Explicitly use `null` instead of `undefined` here to avoid persisting `deleteKeylessAction` during merging of options.\n        __internal_keyless_dismissPrompt: runningWithClaimedKeys ? deleteKeylessAction : null,\n      })}\n      nonce={await generateNonce()}\n      initialState={await generateStatePromise()}\n    >\n      {children}\n    </ClientClerkProvider>\n  );\n\n  if (runningWithClaimedKeys) {\n    try {\n      const secretKey = await import('../../server/keyless-node.js').then(mod => mod.safeParseClerkFile()?.secretKey);\n      if (!secretKey) {\n        // we will ignore it later\n        throw new Error('Missing secret key from `.clerk/`');\n      }\n      const client = createClerkClientWithOptions({\n        secretKey,\n      });\n\n      // Collect metadata\n      const keylessHeaders = await collectKeylessMetadata()\n        .then(formatMetadataHeaders)\n        .catch(() => new Headers());\n\n      /**\n       * Notifying the dashboard the should runs once. We are controlling this behaviour by caching the result of the request.\n       * If the request fails, it will be considered stale after 10 minutes, otherwise it is cached for 24 hours.\n       */\n      await clerkDevelopmentCache?.run(\n        () =>\n          client.__experimental_accountlessApplications.completeAccountlessApplicationOnboarding({\n            requestHeaders: keylessHeaders,\n          }),\n        {\n          cacheKey: `${newOrReadKeys.publishableKey}_complete`,\n          onSuccessStale: 24 * 60 * 60 * 1000, // 24 hours\n        },\n      );\n    } catch {\n      // noop\n    }\n\n    /**\n     * Notify developers.\n     */\n    clerkDevelopmentCache?.log({\n      cacheKey: `${newOrReadKeys.publishableKey}_claimed`,\n      msg: createConfirmationMessage(),\n    });\n\n    return clientProvider;\n  }\n\n  const KeylessCookieSync = await import('../client/keyless-cookie-sync.js').then(mod => mod.KeylessCookieSync);\n\n  const headerStore = await headers();\n  /**\n   * Allow developer to return to local application after claiming\n   */\n  const host = headerStore.get('x-forwarded-host');\n  const proto = headerStore.get('x-forwarded-proto');\n\n  const claimUrl = new URL(newOrReadKeys.claimUrl);\n  if (host && proto) {\n    onlyTry(() => claimUrl.searchParams.set('return_url', new URL(`${proto}://${host}`).href));\n  }\n\n  /**\n   * Notify developers.\n   */\n  clerkDevelopmentCache?.log({\n    cacheKey: newOrReadKeys.publishableKey,\n    msg: createKeylessModeMessage({ ...newOrReadKeys, claimUrl: claimUrl.href }),\n  });\n\n  return <KeylessCookieSync {...newOrReadKeys}>{clientProvider}</KeylessCookieSync>;\n};\n"], "mappings": ";AAEA,SAAS,eAAe;AAExB,OAAO,WAAW;AAElB,SAAS,oCAAoC;AAC7C,SAAS,wBAAwB,6BAA6B;AAE9D,SAAS,qBAAqB;AAC9B,SAAS,kCAAkC;AAC3C,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,2BAA2B;AAEpC,eAAsB,iBACpB,QACA;AACA,MAAI,CAAC,oBAAoB,wBAAwB,2BAA2B,IAAI,CAAC,OAAO,OAAO,EAAE;AACjG,MAAI,eAAe;AACjB,kCAA8B,MAAM,OAAO,8BAA8B,EACtE,KAAK,SAAI;AArBhB;AAqBmB,wBAAI,mBAAmB,MAAvB,mBAA0B,mBAAkB;AAAA,KAAE,EAC1D,MAAM,MAAM,EAAE;AAEjB,6BAAyB,QAAQ,OAAO,cAAc,KAAK,OAAO,mBAAmB;AACrF,yBAAqB,CAAC,OAAO,kBAAkB;AAAA,EACjD;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AASO,MAAM,kBAAkB,OAAO,UAAgC;AACpE,QAAM,EAAE,MAAM,wBAAwB,eAAe,sBAAsB,SAAS,IAAI;AAGxF,QAAM,gBAAgB,MAAM,OAAO,8BAA8B,EAC9D,KAAK,SAAO,IAAI,oBAAoB,CAAC,EACrC,MAAM,MAAM,IAAI;AAEnB,QAAM,EAAE,uBAAuB,2BAA2B,yBAAyB,IAAI,MAAM,OAC3F,mCACF;AAEA,MAAI,CAAC,eAAe;AAElB,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG,2BAA2B,IAAI;AAAA,QACnC,OAAO,MAAM,cAAc;AAAA,QAC3B,cAAc,MAAM,qBAAqB;AAAA,QACzC,gBAAc;AAAA;AAAA,MAEb;AAAA,IACH;AAAA,EAEJ;AAEA,QAAM,iBACJ;AAAA,IAAC;AAAA;AAAA,MACE,GAAG,2BAA2B;AAAA,QAC7B,GAAG;AAAA,QACH,gBAAgB,cAAc;AAAA,QAC9B,+CAA+C,cAAc;AAAA,QAC7D,wCAAwC,cAAc;AAAA;AAAA,QAEtD,kCAAkC,yBAAyB,sBAAsB;AAAA,MACnF,CAAC;AAAA,MACD,OAAO,MAAM,cAAc;AAAA,MAC3B,cAAc,MAAM,qBAAqB;AAAA;AAAA,IAExC;AAAA,EACH;AAGF,MAAI,wBAAwB;AAC1B,QAAI;AACF,YAAM,YAAY,MAAM,OAAO,8BAA8B,EAAE,KAAK,SAAI;AAtF9E;AAsFiF,yBAAI,mBAAmB,MAAvB,mBAA0B;AAAA,OAAS;AAC9G,UAAI,CAAC,WAAW;AAEd,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AACA,YAAM,SAAS,6BAA6B;AAAA,QAC1C;AAAA,MACF,CAAC;AAGD,YAAM,iBAAiB,MAAM,uBAAuB,EACjD,KAAK,qBAAqB,EAC1B,MAAM,MAAM,IAAI,QAAQ,CAAC;AAM5B,aAAM,+DAAuB;AAAA,QAC3B,MACE,OAAO,uCAAuC,yCAAyC;AAAA,UACrF,gBAAgB;AAAA,QAClB,CAAC;AAAA,QACH;AAAA,UACE,UAAU,GAAG,cAAc,cAAc;AAAA,UACzC,gBAAgB,KAAK,KAAK,KAAK;AAAA;AAAA,QACjC;AAAA;AAAA,IAEJ,QAAQ;AAAA,IAER;AAKA,mEAAuB,IAAI;AAAA,MACzB,UAAU,GAAG,cAAc,cAAc;AAAA,MACzC,KAAK,0BAA0B;AAAA,IACjC;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,MAAM,OAAO,kCAAkC,EAAE,KAAK,SAAO,IAAI,iBAAiB;AAE5G,QAAM,cAAc,MAAM,QAAQ;AAIlC,QAAM,OAAO,YAAY,IAAI,kBAAkB;AAC/C,QAAM,QAAQ,YAAY,IAAI,mBAAmB;AAEjD,QAAM,WAAW,IAAI,IAAI,cAAc,QAAQ;AAC/C,MAAI,QAAQ,OAAO;AACjB,YAAQ,MAAM,SAAS,aAAa,IAAI,cAAc,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE,IAAI,CAAC;AAAA,EAC3F;AAKA,iEAAuB,IAAI;AAAA,IACzB,UAAU,cAAc;AAAA,IACxB,KAAK,yBAAyB,EAAE,GAAG,eAAe,UAAU,SAAS,KAAK,CAAC;AAAA,EAC7E;AAEA,SAAO,oCAAC,qBAAmB,GAAG,iBAAgB,cAAe;AAC/D;", "names": []}