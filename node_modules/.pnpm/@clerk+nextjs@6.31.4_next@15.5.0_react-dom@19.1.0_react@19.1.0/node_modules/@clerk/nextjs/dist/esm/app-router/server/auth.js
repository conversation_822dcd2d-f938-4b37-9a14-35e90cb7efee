import "../../chunk-BUSYA2B4.js";
import { constants, createClerkRequest, createRedirect, TokenType } from "@clerk/backend/internal";
import { notFound, redirect } from "next/navigation";
import { PUBLISHABLE_KEY, SIGN_IN_URL, SIGN_UP_URL } from "../../server/constants";
import { createAsyncGetAuth } from "../../server/createGetAuth";
import { authAuthHeaderMissing } from "../../server/errors";
import { getAuthKeyFromRequest, getHeader } from "../../server/headers-utils";
import { unauthorized } from "../../server/nextErrors";
import { createProtect } from "../../server/protect";
import { decryptClerkRequestData } from "../../server/utils";
import { isNextWithUnstableServerActions } from "../../utils/sdk-versions";
import { buildRequestLike } from "./utils";
const auth = async (options) => {
  var _a;
  require("server-only");
  const request = await buildRequestLike();
  const stepsBasedOnSrcDirectory = async () => {
    if (isNextWithUnstableServerActions) {
      return [];
    }
    try {
      const isSrcAppDir = await import("../../server/fs/middleware-location.js").then((m) => m.hasSrcAppDir());
      return [`Your Middleware exists at ./${isSrcAppDir ? "src/" : ""}middleware.(ts|js)`];
    } catch {
      return [];
    }
  };
  const authObject = await createAsyncGetAuth({
    debugLoggerName: "auth()",
    noAuthStatusMessage: authAuthHeaderMissing("auth", await stepsBasedOnSrcDirectory())
  })(request, {
    treatPendingAsSignedOut: options == null ? void 0 : options.treatPendingAsSignedOut,
    acceptsToken: (_a = options == null ? void 0 : options.acceptsToken) != null ? _a : TokenType.SessionToken
  });
  const clerkUrl = getAuthKeyFromRequest(request, "ClerkUrl");
  const createRedirectForRequest = (...args) => {
    const { returnBackUrl } = args[0] || {};
    const clerkRequest = createClerkRequest(request);
    const devBrowserToken = clerkRequest.clerkUrl.searchParams.get(constants.QueryParameters.DevBrowser) || clerkRequest.cookies.get(constants.Cookies.DevBrowser);
    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);
    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);
    return [
      createRedirect({
        redirectAdapter: redirect,
        devBrowserToken,
        baseUrl: clerkRequest.clerkUrl.toString(),
        publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,
        signInUrl: decryptedRequestData.signInUrl || SIGN_IN_URL,
        signUpUrl: decryptedRequestData.signUpUrl || SIGN_UP_URL,
        sessionStatus: authObject.tokenType === TokenType.SessionToken ? authObject.sessionStatus : null
      }),
      returnBackUrl === null ? "" : returnBackUrl || (clerkUrl == null ? void 0 : clerkUrl.toString())
    ];
  };
  const redirectToSignIn = (opts = {}) => {
    const [r, returnBackUrl] = createRedirectForRequest(opts);
    return r.redirectToSignIn({
      returnBackUrl
    });
  };
  const redirectToSignUp = (opts = {}) => {
    const [r, returnBackUrl] = createRedirectForRequest(opts);
    return r.redirectToSignUp({
      returnBackUrl
    });
  };
  if (authObject.tokenType === TokenType.SessionToken) {
    return Object.assign(authObject, { redirectToSignIn, redirectToSignUp });
  }
  return authObject;
};
auth.protect = async (...args) => {
  var _a, _b;
  require("server-only");
  const request = await buildRequestLike();
  const requestedToken = ((_a = args == null ? void 0 : args[0]) == null ? void 0 : _a.token) || ((_b = args == null ? void 0 : args[1]) == null ? void 0 : _b.token) || TokenType.SessionToken;
  const authObject = await auth({ acceptsToken: requestedToken });
  const protect = createProtect({
    request,
    authObject,
    redirectToSignIn: authObject.redirectToSignIn,
    notFound,
    redirect,
    unauthorized
  });
  return protect(...args);
};
export {
  auth
};
//# sourceMappingURL=auth.js.map