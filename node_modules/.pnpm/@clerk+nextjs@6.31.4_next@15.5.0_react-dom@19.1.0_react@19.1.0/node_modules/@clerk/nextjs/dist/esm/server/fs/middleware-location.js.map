{"version": 3, "sources": ["../../../../src/server/fs/middleware-location.ts"], "sourcesContent": ["import { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from './utils';\n\nfunction hasSrcAppDir() {\n  const { existsSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n\n  const projectWithAppSrc = path.join(cwd(), 'src', 'app');\n\n  return !!existsSync(projectWithAppSrc);\n}\n\nfunction suggestMiddlewareLocation() {\n  const fileExtensions = ['ts', 'js'] as const;\n  const suggestionMessage = (\n    extension: (typeof fileExtensions)[number],\n    to: 'src/' | '',\n    from: 'src/app/' | 'app/' | '',\n  ) =>\n    `Clerk: clerkMiddleware() was not run, your middleware file might be misplaced. Move your middleware file to ./${to}middleware.${extension}. Currently located at ./${from}middleware.${extension}`;\n\n  const { existsSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n\n  const projectWithAppSrcPath = path.join(cwd(), 'src', 'app');\n  const projectWithAppPath = path.join(cwd(), 'app');\n\n  const checkMiddlewareLocation = (\n    basePath: string,\n    to: 'src/' | '',\n    from: 'src/app/' | 'app/' | '',\n  ): string | undefined => {\n    for (const fileExtension of fileExtensions) {\n      if (existsSync(path.join(basePath, `middleware.${fileExtension}`))) {\n        return suggestionMessage(fileExtension, to, from);\n      }\n    }\n    return undefined;\n  };\n\n  if (existsSync(projectWithAppSrcPath)) {\n    return (\n      checkMiddlewareLocation(projectWithAppSrcPath, 'src/', 'src/app/') || checkMiddlewareLocation(cwd(), 'src/', '')\n    );\n  }\n\n  if (existsSync(projectWithAppPath)) {\n    return checkMiddlewareLocation(projectWithAppPath, '', 'app/');\n  }\n\n  return undefined;\n}\n\nexport { suggestMiddlewareLocation, hasSrcAppDir };\n"], "mappings": ";AAAA,SAAS,gBAAgB,eAAe,uBAAuB;AAE/D,SAAS,eAAe;AACtB,QAAM,EAAE,WAAW,IAAI,cAAc;AACrC,QAAM,OAAO,gBAAgB;AAC7B,QAAM,MAAM,eAAe;AAE3B,QAAM,oBAAoB,KAAK,KAAK,IAAI,GAAG,OAAO,KAAK;AAEvD,SAAO,CAAC,CAAC,WAAW,iBAAiB;AACvC;AAEA,SAAS,4BAA4B;AACnC,QAAM,iBAAiB,CAAC,MAAM,IAAI;AAClC,QAAM,oBAAoB,CACxB,WACA,IACA,SAEA,iHAAiH,EAAE,cAAc,SAAS,4BAA4B,IAAI,cAAc,SAAS;AAEnM,QAAM,EAAE,WAAW,IAAI,cAAc;AACrC,QAAM,OAAO,gBAAgB;AAC7B,QAAM,MAAM,eAAe;AAE3B,QAAM,wBAAwB,KAAK,KAAK,IAAI,GAAG,OAAO,KAAK;AAC3D,QAAM,qBAAqB,KAAK,KAAK,IAAI,GAAG,KAAK;AAEjD,QAAM,0BAA0B,CAC9B,UACA,IACA,SACuB;AACvB,eAAW,iBAAiB,gBAAgB;AAC1C,UAAI,WAAW,KAAK,KAAK,UAAU,cAAc,aAAa,EAAE,CAAC,GAAG;AAClE,eAAO,kBAAkB,eAAe,IAAI,IAAI;AAAA,MAClD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,qBAAqB,GAAG;AACrC,WACE,wBAAwB,uBAAuB,QAAQ,UAAU,KAAK,wBAAwB,IAAI,GAAG,QAAQ,EAAE;AAAA,EAEnH;AAEA,MAAI,WAAW,kBAAkB,GAAG;AAClC,WAAO,wBAAwB,oBAAoB,IAAI,MAAM;AAAA,EAC/D;AAEA,SAAO;AACT;", "names": []}