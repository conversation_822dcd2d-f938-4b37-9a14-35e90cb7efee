{"version": 3, "sources": ["../../../src/pages/ClerkProvider.tsx"], "sourcesContent": ["import { Clerk<PERSON>rovider as ReactClerkProvider } from '@clerk/clerk-react';\n// Override Clerk React error thrower to show that errors come from @clerk/nextjs\nimport { setClerkJsLoadingErrorPackageName, setErrorThrowerOptions } from '@clerk/clerk-react/internal';\nimport { useRouter } from 'next/router';\nimport React from 'react';\n\nimport { useSafeLayoutEffect } from '../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider } from '../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../types';\nimport { ClerkJSScript } from '../utils/clerk-js-script';\nimport { invalidateNextRouterCache } from '../utils/invalidateNextRouterCache';\nimport { mergeNextClerkPropsWithEnv } from '../utils/mergeNextClerkPropsWithEnv';\nimport { removeBasePath } from '../utils/removeBasePath';\nimport { RouterTelemetry } from '../utils/router-telemetry';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n\nexport function ClerkProvider({ children, ...props }: NextClerkProviderProps): JSX.Element {\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true } = props;\n  const { push, replace } = useRouter();\n  ReactClerkProvider.displayName = 'ReactClerkProvider';\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = invalidateNextRouterCache;\n  }, []);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onAfterSetActive = () => {\n      // Re-run the middleware every time there auth state changes.\n      // This enables complete control from a centralised place (NextJS middleware),\n      // as we will invoke it every time the client-side auth state changes, eg: signing-out, switching orgs, etc.\\\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        void push(window.location.href);\n      }\n    };\n  }, []);\n\n  const navigate = (to: string) => push(removeBasePath(to));\n  const replaceNavigate = (to: string) => replace(removeBasePath(to));\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    routerPush: navigate,\n    routerReplace: replaceNavigate,\n  });\n  // ClerkProvider automatically injects __clerk_ssr_state\n  // getAuth returns a user-facing authServerSideProps that hides __clerk_ssr_state\n  // @ts-expect-error initialState is hidden from the types as it's a private prop\n  const initialState = props.authServerSideProps?.__clerk_ssr_state || props.__clerk_ssr_state;\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider\n        {...mergedProps}\n        initialState={initialState}\n      >\n        <RouterTelemetry />\n        <ClerkJSScript router='pages' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n}\n"], "mappings": ";AAAA,SAAS,iBAAiB,0BAA0B;AAEpD,SAAS,mCAAmC,8BAA8B;AAC1E,SAAS,iBAAiB;AAC1B,OAAO,WAAW;AAElB,SAAS,2BAA2B;AACpC,SAAS,gCAAgC;AAEzC,SAAS,qBAAqB;AAC9B,SAAS,iCAAiC;AAC1C,SAAS,kCAAkC;AAC3C,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAEhC,uBAAuB,EAAE,aAAa,gBAAa,CAAC;AACpD,kCAAkC,eAAY;AAEvC,SAAS,cAAc,EAAE,UAAU,GAAG,MAAM,GAAwC;AAlB3F;AAmBE,QAAM,EAAE,+CAA+C,KAAK,IAAI;AAChE,QAAM,EAAE,MAAM,QAAQ,IAAI,UAAU;AACpC,qBAAmB,cAAc;AAEjC,sBAAoB,MAAM;AACxB,WAAO,gCAAgC;AAAA,EACzC,GAAG,CAAC,CAAC;AAEL,sBAAoB,MAAM;AACxB,WAAO,+BAA+B,MAAM;AAI1C,UAAI,8CAA8C;AAChD,aAAK,KAAK,OAAO,SAAS,IAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,WAAW,CAAC,OAAe,KAAK,eAAe,EAAE,CAAC;AACxD,QAAM,kBAAkB,CAAC,OAAe,QAAQ,eAAe,EAAE,CAAC;AAClE,QAAM,cAAc,2BAA2B;AAAA,IAC7C,GAAG;AAAA,IACH,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB,CAAC;AAID,QAAM,iBAAe,WAAM,wBAAN,mBAA2B,sBAAqB,MAAM;AAE3E,SACE,oCAAC,4BAAyB,SAAS,eACjC;AAAA,IAAC;AAAA;AAAA,MACE,GAAG;AAAA,MACJ;AAAA;AAAA,IAEA,oCAAC,qBAAgB;AAAA,IACjB,oCAAC,iBAAc,QAAO,SAAQ;AAAA,IAC7B;AAAA,EACH,CACF;AAEJ;", "names": []}