{"version": 3, "file": "protect.d.ts", "sourceRoot": "", "sources": ["../../../src/server/protect.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AACjD,OAAO,KAAK,EACV,0BAA0B,EAC1B,0BAA0B,EAC1B,wBAAwB,EACxB,6BAA6B,EAC7B,WAAW,EACX,kBAAkB,EACnB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAkC,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpF,OAAO,KAAK,EACV,mCAAmC,EACnC,yCAAyC,EAGzC,+BAA+B,EAChC,MAAM,cAAc,CAAC;AAKtB,KAAK,kBAAkB,GAAG;IACxB;;OAEG;IACH,KAAK,CAAC,EAAE,0BAA0B,CAAC,cAAc,CAAC,CAAC;IACnD;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,CAAC,CAAC,SAAS,+BAA+B,EACxC,MAAM,CAAC,EAAE,yCAAyC,CAAC,CAAC,CAAC,EACrD,OAAO,CAAC,EAAE,kBAAkB,GAC3B,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAE/B;;;OAGG;IACH,CACE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,mCAAmC,KAAK,OAAO,EAC9D,OAAO,CAAC,EAAE,kBAAkB,GAC3B,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAE/B;;;OAGG;IACH,CAAC,CAAC,SAAS,SAAS,EAClB,OAAO,CAAC,EAAE,kBAAkB,GAAG;QAAE,KAAK,EAAE,CAAC,CAAA;KAAE,GAC1C,OAAO,CAAC,wBAAwB,CAAC,CAAC,EAAE,kBAAkB,EAAE,0BAA0B,CAAC,CAAC,CAAC;IAExF;;;OAGG;IACH,CAAC,CAAC,SAAS,SAAS,EAAE,EACpB,OAAO,CAAC,EAAE,kBAAkB,GAAG;QAAE,KAAK,EAAE,CAAC,CAAA;KAAE,GAC1C,OAAO,CAAC,6BAA6B,CAAC,CAAC,EAAE,kBAAkB,EAAE,0BAA0B,CAAC,CAAC,CAAC;IAE7F;;;OAGG;IACH,CAAC,OAAO,CAAC,EAAE,kBAAkB,GAAG;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,GAAG,OAAO,CAAC,kBAAkB,GAAG,0BAA0B,CAAC,CAAC;IAE5G;;;OAGG;IACH,CAAC,OAAO,CAAC,EAAE,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;CAC7D;AAED,wBAAgB,aAAa,CAAC,IAAI,EAAE;IAClC,OAAO,EAAE,OAAO,CAAC;IACjB,UAAU,EAAE,UAAU,CAAC;IACvB;;;;OAIG;IACH,QAAQ,EAAE,MAAM,KAAK,CAAC;IACtB;;OAEG;IACH,QAAQ,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IAChC;;OAEG;IACH,YAAY,EAAE,MAAM,IAAI,CAAC;IACzB;;;;OAIG;IACH,gBAAgB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;CACxC,GAAG,WAAW,CAqFd"}