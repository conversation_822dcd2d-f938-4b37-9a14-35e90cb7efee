export declare const missingDomainAndProxy = "\nMissing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.\n\n1) With middleware\n   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'\n   ";
export declare const missingSignInUrlInDev = "\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL\n\n1) With middleware\n   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'";
export declare const getAuthAuthHeaderMissing: () => string;
export declare const authAuthHeaderMissing: (helperName?: string, prefixSteps?: string[]) => string;
export declare const authSignatureInvalid = "Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)";
export declare const encryptionKeyInvalid = "Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)";
export declare const encryptionKeyInvalidDev = "Clerk: Unable to decrypt request data.\n\nRefresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.\n\nFor more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)";
//# sourceMappingURL=errors.d.ts.map