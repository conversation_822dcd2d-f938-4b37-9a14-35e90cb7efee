{"version": 3, "file": "nextErrors.d.ts", "sourceRoot": "", "sources": ["../../../src/server/nextErrors.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,QAAA,MAAM,kBAAkB;;;;CAIvB,CAAC;AAEF;;;GAGG;AACH,QAAA,MAAM,2BAA2B,mBAAmB,CAAC;AAErD,KAAK,mBAAmB,GAAG,KAAK,GAAG;IACjC,MAAM,EAAE,OAAO,2BAA2B,CAAC;CAC5C,CAAC;AAEF;;GAEG;AACH,iBAAS,2BAA2B,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,mBAAmB,CAMjF;AAUD,eAAO,MAAM,8BAA8B,6BAA6B,CAAC;AAEzE,MAAM,MAAM,uBAAuB,GAAG,KAAK,GAAG;IAC5C,MAAM,EAAE,GAAG,OAAO,8BAA8B,IAAI,MAAM,EAAE,CAAC;CAC9D,CAAC;AAEF,wBAAgB,yBAAyB,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,uBAAuB,CAO1F;AAED,wBAAgB,4BAA4B,CAAC,KAAK,EAAE,OAAO,GAAG,MAAM,GAAG,SAAS,CAO/E;AAED,iBAAS,qBAAqB,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,mBAAmB,GAAG,uBAAuB,CAMrG;AAED;;;GAGG;AAEH,QAAA,MAAM,mBAAmB,kBAAkB,CAAC;AAE5C,KAAK,aAAa,CAAC,CAAC,GAAG,OAAO,IAAI,KAAK,GAAG;IACxC,MAAM,EAAE,GAAG,OAAO,mBAAmB,IAAI,SAAS,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC;IACvE,YAAY,EAAE,OAAO,kBAAkB,CAAC,eAAe,GAAG,OAAO,kBAAkB,CAAC,mBAAmB,CAAC;CACzG,GAAG,CAAC,CAAC;AAEN,iBAAS,mBAAmB,CAC1B,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC9B,IAAI,GAAE,SAAqB,EAC3B,UAAU,GAAE,GAAS,GACpB,KAAK,CAMP;AAMD,iBAAS,qBAAqB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,CAKtF;AAED,iBAAS,qBAAqB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,CAKtF;AAED;;;;;;GAMG;AACH,iBAAS,qBAAqB,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAAC;IAAE,WAAW,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,CAAC,CAmBpG;AAED,iBAAS,uBAAuB,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAAC;IAAE,aAAa,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,CAAC,CAMxG;AAED,iBAAS,uBAAuB,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,aAAa,CAAC;IAAE,aAAa,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,CAAC,CAMxG;AAED,iBAAS,yBAAyB,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,uBAAuB,CAEnF;AAED;;;GAGG;AACH,iBAAS,YAAY,IAAI,KAAK,CAI7B;AAED,OAAO,EACL,qBAAqB,EACrB,2BAA2B,EAC3B,qBAAqB,EACrB,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,EACrB,uBAAuB,EACvB,uBAAuB,EACvB,yBAAyB,EACzB,YAAY,GACb,CAAC"}