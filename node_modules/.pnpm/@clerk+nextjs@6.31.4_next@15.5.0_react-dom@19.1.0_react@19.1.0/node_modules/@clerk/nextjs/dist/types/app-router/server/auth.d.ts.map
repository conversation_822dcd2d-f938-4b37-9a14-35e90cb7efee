{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../../../src/app-router/server/auth.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,gBAAgB,CAAC;AAC/G,OAAO,KAAK,EACV,0BAA0B,EAC1B,wBAAwB,EACxB,6BAA6B,EAC7B,WAAW,EACX,gBAAgB,EACjB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAiD,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACnG,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAY,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAOrD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAMxD;;GAEG;AACH,KAAK,uBAAuB,CAAC,SAAS,IAAI,iBAAiB,GAAG;IAC5D;;;;;;;OAOG;IACH,gBAAgB,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;IAEzC;;;;;;;OAOG;IACH,gBAAgB,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;CAC1C,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG,qBAAqB,GAAG;IAAE,YAAY,CAAC,EAAE,0BAA0B,CAAC,cAAc,CAAC,CAAA;CAAE,CAAC;AAEhH,MAAM,WAAW,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC,OAAO,QAAQ,CAAC;IAC7D;;;OAGG;IACH,CAAC,CAAC,SAAS,SAAS,EAAE,EACpB,OAAO,EAAE,WAAW,GAAG;QAAE,YAAY,EAAE,CAAC,CAAA;KAAE,GACzC,OAAO,CACN,6BAA6B,CAC3B,CAAC,EACD,uBAAuB,CAAC,SAAS,CAAC,EAClC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC,CACxD,GACD,sBAAsB,CACzB,CAAC;IAEF;;;OAGG;IACH,CAAC,CAAC,SAAS,SAAS,EAClB,OAAO,EAAE,WAAW,GAAG;QAAE,YAAY,EAAE,CAAC,CAAA;KAAE,GACzC,OAAO,CACR,wBAAwB,CAAC,CAAC,EAAE,uBAAuB,CAAC,SAAS,CAAC,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CACjH,CAAC;IAEF;;;OAGG;IACH,CAAC,OAAO,EAAE,WAAW,GAAG;QAAE,YAAY,EAAE,KAAK,CAAA;KAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IAEtE;;;OAGG;IACH,CAAC,OAAO,CAAC,EAAE,qBAAqB,GAAG,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;IAE/E;;;;;;;;;;;;;;;;;OAiBG;IACH,OAAO,EAAE,WAAW,CAAC;CACtB;AAED;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,MAsEP,CAAC"}