/**
 * Clerk's identifiers that are used alongside the ones from Next.js
 */
declare const CONTROL_FLOW_ERROR: {
    REDIRECT_TO_URL: string;
    REDIRECT_TO_SIGN_IN: string;
    REDIRECT_TO_SIGN_UP: string;
};
/**
 * In-house implementation of `notFound()`
 * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/not-found.ts
 */
declare const LEGACY_NOT_FOUND_ERROR_CODE = "NEXT_NOT_FOUND";
type LegacyNotFoundError = Error & {
    digest: typeof LEGACY_NOT_FOUND_ERROR_CODE;
};
/**
 * Checks for the error thrown from `notFound()` for versions <= next@15.0.4
 */
declare function isLegacyNextjsNotFoundError(error: unknown): error is LegacyNotFoundError;
export declare const HTTP_ERROR_FALLBACK_ERROR_CODE = "NEXT_HTTP_ERROR_FALLBACK";
export type HTTPAccessFallbackError = Error & {
    digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`;
};
export declare function isHTTPAccessFallbackError(error: unknown): error is HTTPAccessFallbackError;
export declare function whichHTTPAccessFallbackError(error: unknown): number | undefined;
declare function isNextjsNotFoundError(error: unknown): error is LegacyNotFoundError | HTTPAccessFallbackError;
/**
 * In-house implementation of `redirect()` extended with a `clerk_digest` property
 * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/redirect.ts
 */
declare const REDIRECT_ERROR_CODE = "NEXT_REDIRECT";
type RedirectError<T = unknown> = Error & {
    digest: `${typeof REDIRECT_ERROR_CODE};${'replace'};${string};${307};`;
    clerk_digest: typeof CONTROL_FLOW_ERROR.REDIRECT_TO_URL | typeof CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;
} & T;
declare function nextjsRedirectError(url: string, extra: Record<string, unknown>, type?: 'replace', statusCode?: 307): never;
declare function redirectToSignInError(url: string, returnBackUrl?: string | URL | null): never;
declare function redirectToSignUpError(url: string, returnBackUrl?: string | URL | null): never;
/**
 * Checks an error to determine if it's an error generated by the
 * `redirect(url)` helper.
 *
 * @param error the error that may reference a redirect error
 * @returns true if the error is a redirect error
 */
declare function isNextjsRedirectError(error: unknown): error is RedirectError<{
    redirectUrl: string | URL;
}>;
declare function isRedirectToSignInError(error: unknown): error is RedirectError<{
    returnBackUrl: string | URL;
}>;
declare function isRedirectToSignUpError(error: unknown): error is RedirectError<{
    returnBackUrl: string | URL;
}>;
declare function isNextjsUnauthorizedError(error: unknown): error is HTTPAccessFallbackError;
/**
 * In-house implementation of experimental `unauthorized()`
 * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/unauthorized.ts
 */
declare function unauthorized(): never;
export { isNextjsNotFoundError, isLegacyNextjsNotFoundError, redirectToSignInError, redirectToSignUpError, nextjsRedirectError, isNextjsRedirectError, isRedirectToSignInError, isRedirectToSignUpError, isNextjsUnauthorizedError, unauthorized, };
//# sourceMappingURL=nextErrors.d.ts.map