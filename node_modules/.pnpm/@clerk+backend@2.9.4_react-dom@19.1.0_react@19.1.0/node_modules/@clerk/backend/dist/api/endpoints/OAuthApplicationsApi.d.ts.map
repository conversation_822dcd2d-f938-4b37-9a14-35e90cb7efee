{"version": 3, "file": "OAuthApplicationsApi.d.ts", "sourceRoot": "", "sources": ["../../../src/api/endpoints/OAuthApplicationsApi.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,cAAc,CAAC;AAG3D,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAClD,OAAO,KAAK,EAAE,yBAAyB,EAAE,MAAM,2BAA2B,CAAC;AAC3E,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAI7C,KAAK,4BAA4B,GAAG;IAClC;;;;OAIG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,YAAY,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CACrC,CAAC;AAEF,KAAK,4BAA4B,GAAG,4BAA4B,GAAG;IACjE;;OAEG;IACH,kBAAkB,EAAE,MAAM,CAAC;CAC5B,CAAC;AAEF,KAAK,6BAA6B,GAAG,sBAAsB,CAAC;IAC1D;;;OAGG;IACH,OAAO,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;CAC3C,CAAC,CAAC;AAEH,qBAAa,oBAAqB,SAAQ,WAAW;IACtC,IAAI,CAAC,MAAM,GAAE,6BAAkC;IAQ/C,GAAG,CAAC,kBAAkB,EAAE,MAAM;IAS9B,MAAM,CAAC,MAAM,EAAE,4BAA4B;IAQ3C,MAAM,CAAC,MAAM,EAAE,4BAA4B;IAY3C,MAAM,CAAC,kBAAkB,EAAE,MAAM;IASjC,YAAY,CAAC,kBAAkB,EAAE,MAAM;CAQrD"}